/**
 * 防抖函数，用于限制函数的执行频率
 * @param {Function} fn 需要防抖的函数
 * @param {Number} delay 延迟时间，单位毫秒
 * @returns {Function} 防抖后的函数
 */
export const debounce = (fn, delay) => {
  let timer = null;
  return function() {
    const context = this;
    const args = arguments;
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(context, args);
    }, delay);
  };
};

/**
 * 获取状态栏和标题栏高度
 * @returns {{statusBarHeight: number, titleBarHeight: number}} 状态栏和标题栏高度
 */
export const getNavigationBarInfo = () => {
  try {
    const sysInfo = uni.getSystemInfoSync();
    const statusBarHeight = sysInfo.statusBarHeight;
    let titleBarHeight = 44; // 默认标题栏高度

    // #ifdef MP-WEIXIN
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    // 胶囊按钮高度 + 上下间距
    const padding = (menuButtonInfo.top - sysInfo.statusBarHeight);
    titleBarHeight = menuButtonInfo.height + padding * 2;
    // #endif

    return {
      statusBarHeight,
      titleBarHeight
    };
  } catch (error) {
    console.error('获取系统信息失败:', error);
    // 返回默认值
    return {
      statusBarHeight: 20,
      titleBarHeight: 44
    };
  }
};

// 格式化日期
export const formatDate = (timestamp) => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 格式化日期，只显示年月日
export const formatDateYMD = (timestamp) => {
  if (!timestamp) return ''

  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}
