/**
 * 作文批改相关工具函数
 */

/**
 * 处理作文批改回调结果
 * @param {Object} callbackData - 回调数据
 * @param {string} callbackData.itemId - 任务项ID
 * @param {Object} callbackData.output - 批改结果
 * @param {string} callbackData.output.student - 学生姓名
 * @param {string} callbackData.output.score - 得分
 * @param {string} callbackData.output.rubricTotalScore - 总分
 * @param {string} callbackData.output.improvementSuggestions - 改进建议
 * @returns {Object} 处理后的数据
 */
export function processEssayCallbackResult(callbackData) {
  try {
    const { itemId, output } = callbackData;
    
    if (!itemId || !output) {
      throw new Error('回调数据格式不正确');
    }

    const {
      student,
      score,
      rubricTotalScore,
      improvementSuggestions
    } = output;

    // 验证必要字段
    if (!student || score === undefined || !rubricTotalScore) {
      throw new Error('回调数据缺少必要字段');
    }

    // 转换数据类型
    const processedData = {
      itemId: parseInt(itemId),
      studentName: student.trim(),
      score: parseInt(score),
      totalScore: parseInt(rubricTotalScore),
      suggestions: improvementSuggestions || '',
      // 计算正确率
      correctRate: Math.round((parseInt(score) / parseInt(rubricTotalScore)) * 100)
    };

    return {
      success: true,
      data: processedData
    };
  } catch (error) {
    console.error('处理作文批改回调结果失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 验证作文批改回调数据格式
 * @param {Object} data - 回调数据
 * @returns {Object} 验证结果
 */
export function validateEssayCallbackData(data) {
  const errors = [];

  // 检查基本结构
  if (!data || typeof data !== 'object') {
    errors.push('数据格式不正确');
    return { valid: false, errors };
  }

  // 检查itemId
  if (!data.itemId) {
    errors.push('缺少itemId字段');
  } else if (isNaN(parseInt(data.itemId))) {
    errors.push('itemId必须是数字');
  }

  // 检查output
  if (!data.output || typeof data.output !== 'object') {
    errors.push('缺少output字段或格式不正确');
  } else {
    const { student, score, rubricTotalScore } = data.output;

    if (!student || typeof student !== 'string') {
      errors.push('output.student字段缺失或格式不正确');
    }

    if (score === undefined || isNaN(parseInt(score))) {
      errors.push('output.score字段缺失或格式不正确');
    }

    if (!rubricTotalScore || isNaN(parseInt(rubricTotalScore))) {
      errors.push('output.rubricTotalScore字段缺失或格式不正确');
    }

    // 检查分数合理性
    if (score !== undefined && rubricTotalScore !== undefined) {
      const scoreNum = parseInt(score);
      const totalNum = parseInt(rubricTotalScore);
      
      if (scoreNum < 0 || scoreNum > totalNum) {
        errors.push('得分不能小于0或大于总分');
      }
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 格式化作文批改结果用于显示
 * @param {Object} result - 批改结果
 * @returns {Object} 格式化后的结果
 */
export function formatEssayResult(result) {
  if (!result) return null;

  return {
    studentName: result.studentName || '未知学生',
    score: `${result.score || 0}/${result.totalScore || 100}`,
    correctRate: `${result.correctRate || 0}%`,
    suggestions: result.suggestions || '暂无建议',
    // 根据得分率判断等级
    level: getScoreLevel(result.correctRate || 0)
  };
}

/**
 * 根据得分率获取等级
 * @param {number} correctRate - 正确率
 * @returns {string} 等级
 */
function getScoreLevel(correctRate) {
  if (correctRate >= 90) return '优秀';
  if (correctRate >= 80) return '良好';
  if (correctRate >= 70) return '中等';
  if (correctRate >= 60) return '及格';
  return '不及格';
}

/**
 * 创建作文批改任务的默认配置
 * @param {Object} params - 参数
 * @returns {Object} 任务配置
 */
export function createEssayTaskConfig(params) {
  const {
    title,
    teacherId,
    classId,
    rubricId,
    essayTitle
  } = params;

  return {
    title: title || `作文批改任务_${new Date().toLocaleDateString()}`,
    teacherId,
    classId,
    correctionParsedText: essayTitle || '',
    rubricId,
    taskType: 3, // 作文批改类型
    correctionImage: '',
    correctionType: 'essay'
  };
}
