import { defineStore } from 'pinia'

export const useFileStore = defineStore('file', {
  state: () => ({
    uploadedFiles: [],
    fileCount: 0,
    maxFileCount: 0
  }),

  actions: {
    addFile(file) {
      this.uploadedFiles.push(file)
      this.fileCount++
    },
    deleteFile(index) {
      this.uploadedFiles.splice(index, 1)
      this.fileCount--
    },
    clearFiles() {
      this.uploadedFiles = []
      this.fileCount = 0
    }
  },

  getters: {
    getUploadedFiles: (state) => state.uploadedFiles,
    getFileCount: (state) => state.fileCount,
    getMaxFileCount: (state) => state.maxFileCount
  }
})
