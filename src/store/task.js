import { defineStore } from 'pinia';

export const useTaskStore = defineStore('task', {
  state: () => ({
    currentTask: {
      taskId: null,
      teacherId: null,
      classId: null,
      className: '',
      title: '',
      correctionImage: ''
    }
  }),

  getters: {
    getCurrentTask: (state) => state.currentTask,
    getTaskId: (state) => state.currentTask.taskId,
    getTeacherId: (state) => state.currentTask.teacherId,
    getClassId: (state) => state.currentTask.classId,
    getClassName: (state) => state.currentTask.className,
    getTitle: (state) => state.currentTask.title
  },

  actions: {
    setCurrentTask(taskInfo) {
      this.currentTask = {
        ...this.currentTask,
        ...taskInfo
      };
    },
  },
});
