import develop from '@/config/develop'
import release from '@/config/release'
import trial from '@/config/trial'

const envs = {
  develop,
  trial,
  release,
}

const env = uni.getAccountInfoSync().miniProgram.envVersion

const config = envs[env]

export default {
  ...config,
  env,
  // 微信小程序 appid 用于故障反馈接口
  wechatMiniAppid: 'wx78ba0b57ce58ccee',
  // 支付宝小程序appid，用于故障反馈接口
  alipayMiniAppid: '****************',
}
