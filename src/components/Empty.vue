<template>
  <view class="empty-data">
    <image class="empty-icon" src="/static/icons/empty.svg" mode="aspectFit"></image>
    <text class="empty-text">{{ text }}</text>
  </view>
</template>
<script setup>
const props = defineProps({
  text: {
    type: String,
    default: '暂无记录'
  }
});
</script>

<style scoped lang="scss">
.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .empty-icon {
    width: 160rpx;
    height: 160rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
