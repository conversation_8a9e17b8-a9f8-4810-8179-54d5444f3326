<template>
  <view class="student-selector-container">
    <!-- 不再需要选择按钮，由父组件负责 -->

    <!-- 模态框背景遮罩 -->
    <view class="modal-mask" v-if="isVisible" @tap.stop="closeModal"></view>

    <!-- 模态框内容 -->
    <view class="modal-container" v-if="isVisible" @tap.stop>
      <view class="modal-header">
        <text class="modal-title"> {{ paper.student ? "修改学生":"选择学生" }}</text>
        <view class="close-btn" @tap.stop="closeModal">
          <uni-icons type="closeempty" size="20" color="#999999"></uni-icons>
        </view>
      </view>

      <!-- 搜索框 -->
      <view class="search-input-box">
        <uni-icons type="search" size="16" color="#999999"></uni-icons>
        <input
          class="search-input"
          type="text"
          v-model="keyword"
          placeholder="搜索学生"
          confirm-type="search"
          @input="handleSearchInput"
        />
      </view>

      <!-- 加载状态 -->
      <view class="loading-container" v-if="isLoading">
        <uni-icons type="refresh" size="20" color="#4285F4"></uni-icons>
        <text>正在搜索...</text>
      </view>

      <!-- 学生列表 -->
      <scroll-view scroll-y class="student-list" v-else>
        <view class="student-item empty-result" v-if="studentList.length === 0">
          <text>无匹配学生</text>
        </view>
        <view
          class="student-item"
          v-for="student in studentList"
          :key="student.id"
          @tap.stop="handleStudentSelect(student)"
        >
          <text>{{ student.name }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue';
import { getStudentsWithoutTaskItems } from '@/api/student';

// 定义组件属性
const props = defineProps({
  paper: {
    type: Object,
    required: true
  },
  classId: {
    type: [String, Number],
    default: ''
  },
  taskId: {
    type: [String, Number],
    default: ''
  }
});

// 定义组件事件
const emit = defineEmits(['select', 'visibleChange']);

// 组件状态
const studentList = ref([]);
const isLoading = ref(false);
const isVisible = ref(true);
const keyword = ref('');
const searchTimer = ref(null);

// 防抖时间（毫秒）
const DEBOUNCE_DELAY = 300;

/**
 * 获取学生列表数据
 * @param {string} searchKeyword - 搜索关键词
 */
const fetchStudents = async (searchKeyword = '') => {
  if (!isValidParams()) return;

  try {
    isLoading.value = true;

    const res = await getStudentsWithoutTaskItems({
      classId: props.classId,
      taskId:props.taskId,
      keyword: searchKeyword,
    });

    if (res?.data) {
      studentList.value = res.data;
    } else {
      resetStudentList();
      showErrorToast('获取学生列表失败');
    }
  } catch (error) {
    resetStudentList();
    showErrorToast('获取学生列表失败');
  } finally {
    isLoading.value = false;
  }
};

/**
 * 验证必要参数是否有效
 */
const isValidParams = () => {
  if (!props.classId || !props.taskId) {
    showErrorToast('班级或任务ID无效');
    return false;
  }
  return true;
};

/**
 * 显示错误提示
 */
const showErrorToast = (message) => {
  uni.showToast({
    title: message,
    icon: 'none'
  });
};

/**
 * 重置学生列表
 */
const resetStudentList = () => {
  studentList.value = [];
};

/**
 * 处理搜索输入变化（带防抖）
 */
const handleSearchInput = (e) => {
  const value = e.detail.value;
  keyword.value = value;

  if (searchTimer.value) {
    clearTimeout(searchTimer.value);
  }

  searchTimer.value = setTimeout(() => {
    fetchStudents(value);
  }, DEBOUNCE_DELAY);
};

/**
 * 处理学生选择
 */
const handleStudentSelect = (student) => {
  uni.showModal({
    title: '确认选择',
    content: `确定要${props.paper.studentId === -1 ? '选择' : '修改为'} ${student.name} 吗？`,
    success: (res) => {
      if (res.confirm) {
        emit('select', { paper: props.paper, student });
      }
    }
  });
};

/**
 * 关闭模态框
 */
const closeModal = () => {
  emit('visibleChange', { visible: false });
};

// 生命周期钩子
onMounted(() => {
  fetchStudents();
});

// 监听属性变化
watch([() => props.classId, () => props.taskId], () => {
  fetchStudents(keyword.value);
});

// 监听paper属性变化
watch(() => props.paper, (newPaper, oldPaper) => {
  if (newPaper && (!oldPaper || newPaper.id !== oldPaper.id)) {
    keyword.value = '';
    fetchStudents();
  }
}, { deep: true });
</script>

<style lang="scss" scoped>
.student-selector-container {
  position: relative;
}

// 移除不再需要的选择按钮样式
// .action-btn, .select-student-btn 样式已在父组件中定义

// 模态框背景遮罩
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s;
}

// 模态框容器
.modal-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  animation: modal-in 0.3s ease;
}

@keyframes modal-in {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid #F5F5F5;
}

.modal-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  transition: background-color 0.2s;

  &:active {
    background-color: #F5F5F5;
  }
}

.search-input-box {
  display: flex;
  align-items: center;
  margin: 24rpx;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 16rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
  padding-left: 8rpx;
}

.student-list {
  flex: 1;
  max-height: 600rpx;
  padding: 0 24rpx;
}

.student-item {
  padding: 24rpx 16rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #F5F5F5;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #F8F9FA;
  }
}

.empty-result {
  color: #999;
  text-align: center;
  padding: 40rpx 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #4285F4;
  font-size: 26rpx;

  .uni-icons {
    margin-bottom: 16rpx;
    animation: spin 1.2s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
