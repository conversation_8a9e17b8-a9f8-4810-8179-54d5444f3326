<template>
  <view class="custom-nav-bar">
    <view class="nav-content">
      <view class="nav-left" @click="handleBack" v-if="showBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="nav-center">
        <text class="nav-title">{{ title }}</text>
      </view>
      <view class="nav-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup>
import { getNavigationBarInfo } from '@/utils/tools.js'
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  showBack: {
    type: Boolean,
    default: true
  },
  backgroundColor: {
    type: String,
    default: '#3c77ef'
  },
  textColor: {
    type: String,
    default: '#ffffff'
  }
})

const emit = defineEmits(['back'])

// 计算顶部安全区域高度
const { statusBarHeight, titleBarHeight } = getNavigationBarInfo()
const headerHeight = computed(() => statusBarHeight + titleBarHeight)

const handleBack = () => {
  emit('back')
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: v-bind('backgroundColor');
  padding-top: v-bind('statusBarHeight + "px"');
}

.nav-content {
  height: v-bind('titleBarHeight + "px"');
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  position: relative;
}

.nav-left {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 60rpx;
  
  .back-icon {
    font-size: 40rpx;
    color: v-bind('textColor');
    font-weight: bold;
  }
}

.nav-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  
  .nav-title {
    color: v-bind('textColor');
    font-size: 32rpx;
    font-weight: 600;
  }
}

.nav-right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 60rpx;
}
</style>
