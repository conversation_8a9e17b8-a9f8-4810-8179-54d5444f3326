<template>
  <!-- 步骤指示器 -->
  <view class="header">
    <view class="steps">
      <view
          v-for="step in 2"
          :key="step"
          :class="['step', { active: step === props.activeStep }]"
      >
        <view class="step-number">{{ step }}</view>
        <view class="step-text">{{ step === 1 ? '配置试题' : '上传作业并批改' }}</view>
      </view>
    </view>
  </view>
</template>
<script setup>
// 定义 props，接收当前激活的步骤索引（从1开始）
const props = defineProps({
  activeStep: {
    type: Number,
    default: 1,
    validator: (value) => value >= 1 && value <= 2
  }
})
</script>

<style scoped lang="scss">
// 步骤指示器
.header {
  background-color: $uni-primary-color;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  margin-top: 0;
  padding-top: 0;

  .steps {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 24rpx 0;

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: rgba(255, 255, 255, 0.7);

      .step-number {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.3);
        color: #fff;
        font-size: 28rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 8rpx;
      }

      .step-text {
        font-size: 24rpx;
      }

      &.active {
        color: #fff;

        .step-number {
          background-color: #fff;
          color: $uni-primary-color;
        }
      }
    }
  }
}
</style>
