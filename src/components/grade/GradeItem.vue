<template>
  <view class="card-content">
    <view
        class="record-item"
        @click="onClick"
    >
      <view v-if="item.correctionImage" class="preview-btn" @click.stop="previewImage(item.correctionImage)">
        <uni-icons type="eye" size="18" color="#999"></uni-icons>
      </view>
      <view v-else-if="item.taskType === 3" class="preview-btn" @click.stop="showFullTitle">
        <uni-icons type="eye" size="18" color="#999"></uni-icons>
      </view>
      <view class="record-content">
        <text class="record-title">{{ item.title}}</text>
        <text class="record-date">{{ formatDate(item.createTime) }}</text>
      </view>
      <view class="record-score">
        <text>{{ item.taskType === 3 ? '平均分数' : '平均正确率' }}</text>
        <text class="score-value">{{ item.averageAccuracy }} <text v-if="item.taskType !==3 ">%</text> </text>
      </view>
      <view v-if="showActions" class="more-btn" @click.stop="showActionSheet">
        <uni-icons type="more-filled" size="18" color="#999"></uni-icons>
      </view>
    </view>

    <!-- 文本显示弹窗 -->
    <TextDisplayModal
      v-model:visible="showTextModal"
      title="作文要求"
      :content="item.correctionParsedText"
      confirm-text="确定"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { formatDate } from "@/utils/tools";
import TextDisplayModal from '@/components/TextDisplayModal.vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  showActions: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['click', 'delete', 'rename']);

// 控制文本显示弹窗
const showTextModal = ref(false);

// 显示完整标题
const showFullTitle = () => {
  showTextModal.value = true;
};

const onClick = () => {
  emit('click', props.item);
};

// 预览图片
const previewImage = (url) => {
  uni.previewImage({
    urls: [url],
    current: url
  });
};

// 显示操作菜单
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ['重命名', '删除'],
    itemColor: '#333333',
    success: function (res) {
      switch (res.tapIndex) {
        case 0:
          handleRename();
          break;
        case 1:
          handleDelete();
          break;
      }
    }
  });
};

// 处理重命名
const handleRename = () => {
  emit('rename', props.item);
};

// 处理删除
const handleDelete = () => {
  emit('delete', props.item.id);
};
</script>

<style lang="scss" scoped>
.card-content {
  padding: 0 20rpx;
}

.record-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;

  .preview-btn {
    padding: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;
    border-radius: 8rpx;
    height: 60rpx;
    width: 60rpx;
  }

  .record-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 20rpx;

    .record-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .record-date {
      font-size: 24rpx;
      color: #999;
    }
  }

  .record-score {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 20rpx;

    text {
      font-size: 24rpx;
      color: #999;
    }

    .score-value {
      font-size: 32rpx;
      color: $uni-primary-color;
    }
  }

  .more-btn {
    padding: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20rpx;
    height: 60rpx;
    width: 60rpx;
    opacity: 0.7;
  }
}
</style>
