<template>
    <view v-if="visible" class="loading-mask">
      <view class="loading-content">
        <view class="loading-spinner">
          <uni-icons type="spinner-cycle" size="30" color="#3a86ff"></uni-icons>
        </view>
        <text v-if="text" class="loading-text">{{ text }}</text>
      </view>
    </view>
  </template>

  <script setup>
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: '加载中...'
    }
  });
  </script>

  <style lang="scss" scoped>
  .loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100vh;
    background-color: #ffffff; /* Changed to solid white background */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;

      .loading-spinner {
        margin-bottom: 10px;
        animation: spin 1.5s linear infinite;
      }

      .loading-text {
        font-size: 14px;
        color: #333333;
      }
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  </style>
