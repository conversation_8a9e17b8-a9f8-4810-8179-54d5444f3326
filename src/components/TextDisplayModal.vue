<template>
  <view>
    <!-- 遮罩层 -->
    <view class="modal-mask" v-if="visible" @tap="handleMaskClick"></view>
    
    <!-- 弹窗内容 -->
    <view class="modal-container" v-if="visible">
      <view class="modal-content">
        <!-- 标题 -->
        <view class="modal-header">
          <text class="modal-title">{{ title }}</text>
          <view class="close-btn" @tap="handleClose">
            <uni-icons type="closeempty" size="20" color="#999"></uni-icons>
          </view>
        </view>
        
        <!-- 内容区域 -->
        <view class="modal-body">
          <scroll-view
            class="content-scroll"
            scroll-y
            :style="{ height: '400rpx' }"
          >
            <view class="content-container">
              <text class="content-text" user-select>{{ content }}</text>
            </view>
          </scroll-view>
        </view>
        
        <!-- 底部按钮 -->
        <view class="modal-footer">
          <button class="btn btn-confirm" @tap="handleClose">{{ confirmText }}</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 无需导入任何响应式API，因为都是通过props和emit处理

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '详情'
  },
  content: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  maxHeight: {
    type: Number,
    default: 800 // 最大高度，单位rpx
  }
});

const emit = defineEmits(['close', 'update:visible']);

// 处理遮罩点击
const handleMaskClick = () => {
  handleClose();
};

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};
</script>

<style lang="scss" scoped>
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 40rpx;
  box-sizing: border-box;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 32rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.close-btn {
  padding: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.content-scroll {
  width: 100%;
  height: 100%;
}

.content-container {
  padding: 32rpx;
}

.content-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  text-align: left;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  display: block;
}

.modal-footer {
  padding: 20rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  border: none;
  border-radius: 12rpx;
  background-color: #4285F4;
  color: #fff;
  font-weight: 600;
}

.btn::after {
  border: none;
}
</style>
