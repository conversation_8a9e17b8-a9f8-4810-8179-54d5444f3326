<?xml version="1.0" encoding="UTF-8"?>
<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆形 -->
  <circle cx="18" cy="18" r="17" fill="#F5F7FF" stroke="#3C77EF" stroke-width="2"/>
  
  <!-- 记录图标 -->
  <path d="M12 12H24V24H12V12Z" stroke="url(#gradient)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M16 16H20" stroke="url(#gradient)" stroke-width="2" stroke-linecap="round"/>
  <path d="M16 20H20" stroke="url(#gradient)" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="gradient" x1="12" y1="12" x2="24" y2="24" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#3C77EF"/>
      <stop offset="100%" stop-color="#6B9AFF"/>
    </linearGradient>
  </defs>
</svg> 