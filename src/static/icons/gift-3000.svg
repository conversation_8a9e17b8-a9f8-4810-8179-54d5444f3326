<?xml version="1.0" encoding="UTF-8"?>
<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 阴影效果 -->
  <rect x="7" y="17" width="46" height="36" rx="4" fill="#E65D5D"/>
  
  <!-- 主体礼盒 -->
  <rect x="5" y="15" width="50" height="40" rx="4" fill="#FF6B6B"/>
  
  <!-- 礼盒盖子 -->
  <rect x="15" y="5" width="30" height="10" rx="2" fill="#FF6B6B"/>
  <rect x="13" y="7" width="34" height="8" rx="2" fill="#E65D5D"/>
  
  <!-- 丝带 -->
  <rect x="27" y="5" width="6" height="50" fill="#FFD93D"/>
  <rect x="5" y="25" width="50" height="6" fill="#FFD93D"/>
  
  <!-- 数字背景 -->
  <rect x="12" y="32" width="36" height="16" rx="2" fill="#E65D5D"/>
  
  <!-- 数字文本 -->
  <text x="14" y="45" font-family="Arial" font-size="16" font-weight="bold" fill="#FFFFFF" filter="url(#shadow)">3000</text>
  
  <!-- 阴影滤镜 -->
  <defs>
    <filter id="shadow" x="-1" y="-1" width="200%" height="200%">
      <feDropShadow dx="0" dy="1" stdDeviation="0.5" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
</svg> 