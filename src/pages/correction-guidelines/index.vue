<template>
  <view class="guidelines-container">
    <view class="guidelines-content">
      <view class="section ai-limitation-examples-section">
        <view class="section-title">导致AI批改不准确的具体情况</view>
        <view class="section-content">
          <view class="ai-limitation-intro">
            <text>以下是一些导致AI批改结果不准确的具体情况：</text>
          </view>
          <view class="limitation-example-item">
            <uni-icons type="error" size="18" color="#e65100"></uni-icons>
            <view class="example-content">
              <text class="example-title">连笔字迹难辨认</text>
              <text class="example-desc">学生连笔写名字或单词时，字迹可能非常模糊，甚至连人类肉眼都很难分辨，AI更难以准确识别</text>
            </view>
          </view>
          <view class="limitation-example-item">
            <uni-icons type="error" size="18" color="#e65100"></uni-icons>
            <view class="example-content">
              <text class="example-title">相似字母混淆</text>
              <text class="example-desc">单词书写不清晰时，如n看起来像h，h看起来像n，m与w，i与j等形状相近的字母容易被误识别</text>
            </view>
          </view>
          <view class="limitation-example-item">
            <uni-icons type="error" size="18" color="#e65100"></uni-icons>
            <view class="example-content">
              <text class="example-title">涂改不彻底</text>
              <text class="example-desc">学生写错后没有完全涂改掉，或者只是简单划掉而不是完全涂黑，AI可能会误识别原来的错误内容或无法准确识别</text>
            </view>
          </view>

          <view class="limitation-example-item">
            <uni-icons type="error" size="18" color="#e65100"></uni-icons>
            <view class="example-content">
              <text class="example-title">空行太多，答案前没序号</text>
              <text class="example-desc">当学生答案之间有过多空行，或者答案前没有标明序号时，AI系统难以将题目和答案一一对应，可能导致批改结果不准确</text>
            </view>
          </view>

          <view class="limitation-example-item">
            <uni-icons type="error" size="18" color="#e65100"></uni-icons>
            <view class="example-content">
              <text class="example-title">学生答案序号和参考答案不对应</text>
              <text class="example-desc">当学生答案的序号与参考答案的序号不一致时，AI系统可能会将错误的答案与题目匹配，导致批改结果出现错误，影响正确率</text>
            </view>
          </view>

          <view class="limitation-example-item">
            <uni-icons type="error" size="18" color="#e65100"></uni-icons>
            <view class="example-content">
              <text class="example-title">答题卡格式不规范</text>
              <text class="example-desc">请尽量使用表格格式的答题卡，以便AI更好识别</text>
            </view>
          </view>

          <view class="ai-limitation-conclusion">
            <text>为确保批改准确率，请在使用前向学生强调：答案前必须有序号、保持字迹清晰、避免过多空行。当发现上述问题时，请务必手动复核并修正。您可以在批改详情页面中直接编辑AI识别或判断有误的内容，以确保最终结果的准确性</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 无需额外的脚本逻辑
</script>

<style lang="scss" scoped>
.guidelines-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 40rpx;
}

.guidelines-content {
  padding: 20rpx;
}

.section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 20rpx;
    border-left: 8rpx solid #3f7bfc;
    padding-left: 16rpx;
  }

  .section-content {
    padding: 10rpx 0;
  }
}

/* AI批改局限性部分样式 */
.ai-limitation-section, .ai-limitation-examples-section {
  background-color: #fff8e1;
  border: 2rpx solid #ffe082;
  border-left: 8rpx solid #ff9800;
}

.ai-limitation-section .section-title, .ai-limitation-examples-section .section-title {
  color: #e65100;
  border-left: 8rpx solid #ff9800;
}

.ai-limitation-intro {
  margin-bottom: 20rpx;

  text {
    font-size: 28rpx;
    color: #333333;
    line-height: 1.5;
  }
}

.limitation-item, .limitation-example-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  padding: 10rpx;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 8rpx;

  text {
    font-size: 28rpx;
    color: #333333;
    margin-left: 16rpx;
    line-height: 1.5;
    flex: 1;
  }
}

.limitation-example-item {
  margin-bottom: 20rpx;

  .example-content {
    flex: 1;
    margin-left: 16rpx;
  }

  .example-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #e65100;
    display: block;
    margin-bottom: 6rpx;
  }

  .example-desc {
    font-size: 26rpx;
    color: #333333;
    line-height: 1.5;
    display: block;
  }
}

.ai-limitation-conclusion {
  margin-top: 20rpx;
  padding: 16rpx;
  background-color: rgba(255, 152, 0, 0.1);
  border-radius: 8rpx;

  text {
    font-size: 28rpx;
    color: #e65100;
    font-weight: 500;
    line-height: 1.5;
  }
}

.requirement, .tip {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;

  text {
    font-size: 28rpx;
    color: #333333;
    margin-left: 16rpx;
  }
}

.example {
  margin-top: 20rpx;
  background-color: #f5f7fa;
  padding: 20rpx;
  border-radius: 8rpx;

  .example-title {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 10rpx;
  }

  .example-content {
    font-size: 28rpx;
    color: #333333;
    font-family: monospace;
  }
}

.faq-item {
  margin-bottom: 24rpx;

  .faq-question {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-bottom: 10rpx;
  }

  .faq-answer {
    font-size: 28rpx;
    color: #666666;
  }
}

/* 批改前比读样式 */
.important-section {
  background-color: #fff8f8;
  border: 2rpx solid #ffcdd2;
  border-left: 8rpx solid #ff6b6b;
}

.important-section .section-title {
  color: #e53935;
  border-left: 8rpx solid #e53935;
}

.important-tip {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  text {
    font-size: 30rpx;
    color: #e53935;
    font-weight: 500;
    margin-left: 16rpx;
  }
}

.important-content {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;

  text {
    font-size: 28rpx;
    color: #333333;
  }
}

.numbered-item {
  display: flex;
  margin: 12rpx 0 12rpx 20rpx;

  .number {
    font-weight: bold;
    margin-right: 10rpx;
    color: #e53935;
  }

  text {
    font-size: 28rpx;
  }
}

.important-note {
  font-size: 26rpx;
  color: #757575;
  font-style: italic;
  padding: 10rpx;
}
</style>
