<template>
  <view class="result-display">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">批改结果</text>
      <text class="page-subtitle">AI智能批改完成</text>
    </view>

    <!-- 学生信息 -->
    <view class="student-info">
      <view class="info-left">
        <view class="student-details">
          <view class="student-name">{{ result.student || '体验学生' }}</view>
          <view class="student-id">学号：DEMO001</view>
        </view>
      </view>
      <view class="info-right">
        <text class="exam-date">{{ currentDate }}</text>
      </view>
    </view>

    <!-- 成绩统计 -->
    <view class="score-stats">
      <view class="stat-item total-score">
        <text class="stat-value total">{{ result.correctRate }}%</text>
        <text class="stat-label">正确率</text>
      </view>
      <view class="stat-item correct-count">
        <text class="stat-value correct">{{ result.correctCount }}</text>
        <text class="stat-label">正确数</text>
      </view>
      <view class="stat-item wrong-count">
        <text class="stat-value wrong">{{ result.wrongCount }}</text>
        <text class="stat-label">错误数</text>
      </view>
    </view>

    <!-- 分析建议 -->
    <view class="analysis-section">
      <view class="analysis-item error-analysis">
        <text class="analysis-title">错题分析</text>
        <view class="analysis-content">
          <view class="content-box">
            <view class="list-content">
              <view class="list-item">
                <text class="item-text">{{ result.errorAnalysis }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="analysis-item improvement">
        <text class="analysis-title">改进建议</text>
        <view class="analysis-content">
          <view class="list-content">
            <view class="list-item">
              <text class="item-text">{{ result.improvementSuggestions }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 答题详情 -->
    <view class="answer-detail-section">
      <view class="section-header">
        <view class="section-title">答题详情</view>
      </view>
      <view class="answer-list">
        <view
          class="answer-item"
          v-for="(item, index) in result.answerList"
          :key="index"
        >
          <view class="answer-header">
            <text class="question-number">{{ index + 1 }}.</text>
            <text class="question-content">{{ item.question }}</text>
            <view class="result-badge" :class="{ 'correct': item.isCorrect, 'wrong': !item.isCorrect }">
              <text>{{ item.isCorrect ? '✓' : '✗' }}</text>
            </view>
          </view>
          <view class="answer-content">
            <view class="answer-row">
              <text class="label">标准答案：</text>
              <text class="value correct-answer">{{ item.standardAnswer }}</text>
            </view>
            <view class="answer-row">
              <text class="label">学生答案：</text>
              <text class="value student-answer" :class="{ 'wrong': !item.isCorrect }">{{ item.studentAnswer }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 图片对比区域 -->
    <view class="image-compare-section">
      <view class="section-title">图片对比</view>
      <view class="image-compare">
        <view class="compare-item">
          <text class="compare-label">参考答案</text>
          <image :src="answerImageUrl" mode="aspectFit" class="compare-image" @click="previewImage(answerImageUrl)"></image>
        </view>
        <view class="compare-item">
          <text class="compare-label">学生试卷</text>
          <image :src="paperImageUrl" mode="aspectFit" class="compare-image" @click="previewImage(paperImageUrl)"></image>
        </view>
      </view>
    </view>

    <!-- 重新体验按钮 -->
    <view class="restart-section">
      <button class="restart-btn" @click="handleRestart">
        <text>重新体验</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { formatDate } from '@/utils/tools'

// 定义 props
const props = defineProps({
  result: {
    type: Object,
    default: () => ({})
  },
  answerImageUrl: {
    type: String,
    default: ''
  },
  paperImageUrl: {
    type: String,
    default: ''
  }
})

// 定义 emits
const emit = defineEmits(['restart'])

// 当前日期
const currentDate = computed(() => {
  return formatDate(new Date())
})

// 预览图片
const previewImage = (url) => {
  uni.previewImage({
    urls: [url],
    current: url
  })
}

// 重新体验
const handleRestart = () => {
  emit('restart')
}
</script>

<style lang="scss" scoped>
.result-display {
  .page-header {
    text-align: center;
    padding: 40rpx 0;

    .page-title {
      display: block;
      font-size: 48rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 16rpx;
    }

    .page-subtitle {
      display: block;
      font-size: 28rpx;
      color: #666;
    }
  }

  .student-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background-color: #fff;
    padding: 30rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .info-left {
      .student-details {
        .student-name {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .student-id {
          font-size: 26rpx;
          color: #666;
        }
      }
    }

    .info-right {
      .exam-date {
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .score-stats {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 30rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;

      .stat-value {
        font-size: 50rpx;
        font-weight: 600;
        margin-bottom: 8rpx;

        &.total {
          color: #4285F4;
        }

        &.correct {
          color: #4CAF50;
        }

        &.wrong {
          color: #FF5252;
        }
      }

      .stat-label {
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .analysis-section {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .analysis-item {
      margin-bottom: 30rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .analysis-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 20rpx;
        display: block;
      }

      .analysis-content {
        .content-box {
          background-color: #FFF7F5;
          border-radius: 8rpx;
          padding: 20rpx;
        }

        .list-content {
          padding: 0 10rpx;

          .list-item {
            display: flex;
            margin-bottom: 16rpx;
            font-size: 28rpx;
            line-height: 1.5;

            &:last-child {
              margin-bottom: 0;
            }

            .item-text {
              color: #333;
              flex: 1;
            }
          }
        }
      }

      &.error-analysis {
        .content-box {
          background-color: #FFF7F5;
        }
      }

      &.improvement {
        .list-content {
          background-color: #F0F7FF;
          border-radius: 8rpx;
          padding: 20rpx;
        }
      }
    }
  }

  .answer-detail-section {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .section-header {
      margin-bottom: 20rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .answer-list {
      .answer-item {
        padding: 20rpx 0;
        border-bottom: 1px solid #F5F5F5;

        &:last-child {
          border-bottom: none;
        }

        .answer-header {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          .question-number {
            font-size: 28rpx;
            color: #666;
            margin-right: 8rpx;
            min-width: 40rpx;
          }

          .question-content {
            flex: 1;
            font-size: 28rpx;
            color: #333;
          }

          .result-badge {
            width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20rpx;
            font-weight: bold;

            &.correct {
              background-color: #4CAF50;
              color: #fff;
            }

            &.wrong {
              background-color: #FF5252;
              color: #fff;
            }
          }
        }

        .answer-content {
          margin-left: 48rpx;

          .answer-row {
            display: flex;
            margin-bottom: 8rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              font-size: 26rpx;
              color: #666;
              min-width: 120rpx;
            }

            .value {
              font-size: 26rpx;

              &.correct-answer {
                color: #4CAF50;
              }

              &.student-answer {
                color: #333;

                &.wrong {
                  color: #FF5252;
                }
              }
            }
          }
        }
      }
    }
  }

  .image-compare-section {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
      display: block;
    }

    .image-compare {
      display: flex;
      gap: 20rpx;

      .compare-item {
        flex: 1;
        text-align: center;

        .compare-label {
          display: block;
          font-size: 28rpx;
          color: #666;
          margin-bottom: 12rpx;
        }

        .compare-image {
          width: 100%;
          max-height: 300rpx;
          border-radius: 8rpx;
          border: 1px solid #E5E5E5;
        }
      }
    }
  }

  .restart-section {
    padding: 40rpx 0;

    .restart-btn {
      width: 100%;
      height: 88rpx;
      background-color: #fff;
      border: 2rpx solid #4285F4;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4285F4;
      font-size: 32rpx;

      &:active {
        opacity: 0.8;
        background-color: #F0F7FF;
      }
    }
  }
}
</style>
