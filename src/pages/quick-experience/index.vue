<template>
  <view class="quick-experience">
    <view class="content-container">
      <!-- 页面标题 -->
      <view class="page-header">
        <text class="page-title">快速体验批改</text>
        <text class="page-subtitle">体验AI智能批改效果</text>
      </view>

      <!-- 图片选择区域 -->
      <view class="image-selection-section" v-if="!showResult">
        <!-- 说明文字 -->
        <view class="instruction-text">
          <text>以下为一组对应的批改示例，可直接体验或替换图片</text>
        </view>

        <!-- 图片展示区域 -->
        <view class="images-container">
          <!-- 参考答案 -->
          <view class="image-item">
            <view class="image-header">
              <text class="image-title">参考答案</text>
              <view class="header-actions">
                <text class="action-btn" @click="resetAnswerImage">重置</text>
                <text class="action-btn" @click="uploadAnswerImage">更换</text>
              </view>
            </view>
            <view class="image-wrapper">
              <image :src="answerImageUrl" mode="aspectFit" class="display-image" @click="previewImage(answerImageUrl)"></image>
            </view>
          </view>

          <!-- 学生试卷 -->
          <view class="image-item">
            <view class="image-header">
              <text class="image-title">学生试卷</text>
              <view class="header-actions">
                <text class="action-btn" @click="resetPaperImage">重置</text>
                <text class="action-btn" @click="uploadPaperImage">更换</text>
              </view>
            </view>
            <view class="image-wrapper">
              <image :src="paperImageUrl" mode="aspectFit" class="display-image" @click="previewImage(paperImageUrl)"></image>
            </view>
          </view>
        </view>

        <!-- 开始批改按钮 -->
        <button
          class="start-correction-btn"
          :disabled="!canStartCorrection"
          :class="{ 'disabled': !canStartCorrection }"
          @click="startCorrection"
        >
          <text v-if="!isProcessing">开始AI批改</text>
          <view v-else class="processing-content">
            <view class="loading-spinner">
              <uni-icons type="refresh" size="20" color="#fff"></uni-icons>
            </view>
            <text v-if="countdown > 0">AI批改中... {{ countdown }}s</text>
            <text v-else>AI批改中...</text>
          </view>
        </button>
      </view>

      <!-- 批改结果展示区域 -->
      <CorrectionResult
        v-if="showResult"
        :result="correctionResult"
        :answer-image-url="answerImageUrl"
        :paper-image-url="paperImageUrl"
        @restart="restartExperience"
      />
    </view>

    <LoadingMask :show="isProcessing" />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import LoadingMask from '@/components/LoadingMask.vue'
import CorrectionResult from './components/CorrectionResult.vue'
import { correctByDoubao, setArKApiKey } from '@/api/llm.js'
import { choseAndUploadImage } from '@/libs/utils'



// 图片URL
const answerImageUrl = ref('https://quick-marker-oss.research.top/images/8b31fb04-9e7b-475a-8f13-b86ae45bf71f.jpg')
const paperImageUrl = ref('https://quick-marker-oss.research.top/images/04a395e8-a339-4873-9b1e-1c4bb62a0525.jpg')

// 页面状态
const isProcessing = ref(false)
const showResult = ref(false)
const countdown = ref(0)
const countdownTimer = ref(null)

// 批改结果
const correctionResult = ref(null)

// 是否可以开始批改
const canStartCorrection = computed(() => {
  return answerImageUrl.value && paperImageUrl.value
})



// 上传参考答案图片
const uploadAnswerImage = async () => {
  try {
    const res = await choseAndUploadImage()
    const url = res.data.ossUrl

    answerImageUrl.value = url
    uni.showToast({
      title: '参考答案更换成功',
      icon: 'success'
    })
  } catch (error) {
    if (error.message && (error.message.includes('cancel') || error.message.includes('canceled'))) {
      console.log('用户取消了上传操作')
    } else {
      console.error('上传参考答案失败', error)
      uni.showToast({
        title: '上传失败',
        icon: 'none'
      })
    }
  }
}

// 上传学生试卷图片
const uploadPaperImage = async () => {
  try {
    const res = await choseAndUploadImage()
    const url = res.data.ossUrl

    paperImageUrl.value = url
    uni.showToast({
      title: '学生试卷更换成功',
      icon: 'success'
    })
  } catch (error) {
    if (error.message && (error.message.includes('cancel') || error.message.includes('canceled'))) {
      console.log('用户取消了上传操作')
    } else {
      console.error('上传学生试卷失败', error)
      uni.showToast({
        title: '上传失败',
        icon: 'none'
      })
    }
  }
}

// 重置参考答案图片
const resetAnswerImage = () => {
  answerImageUrl.value = 'https://quick-marker-oss.research.top/images/8b31fb04-9e7b-475a-8f13-b86ae45bf71f.jpg'
  uni.showToast({
    title: '已重置为示例图片',
    icon: 'success'
  })
}

// 重置学生试卷图片
const resetPaperImage = () => {
  paperImageUrl.value = 'https://quick-marker-oss.research.top/images/04a395e8-a339-4873-9b1e-1c4bb62a0525.jpg'
  uni.showToast({
    title: '已重置为示例图片',
    icon: 'success'
  })
}





// 处理批改错误
const handleCorrectionError = (error) => {
  console.error('批改失败:', error)

  let errorMsg = '批改失败，请重试'
  if (error.message.includes('API调用失败')) {
    errorMsg = 'API调用失败，请检查网络连接'
  } else if (error.message.includes('数据结构')) {
    errorMsg = '分析结果解析失败'
  } else if (error.message.includes('JSON')) {
    errorMsg = '返回数据格式错误'
  }

  uni.showToast({
    title: errorMsg,
    icon: 'none',
    duration: 3000
  })

  if (error.message.includes('Authorization')) {
    setTimeout(promptForApiKey, 2000)
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 30
  countdownTimer.value = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value)
      countdownTimer.value = null
    }
  }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  countdown.value = 0
}

// 开始批改
const startCorrection = async () => {
  if (!canStartCorrection.value) return

  isProcessing.value = true
  startCountdown()

  try {
    const [answerUrl, paperUrl] = await Promise.all([
      uploadImageToOSS(answerImageUrl.value),
      uploadImageToOSS(paperImageUrl.value)
    ])

    const result = await correctByDoubao(answerUrl, paperUrl)

    if (result.code === 200) {
      correctionResult.value = result.data
      showResult.value = true
      uni.showToast({
        title: '批改完成！',
        icon: 'success'
      })
    } else {
      throw new Error(result.message || '批改失败')
    }
  } catch (error) {
    handleCorrectionError(error)
  } finally {
    stopCountdown()
    isProcessing.value = false
  }
}

// 提示用户输入API Key
const promptForApiKey = () => {
  uni.showModal({
    title: '设置API Key',
    content: '请输入您的豆包ARK API Key以使用AI批改功能',
    editable: true,
    placeholderText: '请输入API Key',
    success: (res) => {
      if (res.confirm && res.content) {
        setArKApiKey(res.content)
        uni.showToast({
          title: 'API Key已设置',
          icon: 'success'
        })
      }
    }
  })
}

// 上传图片到OSS或转换为可访问的URL
const uploadImageToOSS = async (imageUrl) => {
  // 如果已经是网络图片，直接返回
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }

  // 如果是本地临时文件，转换为base64
  try {
    const base64 = await convertToBase64(imageUrl)
    return `data:image/jpeg;base64,${base64}`
  } catch (error) {
    console.error('处理图片失败:', error)
    throw new Error('图片处理失败')
  }
}

// 将本地图片转换为base64
const convertToBase64 = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath,
      encoding: 'base64',
      success: (res) => resolve(res.data),
      fail: reject
    })
  })
}



// 预览图片
const previewImage = (url) => {
  const urls = [answerImageUrl.value, paperImageUrl.value]
  uni.previewImage({
    urls,
    current: url
  })
}

// 重新体验
const restartExperience = () => {
  stopCountdown()
  showResult.value = false
  answerImageUrl.value = 'https://quick-marker-oss.research.top/images/8b31fb04-9e7b-475a-8f13-b86ae45bf71f.jpg'
  paperImageUrl.value = 'https://quick-marker-oss.research.top/images/04a395e8-a339-4873-9b1e-1c4bb62a0525.jpg'
  correctionResult.value = null
}
</script>

<style lang="scss" scoped>
.quick-experience {
  min-height: 100vh;
  background-color: #F2F2F7;

  .content-container {
    padding: 32rpx;
    box-sizing: border-box;

    .page-header {
      text-align: center;
      padding: 20rpx 0 32rpx 0;

      .page-title {
        display: block;
        font-size: 40rpx;
        font-weight: 600;
        color: #1D1D1F;
        margin-bottom: 8rpx;
      }

      .page-subtitle {
        display: block;
        font-size: 26rpx;
        color: #86868B;
        font-weight: 400;
      }
    }

    .instruction-text {
      background-color: rgba(0, 122, 255, 0.1);
      padding: 24rpx;
      border-radius: 16rpx;
      margin-bottom: 32rpx;
      text-align: center;

      text {
        font-size: 28rpx;
        color: #007AFF;
        line-height: 1.4;
        font-weight: 500;
      }
    }

    .image-selection-section {
      .images-container {
        display: flex;
        gap: 24rpx;
        margin-bottom: 40rpx;

        .image-item {
          flex: 1;
          background-color: #fff;
          border-radius: 20rpx;
          padding: 24rpx;
          box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);

          .image-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;

            .image-title {
              font-size: 32rpx;
              font-weight: 600;
              color: #1D1D1F;
            }

            .header-actions {
              display: flex;
              gap: 24rpx;
            }

            .action-btn {
              font-size: 28rpx;
              color: #007AFF;
              font-weight: 500;
              cursor: pointer;

              &:active {
                opacity: 0.6;
              }
            }
          }

          .image-wrapper {
            .display-image {
              width: 100%;
              height: 240rpx;
              border-radius: 16rpx;
              background-color: #F2F2F7;
              cursor: pointer;

              &:active {
                opacity: 0.8;
              }
            }
          }
        }
      }

      .start-correction-btn {
        width: 100%;
        height: 100rpx;
        background-color: #007AFF;
        border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 34rpx;
        font-weight: 600;
        border: none;
        box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);

        &.disabled {
          background-color: #C7C7CC;
          color: #8E8E93;
          box-shadow: none;
        }

        .processing-content {
          display: flex;
          align-items: center;
          gap: 16rpx;

          .loading-spinner {
            animation: spin 1s linear infinite;
          }
        }

        &:not(.disabled):active {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }
    }


  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
