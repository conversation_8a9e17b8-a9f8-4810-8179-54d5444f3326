<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  no: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['update']);

// 编辑状态
const isEditing = ref(false);
// 编辑中的数据
const editData = ref({
  correctText: '',
  recognizedText: '',
  errorAnalysis: '',
  isCorrect: false
});

// 监听正确/错误状态变化，自动设置评语
watch(() => editData.value.isCorrect, (newValue) => {
  editData.value.errorAnalysis = newValue ? '正确' : '错误';
});

// 开始编辑
const startEdit = () => {
  editData.value = {
    correctText: props.item.correctText,
    recognizedText: props.item.recognizedText,
    errorAnalysis: props.item.errorAnalysis || (props.item.isCorrect ? '正确' : '错误'),
    isCorrect: props.item.isCorrect
  };
  isEditing.value = true;
};

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false;
};

// 保存编辑
const saveEdit = () => {
  emit('update', {
    id: props.item.id,
    correctText: props.item.correctText,
    recognizedText: editData.value.recognizedText,
    errorAnalysis: editData.value.errorAnalysis,
    isCorrect: editData.value.isCorrect ? 1 : 0
  });
  isEditing.value = false;
};

// 处理单选按钮变化
const handleRadioChange = (e) => {
  editData.value.isCorrect = e.detail.value === '1';
};
</script>

<template>
  <view class="answer-item">
    <view class="item-header">
      <text class="item-title">题目{{ no }}</text>
      <view class="header-right">
        <view :class="['item-status', item.isCorrect ? 'status-correct' : 'status-wrong']">
          {{ item.isCorrect ? '正确' : '错误' }}
        </view>
        <view class="edit-btn" @click="startEdit" v-if="!isEditing">
          <uni-icons type="compose" size="18" color="#4285F4"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 查看模式 -->
    <view class="item-content" v-if="!isEditing">
      <view class="answer-row">
        <text class="answer-label">参考答案：</text>
        <text class="answer-text">{{ item.correctText }}</text>
      </view>

      <view class="answer-row">
        <text class="answer-label">学生答案：</text>
        <text class="answer-text">{{ item.recognizedText }}</text>
      </view>

      <view class="answer-row" v-if="item.errorAnalysis">
        <text class="answer-label">评语说明：</text>
        <text class="answer-text">{{ item.errorAnalysis }}</text>
      </view>
    </view>

    <!-- 编辑模式 -->
    <view class="item-content edit-mode" v-else>
      <view class="answer-row">
        <text class="answer-label">参考答案：</text>
        <text class="answer-text">{{ item.correctText }}</text>
      </view>

      <view class="answer-row">
        <text class="answer-label">学生答案：</text>
        <textarea class="answer-textarea" v-model="editData.recognizedText" placeholder="输入学生答案" />
      </view>

      <view class="answer-row">
        <text class="answer-label">是否正确：</text>
        <radio-group class="radio-group" @change="handleRadioChange">
          <label class="radio-item">
            <radio value="1" :checked="editData.isCorrect" color="#4CAF50" />
            <text class="radio-text correct">正确</text>
          </label>
          <label class="radio-item">
            <radio value="0" :checked="!editData.isCorrect" color="#FF5252" />
            <text class="radio-text wrong">错误</text>
          </label>
        </radio-group>
      </view>

      <view class="answer-row">
        <text class="answer-label">评语说明：</text>
        <textarea class="answer-textarea" v-model="editData.errorAnalysis" placeholder="输入评语说明" />
      </view>

      <view class="edit-actions">
        <button class="action-btn cancel-btn" @click="cancelEdit">取消</button>
        <button class="action-btn save-btn" @click="saveEdit">保存</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.answer-item {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1px solid #eee;
  padding: 20rpx;

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 0;
    border-bottom: 1px solid #f5f5f5;

    .item-title {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
    }

    .header-right {
      display: flex;
      align-items: center;
    }

    .item-status {
      padding: 4rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #fff;
      margin-right: 10rpx;

      &.status-correct {
        background-color: #4CAF50;
      }

      &.status-wrong {
        background-color: #FF5252;
      }
    }

    .edit-btn {
      padding: 6rpx;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .item-content {
    padding: 12rpx 0;

    .answer-row {
      display: flex;
      margin-bottom: 16rpx;

      .answer-label {
        width: 200rpx;
        font-size: 26rpx;
        color: #666;
        padding-top: 4rpx;
      }

      .answer-text {
        flex: 1;
        font-size: 26rpx;
        color: #333;
      }

      .radio-group {
        display: flex;
        align-items: center;

        .radio-item {
          display: flex;
          align-items: center;
          margin-right: 30rpx;

          .radio-text {
            font-size: 26rpx;
            margin-left: 8rpx;

            &.correct {
              color: #4CAF50;
            }

            &.wrong {
              color: #FF5252;
            }
          }
        }
      }

      .answer-textarea {
        flex: 1;
        font-size: 26rpx;
        color: #333;
        border: 1px solid #ddd;
        border-radius: 4rpx;
        padding: 8rpx 12rpx;
        height: 120rpx;
        width: 100%;
      }
    }

    &.edit-mode {
      background-color: #f9f9f9;
      padding: 16rpx;
      border-radius: 8rpx;
    }

    .edit-actions {
      display: flex;
      justify-content: space-between;
      margin-top: 30rpx;
      padding: 0 20rpx;
      border-top: 1px solid #eee;
      padding-top: 30rpx;

      .action-btn {
        width: 160rpx;
        height: 70rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        border-radius: 8rpx;
        border: none;

        &.cancel-btn {
          background-color: #f5f5f5;
          color: #333;
          border: 1px solid #e0e0e0;
          margin-right: auto;

          &:active {
            background-color: #e8e8e8;
          }
        }

        &.save-btn {
          background-color: #4285F4;
          color: #fff;

          &:active {
            background-color: #3b78e7;
          }
        }
      }
    }
  }
}
</style>
