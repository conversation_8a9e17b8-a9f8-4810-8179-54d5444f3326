<template>
  <view class="correction-detail">
    <!-- 悬浮导航按钮 -->
    <view class="floating-nav">
      <view class="nav-btn prev-btn" @click="goToPrevious">
        <image src="/static/detail/arrow-up.svg" mode="aspectFit" class="nav-icon"></image>
      </view>
      <view class="nav-btn next-btn" @click="goToNext">
        <image src="/static/detail/arrow-down.svg" mode="aspectFit" class="nav-icon"></image>
      </view>
    </view>

    <view class="content-container">
      <!-- 学生信息 -->
      <view class="student-info">
        <view class="info-left" v-if="taskItemDetail.student">
          <view class="student-info-wrapper">
            <view class="student-details">
              <view class="student-name">{{ taskItemDetail.student.name }}</view>
              <view class="student-id" v-if="taskItemDetail.student.studentNumber">学号：{{ taskItemDetail.student.studentNumber }}</view>
            </view>
            <view class="edit-student-btn" @click="showStudentSelector">
              <uni-icons type="compose" size="18" color="#4285F4"></uni-icons>
            </view>
          </view>
        </view>
        <view class="info-left" v-else>
          <view class="select-student-btn" @click="showStudentSelector">
            <uni-icons type="plusempty" size="20" color="#4285F4"></uni-icons>
            <text>选择学生</text>
          </view>
        </view>
        <view class="info-right">
          <text class="exam-date">{{ formatDate(taskItemDetail.createTime) }}</text>
        </view>
      </view>

      <!-- 成绩统计 -->
      <view class="score-stats">
        <view class="stat-item total-score">
          <text class="stat-value total">{{ taskItemDetail.correctRate }}%</text>
          <text class="stat-label">正确率</text>
        </view>
        <view class="stat-item correct-count">
          <text class="stat-value correct">{{ taskItemDetail.correctCount }}</text>
          <text class="stat-label">正确数</text>
        </view>
        <view class="stat-item wrong-count">
          <text class="stat-value wrong">{{ taskItemDetail.wrongCount }}</text>
          <text class="stat-label">错误数</text>
        </view>
      </view>

      <!-- 分析建议 -->
      <view class="analysis-section">
        <view class="analysis-item error-analysis">
          <text class="analysis-title">错题分析</text>
          <view class="analysis-content">
            <view class="content-box">
              <view class="list-content">
                <view class="list-item">
                  <text class="item-text">{{ taskItemDetail.errorAnalysis }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 错题序号区域 -->
        <view class="analysis-item wrong-questions">
          <text class="analysis-title">错题序号</text>
          <view class="analysis-content">
            <view class="wrong-numbers-box">
              <view v-if="wrongQuestionNumbers.length > 0" class="wrong-numbers-list">
                <text v-for="(num, index) in wrongQuestionNumbers" :key="index" class="wrong-number-tag">{{ num }}</text>
              </view>
              <view v-else class="no-wrong-questions">
                <text>没有错题</text>
              </view>
            </view>
          </view>
        </view>
        <view class="analysis-item improvement">
          <text class="analysis-title">改进建议</text>
          <view class="analysis-content">
            <view class="list-content">
              <view class="list-item">
                <text class="item-text">{{ taskItemDetail.improvementSuggestions }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 答题详情 -->
      <view class="answer-detail-section">
        <view class="section-header">
          <view class="section-title">答题详情</view>
          <view class="filter-switch">
            <text class="switch-label">只看错误</text>
            <switch :checked="showErrorsOnly" @change="toggleErrorsOnly" color="#FF5252" style="transform:scale(0.8)" />
          </view>
        </view>
        <view class="answer-list">
          <AnswerItem
            v-for="item in filteredAnswerList"
            :key="item.id"
            :item="item"
            :no="item.no"
            @update="handleUpdateAnswer"
          />
        </view>
      </view>
    </view>

    <!-- 底部功能区 - 固定在底部 -->
    <view class="action-footer">
      <view class="action-buttons">
        <view class="action-btn view-btn" @click="viewOriginal">
          <uni-icons type="eye-filled" size="24" color="#666666"></uni-icons>
          <text>预览作业</text>
        </view>
        <button class="action-btn export-btn" @click="handleExport">
          <image src="/static/icons/excel.svg" class="excel-icon" mode="aspectFit"></image>
          <text class="btn-text">导出Excel</text>
        </button>
      </view>
    </view>
    <LoadingMask :show="isLoading" />

    <!-- 学生选择器组件 -->
    <StudentSelector
      v-if="showSelector"
      :paper="taskItemDetail"
      :class-id="taskItemDetail.classId"
      :task-id="taskItemDetail.taskId"
      @select="handleStudentConfirm"
      @visibleChange="handleSelectorVisibleChange"
    />
  </view>
</template>

<script setup>
import {ref, computed } from 'vue';
import AnswerItem from './components/AnswerItem.vue';
import LoadingMask from '@/components/LoadingMask';
import StudentSelector from '@/components/StudentSelector';
import { onLoad } from '@dcloudio/uni-app';
import {
  getTaskItemDetail,
  updateTaskItem,
  updateTaskItemDetail,
  exportTaskDetailExcel,
  getTaskItemIdList
} from '@/api/task-item';
import { formatDate } from '@/utils/tools';

const safeAreaInsetBottom = ref(0)
uni.getSystemInfo().then((res) => {
  safeAreaInsetBottom.value = res.safeAreaInsets.bottom < 20 ? 20 : res.safeAreaInsets.bottom
})



const taskId = ref('');
const taskItemDetail = ref({});
const isLoading = ref(false);
const showSelector = ref(false);
const sortField = ref('');

// 答题列表
const answerList = ref([]);

// 任务项ID列表和当前索引
const taskItemIds = ref([]);
const currentItemIndex = ref(-1);

// 只看错误开关
const showErrorsOnly = ref(false);

// 过滤后的答题列表
const filteredAnswerList = computed(() => {
  if (showErrorsOnly.value) {
    return answerList.value.filter(item => !item.isCorrect);
  }
  return answerList.value;
});

// 错题序号列表
const wrongQuestionNumbers = computed(() => {
  return answerList.value
    .filter(item => !item.isCorrect)
    .map(item => item.no)
    .sort((a, b) => a - b);
});

onLoad((option) => {
  const { id, sortField: optionSortField } = option;
  if (id) {
    taskId.value = id;
    if (optionSortField) {
      sortField.value = optionSortField;
    }
    fetchTaskItemDetail(id);
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
  }
})

// 获取任务下所有批改任务项ID列表
const fetchTaskItemIds = async (taskId, sortField) => {
  try {
    const res = await getTaskItemIdList({
      taskId,
      sortField,
    });
    if (res.data && Array.isArray(res.data)) {
      taskItemIds.value = res.data;
      // 找到当前任务项在列表中的索引
      const currentId = taskItemDetail.value.id;
      currentItemIndex.value = taskItemIds.value.findIndex(id => id === currentId);

      // 删除首次加载时的位置提示
    }
  } catch (error) {
    console.error('获取任务项ID列表失败:', error);
  }
};

// 获取批改任务详情
const fetchTaskItemDetail = async (id) => {
  isLoading.value = true;
  return new Promise(async (resolve, reject) => {
    try {
      const res = await getTaskItemDetail(id);
      const data = res.data;

      taskItemDetail.value = res.data;
      fetchTaskItemIds(data.taskId, sortField.value);

      // 更新答题列表
      const list = data.appCorrectionTaskItemDetailsRespVOS || [];

      answerList.value = list.map((item, index) => {
          return {
            ...item,
            no: item.no || index + 1,
          }
      });
      resolve();
    } catch (error) {
      console.error('获取批改任务详情失败:', error);
      uni.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
      reject(error);
    } finally {
      isLoading.value = false;
    }
  });
};

// 查看原图
const viewOriginal = () => {
  if (!taskItemDetail.value.images) {
    uni.showToast({
      title: '暂无原图',
      icon: 'none'
    });
    return;
  }

  uni.previewImage({
    urls: [taskItemDetail.value.images],
    current: 0
  });
};

// 导出Excel报告
const handleExport = async () => {
  try {
    uni.showLoading({ title: '正在导出...' });
    const res = await exportTaskDetailExcel({
      itemId: taskItemDetail.value.id,
      minCorrectRate: 0,
      maxCorrectRate: 100,
      pageNo: 1,
      pageSize: 1,
    });

    const fs = wx.getFileSystemManager();
    const tempFilePath = `${wx.env.USER_DATA_PATH}/${taskItemDetail.value.student?.name || '成绩报告'}.xlsx`;

    fs.writeFile({
      filePath: tempFilePath,
      data: res,
      encoding: 'binary',
      success: () => {
        uni.hideLoading();
        uni.openDocument({
          filePath: tempFilePath,
          showMenu: true,
          success: () => {
            console.log('文件打开成功');
          },
          fail: (err) => {
            console.error('文件打开失败:', err);
            uni.showToast({
              title: '文件打开失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        uni.hideLoading();
        console.error('文件保存失败:', err);
        uni.showToast({
          title: '文件保存失败',
          icon: 'none'
        });
      }
    });
  } catch (err) {
    uni.hideLoading();
    console.error('导出失败:', err);
    uni.showToast({
      title: '导出失败',
      icon: 'none'
    });
  }
};

// 切换只看错误开关
const toggleErrorsOnly = (e) => {
  showErrorsOnly.value = e.detail.value;
};

// 显示学生选择器
const showStudentSelector = () => {
  showSelector.value = true;
};

// 处理学生选择器可见性变化
const handleSelectorVisibleChange = (data) => {
  showSelector.value = data.visible;
};

// 处理学生选择确认
const handleStudentConfirm = async (data) => {
  const { paper, student } = data;
  try {
    const inData = {
      id: paper.id,
      taskId: paper.taskId,
      teacherId: paper.teacherId,
      classId: paper.classId,
      studentId: student.id,
    }
    await updateTaskItem(inData);

    // 更新当前任务项中的学生信息
    taskItemDetail.value.student = {
      id: student.id,
      name: student.name,
      studentNumber: student.studentNumber
    };

    uni.showToast({
      title: '学生更新成功',
      icon: 'success'
    });
  } catch (err) {
    console.error('更新学生失败:', err);
    uni.showToast({
      title: '更新学生失败',
      icon: 'none'
    });
  }
};

// 处理更新答题项
const handleUpdateAnswer = async (data) => {
  try {
    isLoading.value = true;

    // 构建更新数据
    const updateData = {
      itemId: taskItemDetail.value.id,
      ...data,
    };

    // 调用API更新
    await updateTaskItemDetail(updateData);
    await fetchTaskItemDetail(taskId.value)

    uni.showToast({
      title: '更新成功',
      icon: 'success'
    });
  } catch (error) {
    console.error('更新答题项失败:', error);
    uni.showToast({
      title: '更新失败',
      icon: 'none'
    });
  } finally {
    isLoading.value = false;
  }
};

// 导航到上一个任务项
const goToPrevious = () => {
  if (currentItemIndex.value > 0 && taskItemIds.value.length > 0) {
    const prevIndex = currentItemIndex.value - 1;
    const prevId = taskItemIds.value[prevIndex];

    // 显示加载提示
    uni.showLoading({
      title: '正在加载上一个...',
      mask: true
    });

    // 更新当前任务项ID并重新加载详情
    taskId.value = prevId;
    fetchTaskItemDetail(prevId).then(() => {
      // 加载成功后显示提示
      uni.hideLoading();
      uni.showToast({
        title: '已切换到上一个',
        icon: 'none',
        duration: 1500
      });

      // 滚动到页面顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }).catch(() => {
      uni.hideLoading();
    });

    // 重新获取任务项ID列表，保持排序一致
    fetchTaskItemIds(sortField.value);
  } else {
    uni.showToast({
      title: '已经是第一个了',
      icon: 'none'
    });
  }
};

// 导航到下一个任务项
const goToNext = () => {
  if (currentItemIndex.value < taskItemIds.value.length - 1 && taskItemIds.value.length > 0) {
    const nextIndex = currentItemIndex.value + 1;
    const nextId = taskItemIds.value[nextIndex];

    // 显示加载提示
    uni.showLoading({
      title: '正在加载下一个...',
      mask: true
    });

    // 更新当前任务项ID并重新加载详情
    taskId.value = nextId;
    fetchTaskItemDetail(nextId).then(() => {
      // 加载成功后显示提示
      uni.hideLoading();
      uni.showToast({
        title: '已切换到下一个',
        icon: 'none',
        duration: 1500
      });

      // 滚动到页面顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }).catch(() => {
      uni.hideLoading();
    });

    // 重新获取任务项ID列表，保持排序一致
    fetchTaskItemIds(sortField.value);
  } else {
    uni.showToast({
      title: '已经是最后一个了',
      icon: 'none'
    });
  }
};
</script>
<style>
page{

}
</style>
<style lang="scss" scoped>
.correction-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  position: relative; // 添加相对定位以作为悬浮元素的参考

  // 悬浮导航按钮
  .floating-nav {
    position: fixed;
    right: 20rpx;
    top: 50%;
    transform: translateY(-50%);
    z-index: 100;
    display: flex;
    flex-direction: column;
    gap: 20rpx;

    .nav-btn {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background-color: rgba(66, 133, 244, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
      transition: all 0.2s ease;

      .nav-icon {
        width: 32rpx;
        height: 32rpx;
      }

      &:active {
        transform: scale(0.95);
        background-color: rgba(66, 133, 244, 1);
      }
    }
  }

  .content-container {
    padding: 20rpx;
    box-sizing: border-box;

    // 学生信息
    .student-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .info-left {
        .student-info-wrapper {
          display: flex;
          align-items: flex-start;
        }

        .student-details {
          margin-right: 16rpx;
        }

        .student-name {
          font-size: 32rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .student-id, .student-class {
          font-size: 26rpx;
          color: #666;
          margin-top: 8rpx;
        }

        .edit-student-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;

          &:active {
            background-color: #F0F7FF;
          }
        }

        .select-student-btn {
          display: flex;
          align-items: center;
          background-color: #F0F7FF;
          padding: 12rpx 20rpx;
          border-radius: 8rpx;

          text {
            color: #4285F4;
            font-size: 28rpx;
            margin-left: 8rpx;
          }

          &:active {
            opacity: 0.8;
          }
        }
      }

      .info-right {
        .exam-date {
          font-size: 26rpx;
          color: #666;
        }
      }
    }

    // 成绩统计
    .score-stats {
      display: flex;
      justify-content: space-between;
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;

        .stat-value {
          font-size: 50rpx;
          font-weight: 600;
          margin-bottom: 8rpx;

          &.total {
            color: $uni-primary-color;
          }

          &.correct {
            color: #4CAF50;
          }

          &.wrong {
            color: #FF5252;
          }
        }

        .stat-label {
          font-size: 26rpx;
          color: #666;
        }
      }
    }

    // 分析建议
    .analysis-section {
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .analysis-item {
        margin-bottom: 30rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .analysis-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 20rpx;
          display: block;
        }

        .analysis-content {
          .content-box {
            background-color: #FFF7F5;
            border-radius: 8rpx;
            padding: 20rpx;

            .main-text {
              color: #333;
              font-size: 28rpx;
              margin-bottom: 10rpx;
              display: block;
            }
          }

          .list-content {
            padding: 0 10rpx;

            .list-item {
              display: flex;
              margin-bottom: 16rpx;
              font-size: 28rpx;
              line-height: 1.5;

              &:last-child {
                margin-bottom: 0;
              }

              .item-number {
                color: #666;
                margin-right: 8rpx;
              }

              .item-text {
                color: #333;
                flex: 1;
              }
            }
          }
        }

        &.error-analysis {
          .content-box {
            background-color: #FFF7F5;
          }
        }

        &.improvement {
          .list-content {
            background-color: #F0F7FF;
            border-radius: 8rpx;
            padding: 20rpx;
          }
        }

        &.wrong-questions {
          .wrong-numbers-box {
            background-color: #FFF7F5;
            border-radius: 8rpx;
            padding: 20rpx;

            .wrong-numbers-list {
              display: flex;
              flex-wrap: wrap;
              gap: 16rpx;

              .wrong-number-tag {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                min-width: 48rpx;
                height: 48rpx;
                padding: 0 12rpx;
                background-color: #FF5252;
                color: #fff;
                border-radius: 24rpx;
                font-size: 24rpx;
              }
            }

            .no-wrong-questions {
              text-align: center;
              color: #666;
              font-size: 28rpx;
              padding: 20rpx 0;
            }
          }
        }
      }
    }

    // 答题详情
    .answer-detail-section {
      background-color: #fff;
      padding: 30rpx;
      border-radius: 12rpx;
      margin-bottom: 20rpx;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .section-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }

        .filter-switch {
          display: flex;
          align-items: center;

          .switch-label {
            font-size: 26rpx;
            color: #666;
            margin-right: 10rpx;
          }
        }
      }
    }
  }

  // 底部功能区
  .action-footer {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12rpx 24rpx;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    padding-bottom: v-bind('safeAreaInsetBottom + "rpx"');

    .action-buttons {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }

    .action-btn {
      height: 72rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border: 2rpx solid #e5e5e5;
      border-radius: 8rpx;
      padding: 0 32rpx;

      text {
        font-size: 26rpx;
        margin-left: 8rpx;
        color: #333;
      }

      &.view-btn {
        margin-right: 16rpx;
      }

      &.export-btn {
        flex: 1;
        background-color: #fff;

        .excel-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }

        .btn-text {
          color: #217346;
        }
      }

      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>
