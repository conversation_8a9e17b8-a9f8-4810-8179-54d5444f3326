<template>
  <view class="grade-query-container">
    <view class="content">
      <class-selector
        :class-list="classNameList"
        :selected-index="selectedClassIndex"
        @change="onClassChange"
      />

      <view v-if="gradeList.length===0" style="margin-top: 100rpx;">
        <Empty text="暂无成绩记录"></Empty>
      </view>

      <grade-list
         v-else
        :grade-list="gradeList"
        :loading="loading"
        :load-more-status="loadMoreStatus"
        @item-click="navigateToDetail"
        @delete="handleDelete"
        @rename="handleRename"
      />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { correctionTaskList } from '@/api/correction-task';
import { getClassList } from '@/api/class';
import { onPullDownRefresh, onReachBottom, onShow } from "@dcloudio/uni-app";
import Empty from "@/components/Empty.vue";
import { deleteTask, updateTask } from '@/api/task.js';

// 导入组件
import ClassSelector from './components/ClassSelector.vue';
import GradeList from './components/GradeList.vue';

// 班级列表
const classNameList = ref(['全部班级']);
const classList = ref([{ id: '', className: '全部班级' }]);
const selectedClassIndex = ref(0);
const selectedClassId = ref('');
const loading = ref(false);
const loadingClasses = ref(false);

// 成绩数据
const gradeList = ref([]);

// 分页相关
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);
const loadMoreStatus = ref('more');

// 获取班级列表
const fetchClassList = async () => {
  loadingClasses.value = true;
  try {
    const res = await getClassList({
      pageNo: 1,
      pageSize: 100
    });

    if (res.code === 0 || res.code === 200) {
      if (res.data && res.data.list) {
        // 处理API返回的数据，保留'全部班级'选项在首位
        const classes = res.data.list.map(item => ({
          id: item.id,
          className: item.className || '未命名班级'
        }));
        classList.value = [{ id: '', className: '全部班级' }, ...classes];
        classNameList.value = classList.value.map(item => item.className);
      }
    } else {
      console.error('获取班级列表失败:', res.msg);
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
  } finally {
    loadingClasses.value = false;
  }
};

// 获取成绩列表数据
const fetchGradeList = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1;
    hasMore.value = true;
  }

  if (!hasMore.value) return;

  loading.value = true;
  loadMoreStatus.value = 'loading';

  try {
    const params = {
      pageNo: page.value,
      pageSize: pageSize.value
    };

    // 如果选择了特定班级，添加classId参数
    if (selectedClassId.value) {
      params.classId = selectedClassId.value;
    }

    const res = await correctionTaskList(params);
    gradeList.value = isRefresh ? res.data.list : [...gradeList.value, ...res.data.list];

    // 更新分页状态
    const total = res.data.total || 0;
    hasMore.value = gradeList.value.length < total;
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore';
    page.value++;
  } catch (error) {
    console.error('获取成绩列表失败:', error);
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
    loadMoreStatus.value = 'more';
  } finally {
    loading.value = false;
  }
};

// 处理班级选择
const onClassChange = (e) => {
  selectedClassIndex.value = e.detail.value;
  selectedClassId.value = classList.value[selectedClassIndex.value].id;
  // 班级变更后重新获取成绩列表
  fetchGradeList(true);
};

// 导航到详情页
const navigateToDetail = (item) => {
  if (!item || !item.id) return;

  // 根据 taskType 决定跳转到哪个页面
  let url = '';
  if (item.taskType === 3) {
    // 作文批改
    url = `/pages/essay-correction/step-3/step-3?taskId=${item.id}`;
  } else if (item.taskType === 1 || item.taskType === 2) {
    // 英语默写批改
    url = `/pages/ai-correction/step-3/step-3?taskId=${item.id}`;
  } else {
    // 默认跳转到英语默写批改
    url = `/pages/ai-correction/step-3/step-3?taskId=${item.id}`;
  }

  uni.navigateTo({
    url: url
  });
};

// 处理删除
const handleDelete = async (id) => {
  try {
    uni.showModal({
      title: '提示',
      content: '确定要删除这条记录吗？',
      success: async (res) => {
        if (res.confirm) {
          await deleteTask(id);
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
          // 重新获取列表
          fetchGradeList(true);
        }
      }
    });
  } catch (error) {
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    });
  }
};

// 处理重命名
const handleRename = async (data) => {
  try {
    uni.showModal({
      title: '重命名',
      editable: true,
      placeholderText: '请输入新名称',
      content: data.title,
      success: async (res) => {
        if (res.confirm && res.content.trim()) {
          await updateTask({
            id: data.id,
            classId: data.classId,
            teacherId: data.teacherId,
            title: res.content.trim()
          });
          uni.showToast({
            title: '重命名成功',
            icon: 'success'
          });
          // 重新获取列表
          fetchGradeList(true);
        }
      }
    });
  } catch (error) {
    uni.showToast({
      title: '重命名失败',
      icon: 'error'
    });
  }
};

// 下拉刷新
onPullDownRefresh(async () => {
  await fetchGradeList(true);
  uni.stopPullDownRefresh();
});

// 触底加载更多
onReachBottom(() => {
  console.log('触底加载更多');
  if (!loading.value && hasMore.value) {
    fetchGradeList();
  }
});

// 页面重新显示时刷新数据
onShow(() => {
  fetchClassList();
  fetchGradeList(true);
});
</script>

<style lang="scss" scoped>
.grade-query-container {
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
</style>
