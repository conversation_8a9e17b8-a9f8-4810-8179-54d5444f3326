<template>
  <view class="error-notification" v-if="errorCount > 0">
    <view class="error-header">
      <uni-icons type="warn" color="#F5A623" size="20"></uni-icons>
      <text class="error-title">发现 {{ errorCount }} 条识别异常数据</text>
    </view>
    
    <view class="error-item" v-for="(item, index) in errorItems" :key="index">
      <view class="error-text">
        <text class="label">原始文本:</text>
        <text class="link" @click="viewOriginalText(item)">点击查看</text>
      </view>
      <view class="error-action">
        <view class="picker-container">
          <picker @change="onStudentChange($event, index)" :value="item.selectedIndex" :range="studentOptions" range-key="name">
            <view class="uni-input">{{ item.selectedIndex > -1 ? studentOptions[item.selectedIndex].name : '请选择学生' }}<uni-icons class="arrow-icon" type="arrowdown" size="16" color="#333"></uni-icons></view>
          </picker>
        </view>
        <button class="confirm-btn" @click="confirmSelection(index)">确认</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  errorItems: {
    type: Array,
    default: () => [
      { id: 1, originalText: '这是第一条异常数据的原始文本', selectedIndex: -1 },
      { id: 2, originalText: '这是第二条异常数据的原始文本', selectedIndex: -1 }
    ]
  },
  studentOptions: {
    type: Array,
    default: () => [
      { id: '2023001', name: '陈思远' },
      { id: '2023002', name: '林雨晴' },
      { id: '2023003', name: '张浩然' },
      { id: '2023004', name: '刘梦琪' },
      { id: '2023005', name: '王子轩' },
      { id: '2023006', name: '赵雪宝' },
      { id: '2023007', name: '黄嘉伟' }
    ]
  }
});

const errorCount = computed(() => props.errorItems.length);

const emit = defineEmits(['confirm', 'viewOriginal']);

// 处理学生选择
const onStudentChange = (e, index) => {
  const selectedIndex = e.detail.value;
  props.errorItems[index].selectedIndex = selectedIndex;
};

// 确认选择
const confirmSelection = (index) => {
  const item = props.errorItems[index];
  if (item.selectedIndex === -1) {
    uni.showToast({
      title: '请先选择学生',
      icon: 'none'
    });
    return;
  }
  
  const selectedStudent = props.studentOptions[item.selectedIndex];
  emit('confirm', { errorId: item.id, studentId: selectedStudent.id, studentName: selectedStudent.name, index });
};

// 查看原始文本
const viewOriginalText = (item) => {
  emit('viewOriginal', item);
};
</script>

<style lang="scss" scoped>
.error-notification {
  margin: 20rpx 0 40rpx;
  padding: 30rpx;
  background-color: #FFFBF0;
  border: 2rpx solid #FFECB3;
  border-radius: 16rpx;
  
  .error-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    
    .error-title {
      margin-left: 10rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  .error-item {
    padding: 20rpx 0;
    border-bottom: 1rpx solid #F0F0F0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .error-text {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;
      
      .label {
        font-size: 28rpx;
        color: #333;
      }
      
      .link {
        margin-left: 8rpx;
        font-size: 28rpx;
        color: #4285F4;
      }
    }
    
    .error-action {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .picker-container {
        flex: 1;
        margin-right: 16rpx;
        
        .uni-input {
          height: 80rpx;
          padding: 0 24rpx;
          background-color: #FFFFFF;
          border: 1rpx solid #E0E0E0;
          border-radius: 8rpx;
          font-size: 28rpx;
          color: #333;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      
      .confirm-btn {
        width: 160rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        background-color: #4285F4;
        color: #FFFFFF;
        font-size: 28rpx;
        border-radius: 8rpx;
        padding: 0;
      }
    }
  }
}
</style>
