<template>
  <view class="camera-container">
    <!-- Camera View - Always Visible -->
    <camera device-position="back" :flash="flashMode" class="camera" @error="handleCameraError">
      <cover-view class="camera-controls">
        <cover-view class="camera-flash" @tap="toggleFlash">
          <cover-view class="flash-icon">{{ flashIcon }}</cover-view>
        </cover-view>
        <cover-view class="camera-btn" @tap="takePhoto">
          <cover-view class="camera-btn-inner"></cover-view>
        </cover-view>
        <cover-view class="camera-close" @tap="closeCamera">关闭</cover-view>
      </cover-view>
    </camera>

    <!-- Photo Gallery -->
    <view class="photo-gallery">
      <scroll-view
        scroll-x
        class="gallery-scroll"
        scroll-with-animation
        :scroll-into-view="currentPhotoId"
        ref="scrollViewRef"
      >
        <view v-for="(photo, index) in capturedPhotos" :key="index" class="photo-item" :id="`photo-${index}`">
          <image :src="photo" mode="aspectFill" class="photo-thumbnail"></image>
          <view class="photo-number">{{ index + 1 }}</view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';

const emit = defineEmits(['close', 'photo-taken', 'camera-error']);

// 相机相关状态
const cameraContext = ref(null);
const flashMode = ref('off');
const capturedPhotos = ref([]);
const scrollViewRef = ref(null);
const currentPhotoId = ref('');
const flashIcon = computed(() => {
  return flashMode.value === 'torch' ? '关灯' : '开灯';
});

onMounted(() => {
  // 在组件挂载后初始化相机上下文
  setTimeout(() => {
    cameraContext.value = uni.createCameraContext();
  }, 100);
});

/**
 * 处理相机错误
 */
const handleCameraError = (e) => {
  console.error('相机错误:', e.detail);
  emit('camera-error', e.detail);
  showToast('相机启动失败，请检查相机权限');
};


/**
 * 拍照
 */
const takePhoto = () => {
  if (!cameraContext.value) {
    showToast('相机未初始化');
    return;
  }

  // 添加震动反馈
  uni.vibrateShort({
    success: () => {
      console.log('震动成功');
    },
    fail: (err) => {
      console.error('震动失败:', err);
    }
  });

  cameraContext.value.takePhoto({
    quality: 'high',
    success: (res) => {
      // 添加到照片列表
      capturedPhotos.value.push(res.tempImagePath);
      // 生成新照片的 ID
      const newPhotoId = `photo-${capturedPhotos.value.length - 1}`;
      currentPhotoId.value = newPhotoId;
      // 通知父组件照片已拍摄
      emit('photo-taken', res.tempImagePath);
    },
    fail: (err) => {
      console.error('拍照失败:', err);
      showToast('拍照失败');
    }
  });
};

/**
 * 关闭相机
 */
const closeCamera = () => {
  emit('close');
};

/**
 * 切换闪光灯模式
 */
const toggleFlash = () => {
  flashMode.value = flashMode.value === 'torch' ? 'off' : 'torch';
};

/**
 * 显示提示信息
 */
const showToast = (title, icon = 'none') => {
  uni.showToast({
    title,
    icon
  });
};
</script>

<style lang="scss" scoped>
.camera-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background-color: #000;
  display: flex;
  flex-direction: column;

  .camera {
    width: 100%;
    flex: 1;
  }

  .camera-controls {
    position: absolute;
    bottom: 40rpx;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    padding: 0 40rpx;

    .camera-flash {
      width: 100rpx;
      height: 100rpx;
      line-height: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      transition: all 0.2s;
      text-align: center;

      &:active {
        background-color: rgba(255, 255, 255, 0.4);
        transform: scale(0.95);
      }

      .flash-icon {
        font-size: 28rpx;
        color: #fff;
      }
    }

    .camera-btn {
      width: 140rpx;
      height: 140rpx;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.2s;

      &:active {
        transform: scale(0.95);
      }

      .camera-btn-inner {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background-color: #fff;
        transition: all 0.2s;
      }

      &:active .camera-btn-inner {
        width: 110rpx;
        height: 110rpx;
      }
    }

    .camera-close {
      width: 100rpx;
      height: 100rpx;
      line-height: 100rpx;
      color: #fff;
      font-size: 32rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.2);
      transition: all 0.2s;
      text-align: center;

      &:active {
        background-color: rgba(255, 255, 255, 0.4);
        transform: scale(0.95);
      }
    }
  }

  .photo-gallery {
    background-color: #1a1a1a;
    padding: 20rpx 0;

    .gallery-scroll {
      width: 100%;
      white-space: nowrap;
      height: 140rpx;
    }

    .photo-item {
      display: inline-block;
      position: relative;
      margin-left: 20rpx;

      &:last-child {
        margin-right: 20rpx;
      }

      .photo-thumbnail {
        width: 120rpx;
        height: 140rpx;
        border-radius: 8rpx;
      }

      .photo-number {
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        width: 40rpx;
        height: 40rpx;
        border-top-left-radius: 8rpx;
        border-bottom-right-radius: 8rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24rpx;
        text-align: center;
      }
    }
  }
}
</style>
