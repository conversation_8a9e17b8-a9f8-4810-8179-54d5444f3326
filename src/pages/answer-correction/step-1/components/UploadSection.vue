<template>
  <view class="upload-section">
    <!-- 上传区域 -->
    <view class="upload-area">
      <view class="upload-title">上传答题卡</view>
      <view class="upload-desc">请上传学生填写的答题卡图片，支持标准答题卡格式</view>
      
      <view class="upload-container">
        <view class="upload-btn-main" @click="handleUploadType(1)">
          <image class="upload-icon" src="/static/icons/paper-plane-white.svg" mode="aspectFit"></image>
          <text>选择答题卡图片</text>
        </view>
      </view>
      
      <view class="upload-tips">
        <text class="tip-item">• 请确保答题卡图片清晰完整</text>
        <text class="tip-item">• 支持JPG、PNG格式</text>
        <text class="tip-item">• 建议图片大小不超过10MB</text>
      </view>
    </view>


  </view>
</template>

<script setup>
const emit = defineEmits(['upload']);

// 处理上传
const handleUploadType = (type) => {
  emit('upload', type);
};
</script>

<style lang="scss" scoped>
.upload-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 30rpx;
      color: #333;
    }
  }

  // 上传区域样式
  .upload-area {
    text-align: center;
    
    .upload-title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 16rpx;
    }
    
    .upload-desc {
      font-size: 26rpx;
      color: #666;
      margin-bottom: 40rpx;
      line-height: 1.5;
    }
    
    .upload-container {
      margin-bottom: 32rpx;
      
      .upload-btn-main {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: $uni-primary-color;
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
        border-radius: 50rpx;
        padding: 20rpx 40rpx;
        margin: 0 auto;
        width: fit-content;
        min-width: 280rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
        
        .upload-icon {
          width: 36rpx;
          height: 36rpx;
          margin-right: 12rpx;
        }
        
        &:active {
          opacity: 0.8;
          transform: scale(0.98);
        }
      }
    }
    
    .upload-tips {
      background-color: #f8f9fa;
      border-radius: 12rpx;
      padding: 24rpx;
      
      .tip-item {
        display: block;
        font-size: 24rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 8rpx;
        text-align: left;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
