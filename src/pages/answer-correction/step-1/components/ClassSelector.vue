<template>
  <view class="scoring-section">
    <view class="form-item">
      <text class="form-label">班级</text>
      <view class="form-input-box">
        <picker :value="classIndex" :range="classList" @change="handleClassChange">
          <view class="form-input">
            {{ classList[classIndex] || '请选择班级' }}
            <uni-icons type="arrowdown" size="14" color="#666"></uni-icons>
          </view>
        </picker>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  classList: {
    type: Array,
    default: () => []
  },
  classIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['classChange']);

// 班级选择
const handleClassChange = (e) => {
  emit('classChange', e.detail.value);
};
</script>

<style lang="scss" scoped>
.scoring-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

  .form-item {
    display: flex;
    align-items: center;
    margin-bottom: 0;

    .form-label {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 0;
      margin-right: 20rpx;
      white-space: nowrap;
    }

    .form-input-box {
      background-color: #f9f9f9;
      border-radius: 8rpx;
      border: 2rpx solid #eee;
      flex: 1;
    }

    .form-input {
      height: 72rpx;
      padding: 0 20rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28rpx;
    }
  }
}
</style>
