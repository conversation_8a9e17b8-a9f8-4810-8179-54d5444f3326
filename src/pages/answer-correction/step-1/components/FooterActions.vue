<template>
  <view class="footer">
    <button
      class="next-btn"
      :disabled="disabled"
      :class="{ 'btn-disabled': disabled }"
      @click="handleNext"
    >
      下一步
    </button>
  </view>
</template>

<script setup>
const props = defineProps({
  disabled: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['next']);

// 下一步
const handleNext = () => {
  if (!props.disabled) {
    emit('next');
  }
};
</script>

<style lang="scss" scoped>
.footer {
  padding: 32rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;

  .next-btn {
    background-color: $uni-primary-color;
    color: #fff;
    height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn-disabled {
    background-color: #ccc;
    color: #666;
    opacity: 0.7;
    cursor: not-allowed;
    box-shadow: none;
  }
}
</style>
