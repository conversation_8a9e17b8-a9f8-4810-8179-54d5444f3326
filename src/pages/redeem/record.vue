<template>
  <view class="record-page">
    <view class="record-list">
      <view v-if="loading" class="loading">加载中...</view>
      <Empty v-else-if="records.length === 0" text="暂无兑换记录" />
      <view v-else>
        <view class="record-item" v-for="item in records" :key="item.id">
          <view class="record-info">
            <text class="record-name">兑换码: {{ maskCode(item.code) }}</text>
            <text class="record-time">兑换时间：{{ formatTime(item.createTime) }}</text>
          </view>
          <view class="record-amount">
            <text class="amount">+{{ item.correctionCount }}</text>
            <text class="amount-unit">次</text>
          </view>
        </view>
        <uni-load-more :status="loadMoreStatus" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Empty from '@/components/Empty.vue'
import { getMyRedeemCodeRecords } from '@/api/redeem'
import { onPullDownRefresh, onReachBottom, onShow } from "@dcloudio/uni-app"

const loading = ref(false)
const records = ref([])
const loadMoreStatus = ref('more')
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 掩码处理兑换码，只显示前4位和后4位
const maskCode = (code) => {
  if (!code) return ''
  if (code.length <= 8) return code
  return `${code.substring(0, 4)}****${code.substring(code.length - 4)}`
}

// 获取兑换记录
const fetchRecords = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    hasMore.value = true
  }

  if (!hasMore.value) return

  loading.value = true
  loadMoreStatus.value = 'loading'

  try {
    const params = {
      pageNo: page.value,
      pageSize: pageSize.value
    }

    const res = await getMyRedeemCodeRecords(params)

    const { list, total } = res.data
    records.value = isRefresh ? list : [...records.value, ...list]
    hasMore.value = records.value.length < total
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore'
    page.value++
  } catch (error) {
    console.error('获取兑换记录失败:', error)
    uni.showToast({
      title: '获取兑换记录失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'more'
  } finally {
    loading.value = false
  }
}

// 下拉刷新
onPullDownRefresh(async () => {
  await fetchRecords(true)
  uni.stopPullDownRefresh()
})

// 触底加载更多
onReachBottom(() => {
  if (!loading.value && hasMore.value) {
    fetchRecords()
  }
})

// 页面显示时刷新数据
onShow(() => {
  fetchRecords(true)
})

onMounted(() => {
  fetchRecords(true)
})
</script>

<style lang="scss">
.record-page {
  min-height: 100vh;
  background-color: #F5F7FF;
  padding: 24rpx;

  .record-list {
    .loading {
      text-align: center;
      padding: 40rpx 0;
      color: #8E8E93;
      font-size: 28rpx;
    }

    .record-item {
      background-color: #FFFFFF;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-bottom: 24rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

      .record-info {
        .record-name {
          font-size: 32rpx;
          color: #333333;
          font-weight: 600;
          margin-bottom: 8rpx;
          display: block;
        }

        .record-time {
          font-size: 24rpx;
          color: #8E8E93;
        }
      }

      .record-amount {
        display: flex;
        flex-direction: row;
        align-items: center;

        .amount {
          font-size: 36rpx;
          color: #3c77ef;
          font-weight: 600;
        }

        .amount-unit {
          font-size: 24rpx;
          color: #8E8E93;
          margin-left: 4rpx;
        }
      }
    }
  }
}
</style>
