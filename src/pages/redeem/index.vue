<template>
  <view class="redeem-container">
    <!-- 兑换码输入区域 -->
    <view class="redeem-input-section">
      <view class="input-title">
        <image class="title-icon" :src="giftIcon" mode="aspectFit"></image>
        <text class="title-text">兑换码</text>
        <view class="flex-spacer"></view>
        <view class="record-link" @click="navigateToRedeemRecord">
          <text class="record-text">兑换记录</text>
          <image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
        </view>
      </view>
      <view class="input-container">
        <input
          class="redeem-input"
          type="text"
          v-model="redemptionCode"
          placeholder="请输入兑换码"
          :maxlength="REDEEM_CODE_LENGTH"
          @input="handleInput"
        />
        <view class="clear-btn" v-if="redemptionCode" @click="clearInput">
          <image class="clear-icon" src="/static/icons/clear.svg" mode="aspectFit"></image>
        </view>
      </view>
      <text class="input-tip">请输入{{REDEEM_CODE_LENGTH}}位兑换码，区分大小写</text>
    </view>

    <!-- 兑换按钮 -->
    <view
      class="redeem-btn"
      :class="{ 'redeem-btn-active': isValidCode }"
      @click="handleRedeem"
    >
      <text>立即兑换</text>
    </view>

    <!-- 兑换说明 -->
    <view class="redeem-instructions">
      <view class="instruction-title">兑换说明</view>
      <view v-for="(instruction, index) in instructions" :key="index" class="instruction-item">
        <text class="item-dot">•</text>
        <text class="item-text">{{ instruction }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
// 导入外部依赖
import { computed, ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 导入API和状态管理
import { getCurrentTeacher } from '@/api/teacher';
import { useRedeemCode } from '@/api/redeem';
import { useUserStore } from '@/store/user';

// 状态管理
const userStore = useUserStore();
const userInfo = ref(userStore.userInfo);

// 常量和变量
const REDEEM_CODE_LENGTH = 14;
const redemptionCode = ref('');
const giftIcon = '/static/icons/gift.svg'; // 通用礼物图标，不标注具体点数

// 兑换说明列表
const instructions = [
  '兑换码可通过官方渠道获取',
  '每个兑换码仅可使用一次',
  '兑换成功后，相应权益将立即生效',
  '如遇问题，请联系客服'
];

/**
 * 验证兑换码是否有效
 * @returns {boolean} - 兑换码是否有效
 */
const isValidCode = computed(() => {
  return redemptionCode.value && redemptionCode.value.length === REDEEM_CODE_LENGTH;
});

/**
 * 处理输入事件
 * @param {Event} e - 输入事件对象
 */
const handleInput = (e) => {
  redemptionCode.value = e.detail.value.trim();
};

/**
 * 清除输入
 */
const clearInput = () => {
  redemptionCode.value = '';
};

/**
 * 刷新用户信息
 */
const refreshUserInfo = async () => {
  try {
    const response = await getCurrentTeacher();
    if (response.data) {
      userStore.setUserInfo(response.data);
      userInfo.value = userStore.userInfo;
    }
  } catch (error) {
    console.error('刷新用户信息失败:', error);
  }
};

/**
 * 显示提示信息
 * @param {string} title - 提示内容
 * @param {string} icon - 提示图标，默认为none
 */
const showToast = (title, icon = 'none') => {
  uni.showToast({ title, icon });
};

/**
 * 处理兑换逻辑
 */
const handleRedeem = async () => {
  if (!isValidCode.value) {
    showToast('请输入有效的兑换码');
    return;
  }

  try {
    uni.showLoading({ title: '兑换中...' });

    await useRedeemCode({ code: redemptionCode.value });
    await refreshUserInfo();
    
    redemptionCode.value = '';
    uni.showModal({
      title: '兑换成功',
      content: '兑换码已成功使用，相应权益已生效',
      showCancel: false,
      confirmText: '我知道了',
      success: () => {
        uni.navigateBack();
      }
    });
    uni.navigateBack()
  } catch (error) {
    showToast(error.message || '兑换失败，请检查兑换码是否正确');
    console.error('兑换失败:', error);
  } finally {
    uni.hideLoading();
  }
};

/**
 * 跳转到兑换记录页面
 */
const navigateToRedeemRecord = () => {
  uni.navigateTo({ url: '/pages/redeem/record' });
};

// 页面加载时执行
onLoad(() => {
  refreshUserInfo();
});
</script>

<style lang="scss">
// 定义变量
$primary-color: #4775EB;
$background-color: #F5F7FF;
$text-primary: #333333;
$text-secondary: #666666;
$white: #FFFFFF;
$shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
$border-radius-lg: 24rpx;
$border-radius-md: 16rpx;
$spacing-lg: 32rpx;
$spacing-md: 24rpx;
$spacing-sm: 16rpx;
$spacing-xs: 12rpx;
$spacing-xxs: 8rpx;

// 主容器样式
.redeem-container {
  min-height: 100vh;
  background-color: $background-color;
  padding: $spacing-md;

  // 兑换码输入区域
  .redeem-input-section {
    background-color: $white;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    margin-bottom: $spacing-md;
    box-shadow: $shadow;

    .input-title {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-md;

      .title-icon {
        width: 36rpx;
        height: 36rpx;
        margin-right: $spacing-xs;
      }

      .title-text {
        font-size: 32rpx;
        font-weight: 600;
        color: $text-primary;
      }

      .flex-spacer {
        flex: 1;
      }

      .record-link {
        display: flex;
        align-items: center;
        padding: $spacing-xxs $spacing-sm;
        background-color: $background-color;
        border-radius: $border-radius-md;

        .record-text {
          font-size: 24rpx;
          color: #666666;
          margin-right: 8rpx;
        }

        .arrow-icon {
          width: 24rpx;
          height: 24rpx;
        }
      }
    }

    .input-container {
      position: relative;
      margin-bottom: 16rpx;

      .redeem-input {
        width: 100%;
        height: 88rpx;
        background-color: #F5F7FF;
        border-radius: 16rpx;
        padding: 0 88rpx 0 24rpx;
        font-size: 32rpx;
        color: #333333;
        box-sizing: border-box;
      }

      .clear-btn {
        position: absolute;
        right: 24rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;

        .clear-icon {
          width: 32rpx;
          height: 32rpx;
          display: block;
        }
      }
    }

    .input-tip {
      font-size: 24rpx;
      color: #999999;
    }
  }

  // 兑换按钮
  .redeem-btn {
    height: 88rpx;
    background-color: #CCCCCC;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 32rpx;
    transition: all 0.3s;

    text {
      font-size: 32rpx;
      font-weight: 600;
      color: #FFFFFF;
    }

    &-active {
      background-color: #3c77ef;
      box-shadow: 0 8rpx 16rpx rgba(60, 119, 239, 0.3);
    }
  }

  // 兑换说明
  .redeem-instructions {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

    .instruction-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 24rpx;
    }

    .instruction-item {
      display: flex;
      margin-bottom: 16rpx;

      .item-dot {
        font-size: 24rpx;
        color: #3c77ef;
        margin-right: 12rpx;
      }

      .item-text {
        font-size: 24rpx;
        color: #666666;
        flex: 1;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
