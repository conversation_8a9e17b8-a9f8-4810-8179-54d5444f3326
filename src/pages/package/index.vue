<template>
  <view class="membership-container">
    <!-- 功能亮点 -->
    <view class="feature-highlight">
      <view class="feature-left">
        <text class="feature-text">AI 智能批改不限次</text>
        <view class="limited-offer">
          <image :src="lightningIcon" class="lightning-icon"></image>
          <text class="offer-text">限时特惠</text>
        </view>
      </view>
      <view class="record-link" @click="handleRechargeRecord">
        <text class="record-text">订阅记录</text>
        <image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
      </view>
    </view>

    <!-- 订阅选项 -->
    <view class="subscription-options">
      <view 
        v-for="(plan, index) in plans" 
        :key="plan.id"
        class="subscription-card" 
        :class="{ 
          'selected': selectedPlan === index,
          'annual': plan.isPopular
        }" 
        @click="selectPlan(index)"
      >
        <view class="card-content">
          <view class="card-left">
            <view class="card-title">
              <text>{{ plan.name }}</text>
              <text v-if="plan.isPopular" class="popular-text">90% 用户的选择</text>
            </view>
            <view class="card-price">
              <text class="price-value">¥{{ calculateMonthlyPrice(plan) }}</text>
              <text class="price-unit">/月</text>
            </view>
            <view class="card-details">
              <text class="total-price">总价: ¥{{ plan.price }} ({{ plan.validityDays }}天)</text>
              <text class="average-price">平均每天仅需 ¥{{ (plan.price / plan.validityDays).toFixed(2) }}</text>
            </view>
          </view>
          <view class="card-icon" :style="{ backgroundColor: plan.iconBgColor }">
            <image :src="plan.icon" class="icon-img"></image>
          </view>
        </view>
      </view>
    </view>

    <!-- 订阅按钮 -->
    <view class="subscribe-btn" @click="handleSubscribe">
      <text>立即订阅</text>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import {useUserStore} from '@/store/user';
import {getPackageList} from '@/api/package';
import {getCurrentTeacher} from "@/api/teacher";
import {createOrder, submitPayOrder} from "@/api/order";
import { formatDateYMD } from '@/utils/tools';
import { onLoad } from "@dcloudio/uni-app";

const userStore = useUserStore();

// 用户信息
const userInfo = ref(userStore.userInfo);

// 网络图片资源
const avatarUrl = ref(userInfo.value.avatar || 'https://cdn-icons-png.flaticon.com/512/1077/1077114.png');
// 本地图标
const lightningIcon = ref('/static/icons/lightning.svg');

// 选中的套餐索引
const selectedPlan = ref(2); // 默认选中年度会员

// 套餐数据
const plans = ref([]);

// 获取套餐数据
const fetchPlans = async () => {
  try {
    uni.showLoading({ title: '加载中...' });
    const { data } = await getPackageList({});
    plans.value = data.list
  } catch (error) {
    uni.showToast({
      title: '获取套餐信息失败',
      icon: 'none'
    });
    console.error('获取套餐信息失败:', error);
  } finally {
    uni.hideLoading();
  }
};

// 选择套餐
const selectPlan = (index) => {
  selectedPlan.value = index;
};

const calculateMonthlyPrice = (plan) => {
  return (plan.price / plan.validityMonths).toFixed(2);
};

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    const res = await getCurrentTeacher()
    userStore.setUserInfo(res.data)
    userInfo.value = userStore.userInfo
  } catch (error) {
    console.error('刷新用户信息失败:', error)
  }
}

// 处理订阅
const handleSubscribe = async () => {
  if (selectedPlan.value === -1) {
    uni.showToast({
      title: '请选择充值套餐',
      icon: 'none'
    })
    return
  }
  const selectedPackageData = plans.value[selectedPlan.value]

  try {
    uni.showLoading({
      title: '订单创建中'
    })

    // 创建订单
    const res = await createOrder({
      packageId: selectedPackageData.id
    })

    // 提交支付订单
    const payRes = await submitPayOrder({
      id: res.data.transactionId,
      channelCode: 'wx_lite',
      channelExtras: {
        openid: uni.getStorageSync('openid'),
      },
    })

    uni.hideLoading()

    if (payRes.code === 0 && payRes.data.status === 0) {
      // 解析支付参数
      const payParams = JSON.parse(payRes.data.displayContent)

      // 调用微信支付
      uni.requestPayment({
        provider: 'wxpay',
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.packageValue,
        signType: payParams.signType,
        paySign: payParams.paySign,
        success: async (res) => {
          console.log('支付成功', res)
          // 刷新用户信息
          await refreshUserInfo()
          uni.showModal({
            title: '提示',
            content: '订阅成功',
            showCancel: false,
            success: () => {
              uni.navigateBack()
            }
          })
        },
        fail: (err) => {
          console.log('支付失败', err)
          uni.showToast({
            title: '支付已取消',
            icon: 'none'
          })
        }
      })
    } else {
      uni.showToast({
        title: payRes.msg || '支付参数异常',
        icon: 'none'
      })
    }
  } catch (e) {
    console.log(e, 'e')
    uni.hideLoading()
    uni.showToast({
      title: '充值失败',
      icon: 'none'
    })
  }
};

const handleRechargeRecord = () => {
  uni.navigateTo({
    url: '/pages/package/record'
  })
}

// 页面加载时获取套餐数据
onLoad(() => {
  fetchPlans();
  // 更新用户信息
  userInfo.value = userStore.userInfo;
  avatarUrl.value = userInfo.value.avatar || 'https://cdn-icons-png.flaticon.com/512/1077/1077114.png';
});
</script>

<style lang="scss" scoped>
.membership-container {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding-bottom: 120rpx; /* 添加底部padding，防止内容被底部按钮遮挡 */
}

.feature-highlight {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  
  .feature-left {
    display: flex;
    align-items: center;
  }
  
  .feature-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-right: 16rpx;
  }
  
  .limited-offer {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    background-color: #fff0f0;
    border-radius: 16rpx;
    
    .lightning-icon {
      width: 28rpx;
      height: 28rpx;
    }
    
    .offer-text {
      font-size: 24rpx;
      color: #ff4d4f;
      margin-left: 6rpx;
    }
  }
}

.record-link {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: #F5F7FF;
  border-radius: 32rpx;

  .record-text {
    font-size: 26rpx;
    color: #3C77EF;
    margin-right: 8rpx;
  }

  .arrow-icon {
    width: 24rpx;
    height: 24rpx;
  }
}

.subscription-options {
  display: flex;
  flex-direction: column;
  padding: 0 32rpx;
  margin-bottom: 40rpx;
  
  .subscription-card {
    background-color: #ffffff;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    position: relative;
    border: 4rpx solid transparent;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &.selected {
      border-color: #2979FF;
      box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.15);
      transform: translateY(-4rpx);
    }
    
    &.annual {
      background-color: #f8f9ff;
      border-radius: 32rpx;

      &:not(.selected) {
        border-color: #e6e8f6;
      }
    }
    
    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-left {
        flex: 1;
        
        .card-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333333;
          margin-bottom: 16rpx;
        }
        
        .card-price {
          display: flex;
          align-items: baseline;
          margin-bottom: 16rpx;
          
          .price-value {
            font-size: 48rpx;
            font-weight: bold;
            color: #2979FF;
          }
          
          .price-unit {
            font-size: 28rpx;
            color: #666666;
            margin-left: 4rpx;
          }
        }
        
        .card-details {
          display: flex;
          flex-direction: column;
          margin-top: 4rpx;
          
          .total-price {
            font-size: 24rpx;
            color: #999999;
          }
          
          .average-price {
            margin-top: 16rpx;
            font-size: 24rpx;
            color: $uni-primary-color;
          }
        }
      }
      
      .card-icon {
        width: 96rpx;
        height: 96rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .icon-img {
          width: 64rpx;
          height: 64rpx;
        }
        
        &.graduation {
          background-color: #e6f7ff;
        }
        
        &.crown {
          background-color: #fff7e6;
        }
        
        &.piggy {
          background-color: #f0f2ff;
        }
      }
    }

    .popular-text {
      font-size: 24rpx;
      color: #cc312a;
      background-color: #fee1e5;
      border-radius: 24rpx;
      padding: 8rpx 24rpx;
      margin-left: 8rpx;
    }
  }
}

.subscribe-btn {
  position: fixed;
  bottom: 40rpx;
  left: 32rpx;
  right: 32rpx;
  height: 100rpx;
  background-color: #2979FF;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.2);
  
  text {
    font-size: 32rpx;
    font-weight: 500;
    color: #ffffff;
  }
}

/* 适配不同设备尺寸 */
@media screen and (min-height: 700px) {
  .subscription-options {
    margin-bottom: 60rpx;
  }
  
  .subscribe-btn {
    bottom: 60rpx;
  }
}
</style>
