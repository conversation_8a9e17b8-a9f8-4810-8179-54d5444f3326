<template>
  <view class="record-page">
    <view class="record-list">
      <view v-if="loading" class="loading">加载中...</view>
      <Empty v-else-if="records.length === 0" text="暂无已支付记录" />
      <view v-else>
        <view class="record-item" v-for="item in records" :key="item.id">
          <view class="record-content">
            <view class="record-info">
              <text class="record-name">{{ item.packageName || '未命名套餐' }}</text>
              <text class="record-time">订阅时间：{{ formatTime(item.createTime) }}</text>
            </view>
            <view class="record-right">
              <view class="payment-status success" v-if="item.status === 1">
                <text class="status-icon">✓</text>
                <text class="status-text">支付成功</text>
              </view>
              <text class="amount">¥{{ item.price }}</text>
            </view>
          </view>
        </view>
        <uni-load-more :status="loadMoreStatus" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Empty from '@/components/Empty.vue'
import { getOrderList } from '@/api/order'
import { onPullDownRefresh, onReachBottom, onShow } from "@dcloudio/uni-app"

const loading = ref(false)
const records = ref([])
const loadMoreStatus = ref('more')
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)

const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

const fetchRecords = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1
    hasMore.value = true
  }

  if (!hasMore.value) return

  loading.value = true
  loadMoreStatus.value = 'loading'

  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      status: 1,
    }
    const res = await getOrderList(params)

    const { list, total } = res.data
    records.value = isRefresh ? list : [...records.value, ...list]
    hasMore.value = records.value.length < total
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore'
    page.value++
  } catch (error) {
    console.error('获取充值记录失败:', error)
    uni.showToast({
      title: '获取充值记录失败',
      icon: 'none'
    })
    loadMoreStatus.value = 'more'
  } finally {
    loading.value = false
  }
}

// 下拉刷新
onPullDownRefresh(async () => {
  await fetchRecords(true)
  uni.stopPullDownRefresh()
})

// 触底加载更多
onReachBottom(() => {
  if (!loading.value && hasMore.value) {
    fetchRecords()
  }
})

// 页面显示时刷新数据
onShow(() => {
  fetchRecords(true)
})

onMounted(() => {
  fetchRecords(true)
})
</script>

<style lang="scss">
.record-page {
  min-height: 100vh;
  background-color: #F2F2F7;
  padding: 24rpx;

  .record-list {
    .loading {
      text-align: center;
      padding: 40rpx 0;
      color: #8E8E93;
      font-size: 28rpx;
    }

    .record-item {
      background-color: #FFFFFF;
      border-radius: 24rpx;
      padding: 32rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
      overflow: hidden;
      
      .record-content {
        display: flex;
        justify-content: space-between;
        width: 100%;
      }

      .record-info {
        flex: 1;
        
        .record-name {
          font-size: 38rpx;
          color: #000000;
          font-weight: 600;
          margin-bottom: 16rpx;
          display: block;
        }

        .record-time {
          font-size: 26rpx;
          color: #8E8E93;
          display: block;
        }
      }
      
      .record-right {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        justify-content: space-between;
        height: 100%;
        
        .payment-status {
          display: inline-flex;
          align-items: center;
          padding: 4rpx 10rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          margin-bottom: 16rpx;
          
          &.success {
            background-color: rgba(52, 199, 89, 0.1);
            color: #34C759;
            
            .status-icon {
              margin-right: 4rpx;
              font-weight: bold;
            }
          }
        }
        
        .amount {
          font-size: 36rpx;
          color: #FF6B00;
          font-weight: 600;
        }
      }
    } 
  }
}
</style>