<template>
  <view class="add-standard-button" @click="handleClick">
    <view class="add-btn">
      <text class="add-icon">+</text>
      <text class="add-text">添加标准</text>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['click']);

const handleClick = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.add-standard-button {
  position: fixed;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 100;

  .add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 96rpx;
    background-color: #007AFF;
    color: #fff;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
      opacity: 0.9;
    }

    .add-icon {
      margin-right: 12rpx;
      font-size: 40rpx;
      font-weight: 300;
      line-height: 1;
    }

    .add-text {
      color: #fff;
      font-size: 32rpx;
    }
  }
}
</style>
