<template>
  <uni-popup ref="popup" type="bottom" :is-mask-click="true" @maskClick="handleClose">
    <view class="template-modal">
      <view class="modal-header">
        <text class="modal-title">选择模板</text>
        <view class="close-btn" @click="handleClose">
          <uni-icons type="close" size="20" color="#666"></uni-icons>
        </view>
      </view>
      
      <view class="template-list">
        <view v-if="loading" class="loading-state">
          <uni-icons type="spinner-cycle" size="40" color="#007aff"></uni-icons>
          <text class="loading-text">加载模板中...</text>
        </view>

        <view v-else-if="templateList.length === 0" class="empty-state">
          <text class="empty-text">暂无可用模板</text>
        </view>

        <view v-else>
          <view
            v-for="template in templateList"
            :key="template.id"
            class="template-item"
            @click="selectTemplate(template)"
          >
            <view class="template-info">
              <text class="template-name">{{ template.name }}</text>
              <text class="template-desc">{{ template.description }}</text>
            </view>
          </view>
        </view>
      </view>
      

    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { getRubricTemplates } from '@/api/rubric';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show', 'select']);

const popup = ref(null);
const loading = ref(false);

// 模板列表
const templateList = ref([]);

// 获取模板列表
const fetchTemplates = async () => {
  try {
    loading.value = true;
    const res = await getRubricTemplates();

    if (res.code === 0 && res.data) {
      templateList.value = res.data.map(item => ({
        id: item.id,
        name: item.title,
        title: item.title,
        totalScore: item.totalScore,
        description: `总分：${item.totalScore}分`,
        content: item.content
      }));
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    templateList.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    popup.value?.open();
    if (templateList.value.length === 0) {
      fetchTemplates();
    }
  } else {
    popup.value?.close();
  }
});

// 组件挂载时获取模板
onMounted(() => {
  fetchTemplates();
});

// 选择模板
const selectTemplate = (template) => {
  // 直接应用选中的模板
  emit('select', template);
  handleClose();
};



// 关闭弹窗
const handleClose = () => {
  emit('update:show', false);
};
</script>

<style lang="scss" scoped>
.template-modal {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 200;
  touch-action: none;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      padding: 8rpx;
    }
  }

  .template-list {
    flex: 1;
    padding: 24rpx 32rpx 32rpx 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;

      .loading-text {
        margin-top: 24rpx;
        font-size: 28rpx;
        color: #666;
      }
    }

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }

    .template-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      border: 2rpx solid #f0f0f0;
      border-radius: 12rpx;
      margin-bottom: 16rpx;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
        background-color: #f0f8ff;
        border-color: #007aff;
      }

      .template-info {
        flex: 1;

        .template-name {
          display: block;
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }

        .template-desc {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }


}
</style>
