<template>
  <uni-popup ref="popup" type="center" :is-mask-click="false">
    <view class="ai-optimize-modal">
      <view class="modal-header">
        <text class="modal-title">AI润色</text>
        <view class="close-btn" @click="handleClose">
          <uni-icons type="close" size="20" color="#666"></uni-icons>
        </view>
      </view>
      
      <view class="modal-content">
        <view v-if="isLoading" class="loading-state">
          <view v-if="!isCountdownFinished" class="countdown-container">
            <text class="countdown-number">{{ countdown }}</text>
            <text class="countdown-unit">秒</text>
          </view>
          <view v-else class="waiting-container">
            <view class="waiting-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
          </view>
          <text class="loading-text">{{ isCountdownFinished ? '请稍候，正在处理中...' : 'AI正在优化中...' }}</text>
        </view>
        
        <view v-else class="content-container">
          <view class="content-section">
            <text class="section-title">原始内容</text>
            <view class="content-box original">
              <text class="content-text">{{ content }}</text>
            </view>
          </view>
          
          <view class="content-section">
            <text class="section-title">优化后内容</text>
            <view class="content-box optimized">
              <text class="content-text">{{ optimizedContent }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="action-btn cancel-btn" @click="handleCancel">
          {{ isLoading ? '取消' : '重新润色' }}
        </button>
        <button 
          class="action-btn confirm-btn" 
          @click="handleConfirm"
          :disabled="isLoading || !optimizedContent"
        >
          {{ isLoading ? '润色中...' : '确认采用' }}
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch } from 'vue';
import { polishRubricWithAI } from '@/api/rubric';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  totalScore: {
    type: [String, Number],
    default: ''
  }
});

const emit = defineEmits(['update:show', 'confirm']);

const popup = ref(null);
const isLoading = ref(false);
const optimizedContent = ref('');
const countdown = ref(20);
const isCountdownFinished = ref(false);
let countdownTimer = null;

// 监听显示状态
watch(() => props.show, (newVal) => {
  if (newVal) {
    popup.value?.open();
    startOptimize();
  } else {
    popup.value?.close();
    resetState();
  }
});

// 重置状态
const resetState = () => {
  isLoading.value = false;
  optimizedContent.value = '';
  countdown.value = 20;
  isCountdownFinished.value = false;
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

// 开始AI优化
const startOptimize = async () => {
  isLoading.value = true;
  countdown.value = 20;
  isCountdownFinished.value = false;

  // 启动倒计时
  countdownTimer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      isCountdownFinished.value = true;
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  }, 1000);

  try {
    // 将标题、总分和内容拼接成一个完整的字符串
    const fullContent = `标准名称：${props.title || ''}

总分：${props.totalScore || ''}分

标准内容：
${props.content || ''}`;

    const res = await polishRubricWithAI({
      content: fullContent
    });

    if (res.code === 0 && res.data && res.data.data) {
      // 处理API返回的数据结构 - res.data.data 是JSON字符串
      let content = '';
      const apiDataString = res.data.data;

      try {
        // 解析JSON字符串
        const apiData = JSON.parse(apiDataString);
        content = apiData.output || apiDataString;
      } catch (parseError) {
        // 如果解析失败，直接使用原始字符串
        console.warn('解析API数据失败:', parseError);
        content = apiDataString;
      }

      optimizedContent.value = content;
    } else {
      throw new Error(res.msg || res.data?.msg || 'AI优化失败');
    }
  } catch (error) {
    console.error('AI优化失败:', error);
    uni.showToast({
      title: error.message || 'AI优化失败',
      icon: 'none'
    });

    // API失败时关闭弹窗
    setTimeout(() => {
      emit('update:show', false);
    }, 1500);
  } finally {
    isLoading.value = false;
    if (countdownTimer) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  }
};

// 确认采用
const handleConfirm = () => {
  if (optimizedContent.value) {
    emit('confirm', optimizedContent.value);
    handleClose();
  }
};

// 处理取消/重新润色
const handleCancel = () => {
  if (isLoading.value) {
    // 如果正在加载，询问是否取消
    uni.showModal({
      title: '提示',
      content: 'AI正在润色中，确定要取消吗？',
      success: (res) => {
        if (res.confirm) {
          emit('update:show', false);
        }
      }
    });
  } else {
    // 如果不在加载状态，重新开始润色
    if (optimizedContent.value) {
      startOptimize();
    } else {
      handleClose();
    }
  }
};

// 关闭弹窗
const handleClose = () => {
  if (isLoading.value) {
    // 如果正在加载，询问是否取消
    uni.showModal({
      title: '提示',
      content: 'AI正在润色中，确定要取消吗？',
      success: (res) => {
        if (res.confirm) {
          emit('update:show', false);
        }
      }
    });
  } else {
    emit('update:show', false);
  }
};
</script>

<style lang="scss" scoped>
.ai-optimize-modal {
  width: 90vw;
  max-width: 600rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 200;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      padding: 8rpx;
    }
  }

  .modal-content {
    flex: 1;
    padding: 24rpx 32rpx;
    max-height: 60vh;
    overflow-y: auto;

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 0;

      .countdown-container {
        display: flex;
        align-items: baseline;
        justify-content: center;
        margin-bottom: 24rpx;

        .countdown-number {
          font-size: 72rpx;
          font-weight: 700;
          color: #007aff;
          line-height: 1;
        }

        .countdown-unit {
          font-size: 32rpx;
          color: #007aff;
          margin-left: 8rpx;
          font-weight: 500;
        }
      }

      .waiting-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 24rpx;
        height: 72rpx;

        .waiting-dots {
          display: flex;
          gap: 8rpx;

          .dot {
            width: 12rpx;
            height: 12rpx;
            border-radius: 50%;
            background-color: #007aff;
            animation: pulse 1.4s ease-in-out infinite both;

            &:nth-child(1) {
              animation-delay: -0.32s;
            }

            &:nth-child(2) {
              animation-delay: -0.16s;
            }

            &:nth-child(3) {
              animation-delay: 0s;
            }
          }
        }
      }

      .loading-text {
        font-size: 28rpx;
        color: #666;
      }
    }

    .content-container {
      .content-section {
        margin-bottom: 32rpx;

        .section-title {
          display: block;
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 16rpx;
        }

        .content-box {
          padding: 20rpx;
          border-radius: 12rpx;
          border: 1rpx solid #e9ecef;
          min-height: 200rpx;

          &.original {
            background-color: #f8f9fa;
          }

          &.optimized {
            background-color: #f0f8ff;
            border-color: #007aff;
          }

          .content-text {
            font-size: 26rpx;
            line-height: 1.6;
            color: #333;
            word-break: break-all;
          }
        }
      }
    }
  }

  .modal-actions {
    display: flex;
    gap: 16rpx;
    padding: 24rpx 32rpx;
    border-top: 1rpx solid #f0f0f0;

    .action-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 12rpx;
      font-size: 30rpx;
      font-weight: 600;
      border: none;

      &.cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }

      &.confirm-btn {
        background-color: #007aff;
        color: #fff;

        &:disabled {
          background-color: #ccc;
          color: #999;
        }
      }

      &:active:not(:disabled) {
        opacity: 0.8;
      }
    }
  }
}

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
