<template>
  <view class="standard-item" @click="handleClick">
    <view class="standard-content">
      <view class="standard-header">
        <text class="standard-name">{{ standardInfo.title || standardInfo.name }}</text>
      </view>
      <view class="standard-meta">
        <text class="standard-score">{{ standardInfo.totalScore || standardInfo.score }}分</text>
        <text class="standard-time">{{ standardInfo.createTime }}</text>
      </view>
    </view>
    <view class="standard-arrow">
      <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  standardInfo: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['click']);

const handleClick = () => {
  emit('click', props.standardInfo);
};
</script>

<style lang="scss" scoped>
.standard-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;

  &:active {
    background-color: #f8f9fa;
    transform: scale(0.98);
  }

  .standard-content {
    flex: 1;

    .standard-header {
      margin-bottom: 12rpx;

      .standard-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }
    }

    .standard-meta {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .standard-score {
        font-size: 26rpx;
        color: #007aff;
        font-weight: 500;
      }

      .standard-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .standard-arrow {
    margin-left: 16rpx;
  }
}
</style>
