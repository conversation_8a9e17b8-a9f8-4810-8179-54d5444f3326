<template>
  <view class="standard-manage">
    <!-- 空状态 -->
    <Empty v-if="!loading && standardList.length === 0" text="暂无批改标准" />
    <block v-else>
      <view class="standard-list">
        <standard-item
          v-for="item in standardList"
          :key="item.id"
          :standard-info="item"
          @click="handleStandardClick"
        />
      </view>
      <!-- 加载更多 -->
      <uni-load-more :status="loadMoreStatus" />
    </block>

    <!-- 添加标准按钮 -->
    <add-standard-button @click="handleAddStandard" />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onPullDownRefresh, onReachBottom, onShow } from "@dcloudio/uni-app";
import Empty from "@/components/Empty.vue";
import StandardItem from './components/StandardItem.vue';
import AddStandardButton from './components/AddStandardButton.vue';
import { getRubricList } from '@/api/rubric';

// 标准列表数据
const standardList = ref([]);

const loading = ref(false);
const loadMoreStatus = ref('more');
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);

// 获取标准列表
const fetchStandardList = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1;
    standardList.value = [];
    hasMore.value = true;
  }

  if (!hasMore.value) return;

  loading.value = true;
  loadMoreStatus.value = 'loading';

  try {
    const params = {
      pageNo: page.value,
      pageSize: pageSize.value
    };

    const res = await getRubricList(params);

    if (res.code === 0 && res.data) {
      const newList = res.data.list || [];

      if (isRefresh) {
        standardList.value = newList;
      } else {
        standardList.value = [...standardList.value, ...newList];
      }

      // 检查是否还有更多数据
      const total = res.data.total || 0;
      const currentTotal = standardList.value.length;
      hasMore.value = currentTotal < total;

      if (hasMore.value) {
        loadMoreStatus.value = 'more';
        page.value++;
      } else {
        loadMoreStatus.value = 'noMore';
      }
    } else {
      loadMoreStatus.value = 'noMore';
    }
  } catch (error) {
    console.error('获取标准列表失败:', error);
    uni.showToast({
      title: '获取标准列表失败',
      icon: 'none'
    });
    loadMoreStatus.value = 'more';
  } finally {
    loading.value = false;
  }
};

// 处理标准点击事件
const handleStandardClick = (standardInfo) => {
  uni.navigateTo({
    url: `/pages/standard-manage/detail?id=${standardInfo.id}`
  });
};

// 处理添加标准点击事件
const handleAddStandard = () => {
  uni.navigateTo({
    url: '/pages/standard-manage/detail'
  });
};



// 下拉刷新
onPullDownRefresh(async () => {
  await fetchStandardList(true);
  uni.stopPullDownRefresh();
});

// 触底加载更多
onReachBottom(() => {
  if (!loading.value && hasMore.value) {
    fetchStandardList();
  }
});

// 页面显示时刷新数据
onShow(() => {
  fetchStandardList(true);
});
</script>

<style lang="scss">
page {
  background-color: #f8f9fa;
}
</style>

<style lang="scss" scoped>
.standard-manage {
  padding-top: 15rpx;
  padding-bottom: 150rpx;

  .standard-list {
    padding: 15rpx 30rpx;
  }
}
</style>
