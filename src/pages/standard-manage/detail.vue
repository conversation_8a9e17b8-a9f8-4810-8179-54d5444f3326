<template>
  <view class="standard-detail">
    <view class="content">
      <!-- 标准名称 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">标准名称<text class="required">*</text></text>
          <view class="form-input-box">
            <input
              class="form-input"
              v-model="formData.title"
              placeholder="请输入标准名称"
              :maxlength="100"
            />
          </view>
        </view>
      </view>

      <!-- 总分 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">总分<text class="required">*</text></text>
          <view class="form-input-box">
            <input
              class="form-input"
              v-model="formData.totalScore"
              placeholder="请输入总分"
              type="number"
            />
          </view>
        </view>
      </view>

      <!-- 标准内容 -->
      <view class="form-section">
        <view class="form-item">
          <text class="form-label">标准内容<text class="required">*</text></text>
          <view class="form-input-box">
            <textarea
              class="content-textarea"
              v-model="formData.content"
              placeholder="请输入标准内容"
              auto-height
              :maxlength="5000"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-actions">
      <!-- 操作按钮行 -->
      <view class="action-row">
        <view class="action-btn template-btn" @click="showTemplateModal">
          使用模板
        </view>
        <view class="action-btn upload-btn" @click="handleUpload">
          拍照上传
        </view>
        <view class="action-btn ai-btn" @click="handleAIOptimize">
          AI润色
        </view>
      </view>

      <!-- 主要按钮行 -->
      <view class="main-btn-row">
        <view v-if="standardId" class="footer-btn delete-btn" @click="handleDelete">
          删除
        </view>
        <view
          class="footer-btn save-btn"
          :class="{ disabled: isSaving }"
          @click="handleSave"
        >
          {{ isSaving ? '保存中...' : '保存' }}
        </view>
      </view>
    </view>

    <!-- 模板选择弹窗 -->
    <template-modal
      v-model:show="showTemplate"
      @select="handleTemplateSelect"
    />

    <!-- AI润色弹窗 -->
    <ai-optimize-modal
      v-model:show="showAIModal"
      :title="formData.title"
      :total-score="formData.totalScore"
      :content="formData.content"
      @confirm="handleAIConfirm"
    />
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue';
import TemplateModal from './components/TemplateModal.vue';
import AiOptimizeModal from './components/AiOptimizeModal.vue';
import { onLoad } from "@dcloudio/uni-app";
import { getRubricDetail, createRubric, updateRubric, deleteRubric } from '@/api/rubric';
import { aiCompletions } from '@/api/task-item';
import { choseAndUploadImage } from '@/libs/utils';

// 页面参数
const standardId = ref(null);

// 表单数据
const formData = reactive({
  id: null,
  title: '',
  totalScore: '',
  content: ''
});

// 弹窗状态
const showTemplate = ref(false);
const showAIModal = ref(false);

// 保存状态
const isSaving = ref(false);

// 页面加载
onLoad((options) => {
  standardId.value = options.id;

  if (standardId.value) {
    loadStandardDetail();
  }
});

// 加载标准详情
const loadStandardDetail = async () => {
  try {
    uni.showLoading({ title: '加载中...' });

    const res = await getRubricDetail(standardId.value);

    if (res.code === 0 && res.data) {
      const data = res.data;
      formData.id = data.id;
      formData.title = data.title || '';
      formData.totalScore = data.totalScore || '';
      formData.content = data.content || '';
    } else {
      throw new Error(res.msg || '获取详情失败');
    }
  } catch (error) {
    console.error('加载标准详情失败:', error);
    uni.showToast({
      title: error.message || '加载失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 显示模板选择
const showTemplateModal = () => {
  showTemplate.value = true;
};

// 处理模板选择
const handleTemplateSelect = (template) => {
  // 应用模板的标题、总分和内容
  formData.title = template.title || template.name || '';
  formData.totalScore = template.totalScore || '';
  formData.content = template.content || '';

  uni.showToast({
    title: '模板已应用',
    icon: 'success'
  });
};

// 处理拍照上传
const handleUpload = async () => {
  try {
    // 显示上传提示
    uni.showLoading({
      title: '正在上传图片...',
      mask: true
    });

    // 选择并上传图片
    const uploadResult = await choseAndUploadImage({
      count: 1,
      sourceType: ['camera']
    });

    const imageUrl = uploadResult.data.ossUrl;

    // 显示识别提示
    uni.showLoading({
      title: '正在识别文字...',
      mask: true
    });

    // 调用AI接口识别文字
    const aiResult = await aiCompletions({
      prompt: '请识别并提取图片中的文字内容，保持原有的格式和结构，不要使用 markdown,使用空格换行就行',
      imageUrl: imageUrl
    });

    uni.hideLoading();

    if (aiResult.code === 0 && aiResult.data) {
      // 根据实际响应结构获取识别的文字内容
      const recognizedText = aiResult.data.output || aiResult.data.content || aiResult.data;

      if (recognizedText && recognizedText.trim()) {
        // 清空已输入的内容，直接设置识别的文字
        formData.content = recognizedText.trim();

        uni.showToast({
          title: '文字识别完成',
          icon: 'success'
        });
      } else {
        uni.showToast({
          title: '未识别到文字内容',
          icon: 'none'
        });
      }
    } else {
      throw new Error(aiResult.msg || '文字识别失败');
    }
  } catch (error) {
    uni.hideLoading();
    console.error('上传识别失败:', error);

    let errorMessage = '操作失败';
    if (error.message) {
      if (error.message.includes('选择图片失败')) {
        errorMessage = '图片选择失败';
      } else if (error.message.includes('上传失败')) {
        errorMessage = '图片上传失败';
      } else if (error.message.includes('识别失败')) {
        errorMessage = '文字识别失败';
      } else {
        errorMessage = error.message;
      }
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none'
    });
  }
};

// 处理AI润色
const handleAIOptimize = () => {
  // 只验证标题和总分是否输入
  if (!formData.title.trim()) {
    uni.showToast({
      title: '请先输入标准名称',
      icon: 'none'
    });
    return;
  }

  if (formData.title.trim().length > 100) {
    uni.showToast({
      title: '标准名称不能超过100个字符',
      icon: 'none'
    });
    return;
  }

  if (!formData.totalScore) {
    uni.showToast({
      title: '请先输入总分',
      icon: 'none'
    });
    return;
  }

  const totalScore = Number(formData.totalScore);
  if (!Number.isInteger(totalScore) || totalScore <= 0 || totalScore > 100) {
    uni.showToast({
      title: '总分必须是1-100之间的正整数',
      icon: 'none'
    });
    return;
  }

  showAIModal.value = true;
};

// 处理AI润色确认
const handleAIConfirm = (optimizedContent) => {
  formData.content = optimizedContent;
  uni.showToast({
    title: 'AI润色完成',
    icon: 'success'
  });
};

// 处理保存
const handleSave = async () => {
  // 如果正在保存，直接返回
  if (isSaving.value) {
    return;
  }

  // 表单验证
  if (!formData.title.trim()) {
    uni.showToast({
      title: '请输入标准名称',
      icon: 'none'
    });
    return;
  }

  if (formData.title.trim().length > 100) {
    uni.showToast({
      title: '标准名称不能超过100个字符',
      icon: 'none'
    });
    return;
  }

  if (!formData.totalScore) {
    uni.showToast({
      title: '请输入总分',
      icon: 'none'
    });
    return;
  }

  const totalScore = Number(formData.totalScore);
  if (!Number.isInteger(totalScore) || totalScore <= 0 || totalScore > 100) {
    uni.showToast({
      title: '总分必须是1-100之间的正整数',
      icon: 'none'
    });
    return;
  }

  if (!formData.content.trim()) {
    uni.showToast({
      title: '请输入标准内容',
      icon: 'none'
    });
    return;
  }

  try {
    // 设置保存状态
    isSaving.value = true;

    uni.showLoading({
      title: standardId.value ? '保存中...' : '创建中...'
    });

    const apiData = {
      title: formData.title,
      totalScore: parseInt(formData.totalScore),
      content: formData.content
    };

    // 如果有ID，添加到请求数据中
    if (standardId.value && formData.id) {
      apiData.id = formData.id;
    }

    let res;
    if (standardId.value) {
      res = await updateRubric(apiData);
    } else {
      res = await createRubric(apiData);
    }

    if (res.code === 0) {
      uni.showModal({
        title: '操作成功',
        content: standardId.value ? '保存成功！' : '创建成功！',
        showCancel: false,
        confirmText: '确认',
        success: (modalRes) => {
          if (modalRes.confirm) {
            uni.navigateBack();
          }
        }
      });
    } else {
      throw new Error(res.msg || '保存失败');
    }
  } catch (error) {
    console.error('保存失败:', error);
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
    // 重置保存状态
    isSaving.value = false;
  }
};

// 处理删除
const handleDelete = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个标准吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' });

          const deleteRes = await deleteRubric(standardId.value);

          if (deleteRes.code === 0) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            });

            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            throw new Error(deleteRes.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          uni.showToast({
            title: error.message || '删除失败',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.standard-detail {
  background-color: #ffffff;
  min-height: 100vh;
  padding-bottom: 200rpx;

  .content {
    padding: 60rpx 32rpx 60rpx;
  }

  // 简约表单样式 - 无卡片包装
  .form-section {
    margin-bottom: 40rpx;

    .form-item {
      .form-label {
        font-size: 30rpx;
        color: #1d1d1f;
        font-weight: 600;
        margin-bottom: 20rpx;
        display: block;
        letter-spacing: -0.3rpx;

        .required {
          color: #ff3b30;
          margin-left: 4rpx;
          font-weight: 400;
        }
      }

      .form-input-box {
        background-color: #f2f2f7;
        border-radius: 14rpx;
        border: none;
        transition: all 0.2s ease;
        overflow: hidden;

        &:focus-within {
          background-color: #ffffff;
          box-shadow: 0 0 0 2rpx rgba(0, 122, 255, 0.15);
        }

        .form-input {
          height: 88rpx;
          padding: 0 28rpx;
          width: 100%;
          font-size: 28rpx;
          color: #1d1d1f;
          background-color: transparent;
          border: none;
          line-height: 1.4;
          font-weight: 400;
          box-sizing: border-box;

          &::placeholder {
            color: #8e8e93;
            font-weight: 400;
          }

          &:focus {
            padding: 0 28rpx;
            outline: none;
          }

          &:disabled {
            color: #8e8e93;
            background-color: #f2f2f7;
          }
        }

        .content-textarea {
          width: 100%;
          min-height: 520rpx;
          padding: 28rpx;
          font-size: 28rpx;
          color: #1d1d1f;
          background-color: transparent;
          border: none;
          line-height: 1.6;
          font-weight: 400;
          box-sizing: border-box;

          &::placeholder {
            color: #8e8e93;
            font-weight: 400;
          }

          &:focus {
            padding: 28rpx;
            outline: none;
          }

          &:disabled {
            color: #8e8e93;
            background-color: #f2f2f7;
          }
        }
      }


    }
  }

  // 底部按钮 - 简约风格
  .footer-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 32rpx;
    padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
    border-top: 1rpx solid rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(40rpx);
    z-index: 2;

    // 操作按钮行
    .action-row {
      display: flex;
      gap: 12rpx;
      margin-bottom: 24rpx;

      .action-btn {
        flex: 1;
        min-width: 0;
        height: 72rpx;
        font-size: 24rpx;
        font-weight: 500;
        line-height: 72rpx;
        padding: 0 16rpx;
        border-radius: 12rpx;
        border: none;
        background-color: #f2f2f7;
        color: #007aff;
        text-align: center;
        transition: all 0.15s ease;
        letter-spacing: -0.2rpx;

        &:active {
          transform: scale(0.96);
          background-color: #e5e5ea;
        }

        &.template-btn {
          color: #007aff;
          &:active {
            background-color: rgba(0, 122, 255, 0.1);
          }
        }

        &.upload-btn {
          color: #34c759;
          &:active {
            background-color: rgba(52, 199, 89, 0.1);
          }
        }

        &.ai-btn {
          color: #af52de;
          &:active {
            background-color: rgba(175, 82, 222, 0.1);
          }
        }
      }
    }

    // 主要按钮行
    .main-btn-row {
      display: flex;
      gap: 20rpx;

      .footer-btn {
        flex: 1;
        height: 96rpx;
        line-height: 92rpx; // 减去边框高度，确保文字垂直居中
        border-radius: 16rpx;
        font-size: 30rpx;
        font-weight: 600;
        border: 2rpx solid transparent; // 给所有按钮添加透明边框，保持一致的盒模型
        text-align: center;
        transition: all 0.15s ease;
        letter-spacing: -0.3rpx;
        box-sizing: border-box; // 确保边框包含在总高度内

        &.delete-btn {
          background-color: rgba(255, 59, 48, 0.08);
          color: #ff3b30;
          border-color: rgba(255, 59, 48, 0.2);

          &:active {
            background-color: rgba(255, 59, 48, 0.15);
            border-color: rgba(255, 59, 48, 0.3);
            transform: scale(0.96);
          }
        }

        &.save-btn {
          background-color: #007aff;
          color: #ffffff;
          border-color: transparent; // 保存按钮保持透明边框

          &:active {
            background-color: #0056cc;
            transform: scale(0.96);
          }

          &.disabled {
            background-color: #c7c7cc;
            color: #ffffff;
            pointer-events: none;

            &:active {
              background-color: #c7c7cc;
              transform: none;
            }
          }
        }
      }
    }
  }
}
</style>
