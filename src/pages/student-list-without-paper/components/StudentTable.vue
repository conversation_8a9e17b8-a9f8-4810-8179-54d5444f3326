<template>
  <view class="student-list">
    <view class="table-wrapper">
      <!-- 表头 -->
      <view class="table-header">
        <view class="name-column">姓名</view>
        <view class="id-column">学号</view>
      </view>

      <!-- 学生列表内容 -->
      <view class="table-content">
        <student-item
          v-for="(student, index) in students"
          :key="student.id"
          :student="student"
          :index="index"
          :is-striped="index % 2 !== 0"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </view>
    </view>

    <!-- 空状态 -->
    <Empty v-if="!loading && students.length === 0" :text="emptyText"></Empty>
  </view>
</template>

<script setup>
import StudentItem from './StudentItem.vue';
import Empty from "@/components/Empty.vue";

const props = defineProps({
  students: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  emptyText: {
    type: String,
    default: '暂无学生信息'
  },
  loadMoreStatus: {
    type: String,
    default: 'more'
  }
});

const emit = defineEmits(['edit', 'delete']);

// 编辑学生
const handleEdit = (student, index) => {
  emit('edit', student, index);
};

// 删除学生
const handleDelete = (student, index) => {
  emit('delete', student, index);
};
</script>

<style lang="scss" scoped>
.student-list {
  background-color: #ffffff;
  margin: 20rpx 30rpx 120rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;

  .table-wrapper {
    width: 100%;
    overflow-x: auto;
  }

  .table-header {
    display: flex;
    padding: 24rpx 0;
    background-color: #f8f9fc;
    border-bottom: 2rpx solid #e8e8e8;
    font-size: 28rpx;
    color: #333;
    font-weight: 600;
  }

  .name-column {
    width: 30%;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
  }

  .id-column {
    width: 40%;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
  }

  .operation-column {
    width: 30%;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
