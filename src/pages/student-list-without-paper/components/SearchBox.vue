<template>
  <view class="search-box">
    <view class="search-input">
      <text class="uni-icons uni-icons-search"></text>
      <input
        type="text"
        :placeholder="placeholder"
        v-model="searchValue"
        @input="handleSearchInput"
        confirm-type="search"
      />
      <view
        v-if="searchValue"
        class="clear-button"
        @click="clearSearch"
      >
        <text class="uni-icons uni-icons-clear">✕</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';
import { debounce } from '@/utils/tools';

const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索'
  },
  debounceTime: {
    type: Number,
    default: 500
  }
});

const emit = defineEmits(['search', 'clear']);

// 搜索文本
const searchValue = ref('');

// 防抖处理搜索输入
const handleSearchInput = debounce(() => {
  emit('search', searchValue.value);
}, props.debounceTime);

// 清除搜索
const clearSearch = () => {
  searchValue.value = '';
  emit('clear');
};

// 对外暴露搜索值，便于父组件获取
defineExpose({
  searchValue
});
</script>

<style lang="scss" scoped>
.search-box {
  padding: 20rpx 30rpx;

  .search-input {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    border-radius: 8rpx;
    padding: 0 20rpx;
    height: 80rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
    position: relative;

    .uni-icons {
      margin-right: 10rpx;
      color: #999;
      font-size: 28rpx;
    }

    input {
      flex: 1;
      height: 100%;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }

    .clear-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 42rpx;
      height: 42rpx;
      background-color: #f0f0f0;
      border-radius: 50%;
      margin-left: 10rpx;
      padding: 0;
      transition: all 0.2s ease;

      &:active {
        background-color: #e0e0e0;
      }

      .uni-icons-clear {
        color: #999;
        font-size: 22rpx;
        margin: 0;
        line-height: 1;
      }
    }
  }
}
</style>