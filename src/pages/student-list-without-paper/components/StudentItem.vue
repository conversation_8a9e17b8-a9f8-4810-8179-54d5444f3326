<template>
  <view class="student-item" :class="{ 'striped': isStriped }" hover-class="student-item-hover">
    <view class="student-content">
      <view class="name-column">
        <text class="student-name">{{ student.name }}</text>
      </view>
      <view class="id-column">
        <text class="student-id">{{ student.studentNumber }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  student: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  isStriped: {
    type: Boolean,
    default: false
  }
});
</script>

<style lang="scss" scoped>
.student-item {
  position: relative;
  font-size: 28rpx;
  transition: background-color 0.2s;
  border-bottom: 1rpx solid #eeeeee;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.striped {
    background-color: #f9fafc;
  }
  
  &-hover {
    background-color: rgba(64, 128, 255, 0.05);
  }
  
  .student-content {
    display: flex;
    padding: 26rpx 0;
    align-items: center;
  }

  .name-column {
    width: 30%;
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    
    .student-name {
      font-weight: 500;
      color: #333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .id-column {
    width: 40%;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    
    .student-id {
      color: #666;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .operation-column {
    width: 30%;
    padding: 0 30rpx;

    .operation-buttons {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      
      .operation-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        position: relative;
      }
      
      .btn-hover {
        background-color: rgba(0, 0, 0, 0.08);
      }

      .edit-button {
        margin-right: 24rpx;
        
        .icon {
          width: 28rpx;
          height: 28rpx;
        }
      }

      .delete-button {
        .icon {
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
  }
}
</style>
