<template>
  <view class="student-list">
    <!-- 搜索框组件 - 移除ref，通过事件获取搜索值 -->
    <search-box
      placeholder="搜索学生姓名或学号"
      @search="handleSearch"
      @clear="clearSearch"
    />

    <!-- 空状态 -->
    <Empty v-if="!loading && studentList.length === 0" text="暂无学生数据" />

    <student-table
        v-else
        :students="studentList"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad, onPullDownRefresh } from '@dcloudio/uni-app';
import { getStudentsWithoutTaskItems } from '@/api/student';
import SearchBox from './components/SearchBox.vue';
import StudentTable from './components/StudentTable.vue';
import Empty from "@/components/Empty.vue";

// 班级信息
const taskId = ref('');
const classId = ref('');

// 列表数据和状态
const allStudents = ref([]); // 存储所有学生数据
const loading = ref(false);
const searchKeyword = ref('');

// 过滤后的学生列表
const filteredStudents = computed(() => {
  const keyword = searchKeyword.value.toLowerCase().trim();
  return allStudents.value.filter(student => 
    !keyword || 
    student.name.toLowerCase().includes(keyword) || 
    (student.studentNumber && student.studentNumber.toLowerCase().includes(keyword))
  );
});

// 直接使用过滤后的学生列表
const studentList = computed(() => {
  return filteredStudents.value;
});

// 获取学生列表
const fetchStudentList = async () => {
  loading.value = true;
  try {
    const params = { 
      classId: classId.value,
      taskId: taskId.value
    };
    const res = await getStudentsWithoutTaskItems(params);
    allStudents.value = res.data || [];
  } finally {
    loading.value = false;
    uni.stopPullDownRefresh();
  }
};

// 页面加载
onLoad((option) => {
  classId.value = option.classId;
  taskId.value = option.taskId;
  fetchStudentList();
});

// 搜索处理
const handleSearch = (keyword)=>{
  searchKeyword.value = keyword;
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = '';
};

// 下拉刷新
onPullDownRefresh(async () => {
  await fetchStudentList();
});
</script>

<style lang="scss" scoped>
.student-list {
  padding-bottom: 90rpx;
  
  .student-table {
    padding: 15rpx 30rpx;
  }
}
</style>

