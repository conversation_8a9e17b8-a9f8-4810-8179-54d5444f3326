<template>
  <view class="correction-analysis">
    <view class="section-title">批改分析</view>

    <!-- 批改分析内容 -->
    <view class="analysis-content">
      <ua-markdown :source="correctionAnalysis" />
    </view>

    <!-- 错误类型饼图 -->
    <MFCPieChart :errors="errorStats" :error-details="errorDetails" />

    <!-- 改进建议 -->
    <view class="improvement">
      <view class="section-title">改进建议</view>
      <view class="improvement-content">
        <ua-markdown :source="improvementSuggestions" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import MFCPieChart from './MFCPieChart.vue';

const props = defineProps({
  errorStats: {
    type: Object,
    default: () => ({
      wordMeaning: 0,
      tense: 0,
      article: 0,
      conjunction: 0
    })
  },
  improvementSuggestions: {
    type: String,
    default: ''
  },
  correctionAnalysis: {
    type: String,
    default: ''
  },
  errorDetails: {
    type: Object,
    default: () => ({
      tenseErrors: [],
      wordChoiceErrors: [],
      articleErrors: [],
      conjunctionErrors: []
    })
  }
});



</script>

<style lang="scss" scoped>
.correction-analysis {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .analysis-content {
    padding: 20rpx;
    background-color: #FFF7F5;
    border-radius: 8rpx;
    margin-bottom: 30rpx;
    line-height: 1.6;
    font-size: 28rpx;
    color: #333;
  }



  .improvement {
    margin-top: 30rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }

    .improvement-content {
      padding: 20rpx;
      background-color: #f0f7ff;
      border-radius: 8rpx;
      line-height: 1.6;
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
