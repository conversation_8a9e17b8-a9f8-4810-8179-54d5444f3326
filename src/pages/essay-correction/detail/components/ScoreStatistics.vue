<template>
  <view class="score-section">
    <view class="score-item">
      <view class="score-value">{{ score }}</view>
      <view class="score-label">成绩</view>
    </view>
    <view class="score-item">
      <view class="score-value">{{ totalScore }}</view>
      <view class="score-label">总分</view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  score: {
    type: [Number, String],
    default: 0
  },
  totalScore: {
    type: [Number, String],
    default: 100
  }
});
</script>

<style lang="scss" scoped>
.score-section {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;

  .score-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;

    .score-value {
      font-size: 80rpx;
      font-weight: 600;
      margin-bottom: 8rpx;
      color: #333;
    }

    .score-label {
      font-size: 28rpx;
      color: #666;
    }
  }

  // 第一个成绩项（成绩）使用主色调
  .score-item:first-child {
    .score-value {
      color: #4285F4;
    }
  }

  // 第二个成绩项（总分）使用灰色
  .score-item:last-child {
    .score-value {
      color: #666;
    }
  }
}
</style>
