<template>
  <view class="essay-section">
    <!-- 作文原文 -->
    <view class="essay-original">
      <view class="section-title">作文原文</view>
      <view class="essay-content">
        <text user-select>{{ content }}</text>
      </view>
    </view>

    <!-- 对比图片按钮 -->
    <view class="compare-btn" @click="handleViewOriginal">
      <text>对比图片</text>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  content: {
    type: String,
    default: ''
  },
  images: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['viewOriginal']);


const handleViewOriginal = () => {
  emit('viewOriginal', props.images);
};
</script>

<style lang="scss" scoped>
.essay-section {
  margin-bottom: 20rpx;
}

.essay-original {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .essay-content {
    padding: 20rpx;
    background-color: #F0F7FF;
    border-radius: 8rpx;
    line-height: 1.6;
    font-size: 28rpx;
    color: #333;
    /* 移除高度限制和滚动，让内容完整展示 */
    min-height: 200rpx;
  }
}

.compare-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #4285F4;
  padding: 20rpx;
  border-radius: 8rpx;

  text {
    color: #fff;
    font-size: 30rpx;
  }

  &:active {
    opacity: 0.8;
  }
}
</style>
