<template>
  <view class="floating-nav">
    <view class="nav-btn prev-btn" @click="handlePrevious">
      <image src="/static/detail/arrow-up.svg" mode="aspectFit" class="nav-icon"></image>
    </view>
    <view class="nav-btn next-btn" @click="handleNext">
      <image src="/static/detail/arrow-down.svg" mode="aspectFit" class="nav-icon"></image>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['previous', 'next']);

const handlePrevious = () => {
  emit('previous');
};

const handleNext = () => {
  emit('next');
};
</script>

<style lang="scss" scoped>
.floating-nav {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .nav-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background-color: rgba(66, 133, 244, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;

    .nav-icon {
      width: 32rpx;
      height: 32rpx;
    }

    &:active {
      transform: scale(0.95);
      background-color: rgba(66, 133, 244, 1);
    }
  }
}
</style>
