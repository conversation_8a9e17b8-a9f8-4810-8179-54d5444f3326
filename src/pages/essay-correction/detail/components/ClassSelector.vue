<template>
  <view class="filter-section">
    <picker @change="onChange" :value="selectedIndex" :range="classList">
      <view class="uni-picker-selector">
        <text>{{ classList[selectedIndex] }}</text>
        <text class="dropdown-icon">▼</text>
      </view>
    </picker>
  </view>
</template>

<script setup>
const props = defineProps({
  classList: {
    type: Array,
    default: () => ['全部班级']
  },
  selectedIndex: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['change']);

const onChange = (e) => {
  emit('change', e);
};
</script>

<style lang="scss" scoped>
.filter-section {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  height: 100rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .uni-picker-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    font-weight: 600;
    color: #007aff;
    padding: 12rpx 24rpx;
    background-color: rgba(0, 122, 255, 0.08);
    border-radius: 16rpx;
    width: 210rpx;
    transition: all 0.25s cubic-bezier(0.33, 1, 0.68, 1);
    box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);

    &:active {
      opacity: 0.6;
      transform: scale(0.98);
    }

    .dropdown-icon {
      margin-left: 8rpx;
      font-size: 18rpx;
      color: #007aff;
    }
  }
}
</style>
