<template>
  <view class="grade-list">
    <grade-item
      v-for="(item, index) in gradeList"
      :key="index"
      :item="item"
      :show-actions="true"
      @click="onItemClick"
      @delete="handleDelete"
      @rename="handleRename"
    />
    <!-- 加载更多 -->
    <uni-load-more :status="loadMoreStatus" />
  </view>
</template>

<script setup>
import GradeItem from '@/components/grade/GradeItem.vue';

const props = defineProps({
  gradeList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadMoreStatus: {
    type: String,
    default: 'more'
  }
});

const emit = defineEmits(['itemClick', 'delete', 'rename']);

const onItemClick = (item) => {
  emit('itemClick', item);
};

// 处理删除
const handleDelete = (id) => {
  emit('delete', id);
};

// 处理重命名
const handleRename = (data) => {
  emit('rename', data);
};
</script>

<style lang="scss" scoped>
.grade-list {
  margin: 20rpx;
  margin-top: 120rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .empty-tip {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 120rpx 0;
    height: 240rpx;

    text {
      color: #8e8e93;
      font-size: 30rpx;
      font-weight: 500;
      text-align: center;
      opacity: 0.9;
    }
  }
}
</style>
