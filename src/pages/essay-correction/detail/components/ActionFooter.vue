<template>
  <view class="action-footer">
    <view class="action-buttons">
      <view class="action-btn export-btn" @click="handleExport">
        <text>导出Excel</text>
      </view>
      <view class="action-btn edit-btn" @click="handleEditScore">
        <text>修改分数</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  safeAreaInsetBottom: {
    type: Number,
    default: 20
  }
});

const emit = defineEmits(['export', 'editScore']);

const handleExport = () => {
  emit('export');
};

const handleEditScore = () => {
  emit('editScore');
};
</script>

<style lang="scss" scoped>
.action-footer {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12rpx 24rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: v-bind('props.safeAreaInsetBottom + "rpx"');

  .action-buttons {
    display: flex;
    width: 100%;
    gap: 16rpx;
  }

  .action-btn {
    flex: 1;
    height: 72rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border: 2rpx solid #e5e5e5;
    border-radius: 8rpx;
    font-size: 26rpx;

    &.export-btn {
      text {
        color: #217346;
      }
    }

    &.edit-btn {
      text {
        color: #4285F4;
      }
    }

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
