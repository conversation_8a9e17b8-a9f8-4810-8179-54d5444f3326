<template>
  <view class="error-stats">
    <view class="stats-title">错误统计</view>
    <view class="stats-list">
      <view v-for="(item, index) in errorTypes" :key="index" class="stats-item-wrapper">
        <view class="stats-item" :class="{ 'has-errors': item.errors && item.errors.length > 0 }">
          <view class="color-indicator" :style="{ backgroundColor: item.color }"></view>
          <text class="stats-text">{{ item.name }}: {{ item.value }}</text>
        </view>

        <!-- 具体错误列表 -->
        <view v-if="item.errors && item.errors.length > 0" class="error-details">
          <view v-for="(error, errorIndex) in item.errors" :key="errorIndex" class="error-item">
            <text class="error-number">{{ errorIndex + 1 }}.</text>
            <text class="error-text">{{ error }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  errors: {
    type: Object,
    default: () => ({
      wordMeaning: 4,
      tense: 3,
      article: 2,
      conjunction: 1
    })
  },
  errorDetails: {
    type: Object,
    default: () => ({
      tenseErrors: [],
      wordChoiceErrors: [],
      articleErrors: [],
      conjunctionErrors: []
    })
  }
});

// 错误类型数据 - 使用更美观的颜色，并包含具体错误
const errorTypes = computed(() => [
  {
    name: '词意错误',
    value: props.errors.wordMeaning || 0,
    color: '#FF6B6B',
    errors: props.errorDetails.wordChoiceErrors || []
  },
  {
    name: '时态错误',
    value: props.errors.tense || 0,
    color: '#4ECDC4',
    errors: props.errorDetails.tenseErrors || []
  },
  {
    name: '冠词错误',
    value: props.errors.article || 0,
    color: '#45B7D1',
    errors: props.errorDetails.articleErrors || []
  },
  {
    name: '连词错误',
    value: props.errors.conjunction || 0,
    color: '#96CEB4',
    errors: props.errorDetails.conjunctionErrors || []
  }
]);

// 不需要复杂的计算逻辑了

</script>

<style lang="scss" scoped>
.error-stats {
  width: 100%;

  .stats-title {
    font-size: 34rpx;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 32rpx;
    text-align: center;
    letter-spacing: 0.5rpx;
  }

  .stats-list {
    display: flex;
    flex-direction: column;
    gap: 24rpx;

    .stats-item-wrapper {
      background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
      border-radius: 16rpx;
      overflow: hidden;
      transition: all 0.3s ease;
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
      border: 1rpx solid rgba(0, 0, 0, 0.04);

      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
      }

      .stats-item {
        display: flex;
        align-items: center;
        padding: 24rpx 28rpx;
        position: relative;

        &.has-errors::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 28rpx;
          right: 28rpx;
          height: 1rpx;
          background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.06) 50%, transparent 100%);
        }

        .color-indicator {
          width: 40rpx;
          height: 40rpx;
          margin-right: 24rpx;
          border-radius: 12rpx;
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20rpx;
            height: 20rpx;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
          }
        }

        .stats-text {
          font-size: 30rpx;
          color: #2c3e50;
          font-weight: 600;
          letter-spacing: 0.3rpx;
          flex: 1;
        }
      }

      .error-details {
        padding: 0 28rpx 24rpx 28rpx;
        background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);

        .error-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16rpx;
          padding: 12rpx 16rpx;
          background: rgba(255, 255, 255, 0.7);
          border-radius: 10rpx;
          border-left: 4rpx solid #e9ecef;
          transition: all 0.2s ease;

          &:last-child {
            margin-bottom: 0;
          }

          &:hover {
            background: rgba(255, 255, 255, 0.9);
            border-left-color: #007aff;
            transform: translateX(4rpx);
          }

          .error-number {
            font-size: 26rpx;
            color: #007aff;
            margin-right: 12rpx;
            flex-shrink: 0;
            font-weight: 600;
            margin-top: 2rpx;
            background: rgba(0, 122, 255, 0.1);
            padding: 2rpx 8rpx;
            border-radius: 6rpx;
            min-width: 32rpx;
            text-align: center;
          }

          .error-text {
            font-size: 28rpx;
            color: #34495e;
            line-height: 1.6;
            flex: 1;
            user-select: text;
            font-weight: 400;
          }
        }
      }


    }
  }
}
</style>