<template>
  <view>
    <!-- 遮罩层 -->
    <view class="modal-mask" v-if="visible" @tap="handleMaskClick"></view>
    
    <!-- 弹窗内容 -->
    <view
      class="modal-container"
      v-if="visible"
      :style="{
        paddingBottom: keyboardHeight + 'px',
        paddingTop: keyboardHeight > 0 ? '15vh' : '30vh'
      }"
    >
      <view class="modal-content">
        <!-- 标题 -->
        <view class="modal-header">
          <text class="modal-title">修改分数</text>
        </view>
        
        <!-- 输入区域 -->
        <view class="modal-body">
          <view class="input-container">
            <input
              class="score-input"
              type="number"
              v-model="inputValue"
              :placeholder="placeholder"
              :maxlength="3"
              @input="handleInput"
              @focus="handleFocus"
              :focus="autoFocus"
              adjust-position
              cursor-spacing="30"
            />
            <text class="input-suffix">分</text>
          </view>
          <view class="input-tip">
            分数范围：1-{{ maxScore }}分
          </view>
        </view>
        
        <!-- 按钮区域 -->
        <view class="modal-footer">
          <button class="btn btn-cancel" @tap="handleCancel">取消</button>
          <button class="btn btn-confirm" @tap="handleConfirm">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentScore: {
    type: [Number, String],
    default: ''
  },
  maxScore: {
    type: Number,
    default: 100
  }
});

const emit = defineEmits(['confirm', 'cancel', 'update:visible']);

const inputValue = ref('');
const autoFocus = ref(false);
const placeholder = ref('');
const keyboardHeight = ref(0);

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗显示时，设置初始值和占位符
    inputValue.value = props.currentScore ? props.currentScore.toString() : '';
    placeholder.value = props.currentScore ? props.currentScore.toString() : '请输入分数';

    // 监听键盘高度变化
    uni.onKeyboardHeightChange((res) => {
      keyboardHeight.value = res.height;
    });

    // 延迟设置焦点，确保弹窗完全显示后再聚焦
    setTimeout(() => {
      autoFocus.value = true;
    }, 100);
  } else {
    autoFocus.value = false;
    keyboardHeight.value = 0;
    // 移除键盘监听
    uni.offKeyboardHeightChange();
  }
});

// 处理输入
const handleInput = (e) => {
  inputValue.value = e.detail.value;
};

// 处理聚焦
const handleFocus = () => {
  // 聚焦时选中所有文本（如果有的话）
  // 注意：小程序中input的select方法可能不可用，这里只是占位
};

// 处理遮罩点击
const handleMaskClick = () => {
  handleCancel();
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 处理确认
const handleConfirm = () => {
  const score = inputValue.value.trim();
  
  if (!score) {
    uni.showToast({
      title: '请输入分数',
      icon: 'none'
    });
    return;
  }
  
  const numScore = parseInt(score);
  
  // 校验规则
  if (isNaN(numScore)) {
    uni.showToast({
      title: '请输入有效的数字',
      icon: 'none'
    });
    return;
  }
  
  if (!Number.isInteger(parseFloat(score))) {
    uni.showToast({
      title: '分数必须是正整数',
      icon: 'none'
    });
    return;
  }
  
  if (numScore <= 0) {
    uni.showToast({
      title: '分数必须大于0',
      icon: 'none'
    });
    return;
  }
  
  if (numScore > props.maxScore) {
    uni.showToast({
      title: `分数不能超过${props.maxScore}分`,
      icon: 'none'
    });
    return;
  }
  
  emit('update:visible', false);
  emit('confirm', numScore);
};
</script>

<style lang="scss" scoped>
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9998;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 9999;
  box-sizing: border-box;
  transition: padding-bottom 0.3s ease;
  padding-top: 30vh;
}

.modal-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  max-width: 90vw;
  overflow: hidden;
}

.modal-header {
  padding: 40rpx 40rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-body {
  padding: 40rpx;
}

.input-container {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 24rpx;
  margin-bottom: 20rpx;
}

.score-input {
  flex: 1;
  height: 88rpx;
  font-size: 32rpx;
  color: #333;
  background-color: transparent;
  border: none;
  outline: none;
}

.input-suffix {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
}

.input-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 32rpx;
  border: none;
  background-color: transparent;
  border-radius: 0;
}

.btn-cancel {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.btn-confirm {
  color: #4285F4;
  font-weight: 600;
}

.btn::after {
  border: none;
}
</style>
