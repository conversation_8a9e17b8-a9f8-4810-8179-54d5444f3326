<template>
  <view class="student-info">
    <view class="info-left" v-if="student && student.name">
      <view class="student-info-wrapper">
        <view class="student-details">
          <view class="student-name">{{ student.name }}</view>
          <view class="student-id" v-if="student.studentNumber">学号：{{ student.studentNumber }}</view>
        </view>
        <view class="edit-student-btn" @click="handleEditStudent">
          <uni-icons type="compose" size="18" color="#4285F4"></uni-icons>
        </view>
      </view>
    </view>
    <view class="info-left" v-else>
      <view class="select-student-btn" @click="handleSelectStudent">
        <uni-icons type="plusempty" size="20" color="#4285F4"></uni-icons>
        <text>选择学生</text>
      </view>
    </view>
    <view class="info-right">
      <text class="exam-date">{{ formattedDate }}</text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import { formatDate } from '@/utils/tools';

const props = defineProps({
  student: {
    type: Object,
    default: () => ({})
  },
  createTime: {
    type: [Number, String],
    default: ''
  }
});

const emit = defineEmits(['selectStudent', 'editStudent']);

const formattedDate = computed(() => {
  return props.createTime ? formatDate(props.createTime) : '';
});

const handleSelectStudent = () => {
  emit('selectStudent');
};

const handleEditStudent = () => {
  emit('editStudent');
};
</script>

<style lang="scss" scoped>
.student-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;

  .info-left {
    .student-info-wrapper {
      display: flex;
      align-items: flex-start;
    }

    .student-details {
      margin-right: 16rpx;
    }

    .student-name {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .student-id {
      font-size: 26rpx;
      color: #666;
      margin-top: 8rpx;
    }

    .edit-student-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;

      &:active {
        background-color: #F0F7FF;
      }
    }

    .select-student-btn {
      display: flex;
      align-items: center;
      background-color: #F0F7FF;
      padding: 12rpx 20rpx;
      border-radius: 8rpx;

      text {
        color: #4285F4;
        font-size: 28rpx;
        margin-left: 8rpx;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }

  .info-right {
    .exam-date {
      font-size: 26rpx;
      color: #666;
    }
  }
}
</style>
