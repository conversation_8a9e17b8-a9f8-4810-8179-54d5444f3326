<template>
  <view class="correction-detail">
    <!-- 悬浮导航按钮 -->
    <FloatingNavigation
      @previous="goToPrevious"
      @next="goToNext"
    />

    <view class="content-container">
      <!-- 学生信息 -->
      <StudentInfoCard
        :student="taskItemDetail.student"
        :create-time="taskItemDetail.createTime"
        @select-student="showStudentSelector"
        @edit-student="showStudentSelector"
      />

      <!-- 成绩统计 -->
      <ScoreStatistics
        :score="taskItemDetail.finalScore || parsedCorrectionResult?.score"
        :total-score="parsedCorrectionResult?.rubricTotalScore"
      />

      <!-- 作文原文 -->
      <EssayOriginal
        :content="parsedCorrectionResult?.originalEssay"
        :images="taskItemDetail.images"
        @view-original="viewOriginal"
      />

      <!-- 批改分析 -->
      <CorrectionAnalysis
        :error-stats="errorStats"
        :improvement-suggestions="parsedCorrectionResult?.improvementSuggestions"
        :correction-analysis="parsedCorrectionResult?.correctionAnalysis"
        :error-details="parsedCorrectionResult || {}"
      />


    </view>

    <!-- 底部功能区 - 固定在底部 -->
    <ActionFooter
      :safe-area-inset-bottom="safeAreaInsetBottom"
      @export="handleExport"
      @edit-score="handleEditScore"
    />

    <LoadingMask :show="isLoading" />

    <!-- 学生选择器组件 -->
    <StudentSelector
      v-if="showSelector"
      :paper="taskItemDetail"
      :class-id="taskItemDetail.classId"
      :task-id="taskItemDetail.taskId"
      @select="handleStudentConfirm"
      @visibleChange="handleSelectorVisibleChange"
    />

    <!-- 分数编辑弹窗 -->
    <ScoreEditModal
      v-model:visible="showScoreEditModal"
      :current-score="taskItemDetail.finalScore || parsedCorrectionResult?.score"
      :max-score="parsedCorrectionResult?.rubricTotalScore"
      @confirm="handleScoreConfirm"
      @cancel="handleScoreCancel"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import LoadingMask from '@/components/LoadingMask';
import StudentSelector from '@/components/StudentSelector';
import FloatingNavigation from './components/FloatingNavigation.vue';
import StudentInfoCard from './components/StudentInfoCard.vue';
import ScoreStatistics from './components/ScoreStatistics.vue';
import EssayOriginal from './components/EssayOriginal.vue';
import CorrectionAnalysis from './components/CorrectionAnalysis.vue';
import ActionFooter from './components/ActionFooter.vue';
import ScoreEditModal from './components/ScoreEditModal.vue';
import { onLoad } from '@dcloudio/uni-app';
import {
  getTaskItemDetail,
  updateTaskItem,
  exportTaskDetailExcel,
  getTaskItemIdList
} from '@/api/task-item';

const safeAreaInsetBottom = ref(0)
uni.getSystemInfo().then((res) => {
  safeAreaInsetBottom.value = res.safeAreaInsets.bottom < 20 ? 20 : res.safeAreaInsets.bottom
})

const taskId = ref('');
const taskItemDetail = ref({
  student: {
    name: ''
  },
  finalScore: null,
  content: '',
  improvementSuggestions: '',
  createTime: ''
});

// correctionResult解析后的数据
const parsedCorrectionResult = ref(null);
const isLoading = ref(false);
const showSelector = ref(false);
const showScoreEditModal = ref(false);
const sortField = ref('');

// 任务项ID列表和当前索引
const taskItemIds = ref([]);
const currentItemIndex = ref(-1);

// 错误统计数据（用于饼图）
const errorStats = computed(() => {
  if (!parsedCorrectionResult.value) {
    return {
      wordMeaning: 0,
      tense: 0,
      article: 0,
      conjunction: 0
    };
  }

  return {
    wordMeaning: parsedCorrectionResult.value.wordChoiceErrors?.length || 0,
    tense: parsedCorrectionResult.value.tenseErrors?.length || 0,
    article: parsedCorrectionResult.value.articleErrors?.length || 0,
    conjunction: parsedCorrectionResult.value.conjunctionErrors?.length || 0
  };
});

onLoad((option) => {
  const { id, sortField: optionSortField } = option;
  if (id) {
    taskId.value = id;
    if (optionSortField) {
      sortField.value = optionSortField;
    }
    fetchTaskItemDetail(id);
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
  }
})

// 获取任务下所有批改任务项ID列表
const fetchTaskItemIds = async (taskId, sortField) => {
  try {
    const res = await getTaskItemIdList({
      taskId,
      sortField,
    });
    if (res.data && Array.isArray(res.data)) {
      taskItemIds.value = res.data;
      // 找到当前任务项在列表中的索引
      const currentId = taskItemDetail.value.id;
      currentItemIndex.value = taskItemIds.value.findIndex(id => id === currentId);

      // 删除首次加载时的位置提示
    }
  } catch (error) {
    console.error('获取任务项ID列表失败:', error);
  }
};

// 获取批改任务详情
const fetchTaskItemDetail = async (id) => {
  isLoading.value = true;
  return new Promise(async (resolve, reject) => {
    try {
      const res = await getTaskItemDetail(id);
      const data = res.data;

      // 更新基础任务项数据
      taskItemDetail.value = {
        ...data
      };

      // 解析 correctionResult JSON 字符串
      if (data.correctionResult) {
        try {
          parsedCorrectionResult.value = JSON.parse(data.correctionResult);
        } catch (parseError) {
          console.error('解析 correctionResult 失败:', parseError);
          parsedCorrectionResult.value = null;
        }
      } else {
        parsedCorrectionResult.value = null;
      }

      // 获取任务项ID列表用于导航
      fetchTaskItemIds(data.taskId, sortField.value);

      resolve();
    } catch (error) {
      console.error('获取批改任务详情失败:', error);
      uni.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
      reject(error);
    } finally {
      isLoading.value = false;
    }
  });
};

// 查看原图
const viewOriginal = (images) => {
  const imageUrl = images || taskItemDetail.value.images;
  if (!imageUrl) {
    uni.showToast({
      title: '暂无原图',
      icon: 'none'
    });
    return;
  }

  uni.previewImage({
    urls: [imageUrl],
    current: 0
  });
};

// 导出Excel报告
const handleExport = async () => {
  try {
    uni.showLoading({ title: '正在导出...' });
    const res = await exportTaskDetailExcel({
      itemId: taskItemDetail.value.id,
      minCorrectRate: 0,
      maxCorrectRate: 100,
      pageNo: 1,
      pageSize: 1,
    });

    const fs = wx.getFileSystemManager();
    const tempFilePath = `${wx.env.USER_DATA_PATH}/${taskItemDetail.value.student?.name || '成绩报告'}.xlsx`;

    fs.writeFile({
      filePath: tempFilePath,
      data: res,
      encoding: 'binary',
      success: () => {
        uni.hideLoading();
        uni.openDocument({
          filePath: tempFilePath,
          showMenu: true,
          success: () => {
            console.log('文件打开成功');
          },
          fail: (err) => {
            console.error('文件打开失败:', err);
            uni.showToast({
              title: '文件打开失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        uni.hideLoading();
        console.error('文件保存失败:', err);
        uni.showToast({
          title: '文件保存失败',
          icon: 'none'
        });
      }
    });
  } catch (err) {
    uni.hideLoading();
    console.error('导出失败:', err);
    uni.showToast({
      title: '导出失败',
      icon: 'none'
    });
  }
};

// 显示学生选择器
const showStudentSelector = () => {
  showSelector.value = true;
};

// 处理学生选择器可见性变化
const handleSelectorVisibleChange = (data) => {
  showSelector.value = data.visible;
};

// 处理学生选择确认
const handleStudentConfirm = async (data) => {
  const { paper, student } = data;
  try {
    const inData = {
      id: paper.id,
      taskId: paper.taskId,
      teacherId: paper.teacherId,
      classId: paper.classId,
      studentId: student.id,
    }
    await updateTaskItem(inData);

    // 更新当前任务项中的学生信息
    taskItemDetail.value.student = {
      id: student.id,
      name: student.name,
      studentNumber: student.studentNumber
    };

    // 关闭学生选择器弹窗
    showSelector.value = false;

    uni.showToast({
      title: '学生更新成功',
      icon: 'success'
    });
  } catch (err) {
    console.error('更新学生失败:', err);
    uni.showToast({
      title: '更新学生失败',
      icon: 'none'
    });
  }
};

// 导航到上一个任务项
const goToPrevious = () => {
  if (currentItemIndex.value > 0 && taskItemIds.value.length > 0) {
    const prevIndex = currentItemIndex.value - 1;
    const prevId = taskItemIds.value[prevIndex];

    // 显示加载提示
    uni.showLoading({
      title: '正在加载上一个...',
      mask: true
    });

    // 更新当前任务项ID并重新加载详情
    taskId.value = prevId;
    fetchTaskItemDetail(prevId).then(() => {
      // 加载成功后显示提示
      uni.hideLoading();
      uni.showToast({
        title: '已切换到上一个',
        icon: 'none',
        duration: 1500
      });

      // 滚动到页面顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }).catch(() => {
      uni.hideLoading();
    });

    // 重新获取任务项ID列表，保持排序一致
    fetchTaskItemIds(taskItemDetail.value.taskId, sortField.value);
  } else {
    uni.showToast({
      title: '已经是第一个了',
      icon: 'none'
    });
  }
};

// 导航到下一个任务项
const goToNext = () => {
  if (currentItemIndex.value < taskItemIds.value.length - 1 && taskItemIds.value.length > 0) {
    const nextIndex = currentItemIndex.value + 1;
    const nextId = taskItemIds.value[nextIndex];

    // 显示加载提示
    uni.showLoading({
      title: '正在加载下一个...',
      mask: true
    });

    // 更新当前任务项ID并重新加载详情
    taskId.value = nextId;
    fetchTaskItemDetail(nextId).then(() => {
      // 加载成功后显示提示
      uni.hideLoading();
      uni.showToast({
        title: '已切换到下一个',
        icon: 'none',
        duration: 1500
      });

      // 滚动到页面顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }).catch(() => {
      uni.hideLoading();
    });

    // 重新获取任务项ID列表，保持排序一致
    fetchTaskItemIds(taskItemDetail.value.taskId, sortField.value);
  } else {
    uni.showToast({
      title: '已经是最后一个了',
      icon: 'none'
    });
  }
};

// 修改分数方法
const handleEditScore = () => {
  // 检查是否有标准分数据
  if (!parsedCorrectionResult.value?.rubricTotalScore) {
    uni.showToast({
      title: '无法获取标准分数据',
      icon: 'none'
    });
    return;
  }

  const totalScore = parseInt(parsedCorrectionResult.value.rubricTotalScore);
  if (isNaN(totalScore) || totalScore <= 0) {
    uni.showToast({
      title: '标准分数据异常',
      icon: 'none'
    });
    return;
  }

  // 显示分数编辑弹窗
  showScoreEditModal.value = true;
};

// 处理分数确认
const handleScoreConfirm = async (newScore) => {
  try {
    isLoading.value = true;

    uni.showLoading({ title: '更新中...' });
    // 调用API更新分数，使用完整的参数
    await updateTaskItem({
      id: taskItemDetail.value.id,
      taskId: taskItemDetail.value.taskId,
      teacherId: taskItemDetail.value.teacherId,
      classId: taskItemDetail.value.classId,
      finalScore: newScore,
      correctRate: newScore
    });

    await fetchTaskItemDetail(taskItemDetail.value.id);

    // 通知列表页面刷新数据
    uni.$emit('refreshTaskList');

    uni.hideLoading();
    uni.showToast({
      title: '分数已修改',
      icon: 'success'
    });
  } catch (error) {
    console.error('修改分数失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '修改分数失败',
      icon: 'none'
    });
  } finally {
    isLoading.value = false;
  }
};

// 处理分数取消
const handleScoreCancel = () => {
  // 弹窗组件会自动关闭，这里可以添加其他逻辑
};
</script>

<style lang="scss" scoped>
.correction-detail {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  box-sizing: border-box;
  position: relative;

  .content-container {
    padding: 20rpx;
    box-sizing: border-box;
  }
}
</style>
