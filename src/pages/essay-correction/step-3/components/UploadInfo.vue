<template>
  <view class="upload-info-container">
    <image src="/static/icons/upload-illustration.svg" class="upload-illustration"></image>
    <view class="upload-title">开始上传作文</view>
    <view class="upload-description">点击下方"上传作文"按钮，开始上传学生的默写结果</view>
    <view class="upload-counter">
      <text class="current-count">{{ currentCount }}</text>
      <text class="counter-separator">/</text>
      <text class="max-count">{{ maxCount }}</text>
      <text class="counter-label">张作业</text>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  currentCount: {
    type: Number,
    default: 0
  },
  maxCount: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['click']);
</script>

<style lang="scss" scoped>
.upload-info-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;

  .upload-illustration {
    width: 240rpx;
    height: 240rpx;
    margin-bottom: 40rpx;
  }

  .upload-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
  }

  .upload-description {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 40rpx;
    line-height: 1.5;
    max-width: 500rpx;
  }

  .upload-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border-radius: 40rpx;
    padding: 16rpx 30rpx;

    .current-count {
      font-size: 36rpx;
      font-weight: 600;
      color: #4285F4;
    }

    .counter-separator {
      font-size: 28rpx;
      color: #999;
      margin: 0 8rpx;
    }

    .max-count {
      font-size: 28rpx;
      color: #666;
    }

    .counter-label {
      font-size: 24rpx;
      color: #666;
      margin-left: 10rpx;
    }
  }
}
</style>
