<template>
  <view class="frequent-errors" v-if="errorWords.length > 0">
    <view class="header" @click="toggleExpand">
      <text class="title">常见问题: {{ totalCount }}</text>
      <uni-icons :type="isExpanded ? 'top' : 'bottom'" size="14" color="#666" class="icon" :class="{ 'icon-rotate': isExpanded }"></uni-icons>
    </view>
    <view class="error-list-wrapper" :class="{ 'expanded': isExpanded }">
      <view class="error-table">
        <view class="table-header">
          <text class="th th-no">序号</text>
          <text class="th th-text">问题描述</text>
          <text class="th th-count">出现次数</text>
        </view>
        <view
          v-for="(word, index) in errorWords"
          :key="index"
          class="table-row"
          :class="getTagClass(word.count)"
        >
          <text class="td td-no">{{word.no}}</text>
          <text class="td td-text">{{ word.correctText }}</text>
          <text class="td td-count">{{ word.count }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  errorWords: {
    type: Array,
    default: () => []
  }
});

const isExpanded = ref(false);
const totalCount = computed(() => props.errorWords.length);

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

const getTagClass = (count) => {
  if (count >= 4) return 'error-high';
  if (count >= 2) return 'error-medium';
  return 'error-low';
};
</script>

<style lang="scss" scoped>
.frequent-errors {
  margin-top: 20rpx;
  background: #FFFFFF;
  border-radius: 12rpx;
  padding: 0 40rpx;
  margin-bottom: 20rpx;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  height: 80rpx;

  .title {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }

  .icon {
    transition: transform 0.3s ease;

    &.icon-rotate {
      transform: rotate(180deg);
    }
  }
}

.error-list-wrapper {
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-out;

  &.expanded {
    max-height: 1000rpx;
    padding: 20rpx 0;
  }
}

.error-table {
  width: 100%;
  border-radius: 8rpx;
  overflow: hidden;
  border: 1px solid #eee;
}

.table-header {
  display: flex;
  background-color: #f5f5f5;
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}

.table-row {
  display: flex;
  border-top: 1px solid #eee;
  font-size: 24rpx;
  
  &.error-high {
    background-color: #FFF1F0;
    color: #FF4D4F;
  }

  &.error-medium {
    background-color: #FFFBE6;
    color: #FAAD14;
  }

  &.error-low {
    background-color: #F0F5FF;
    color: #4285F4;
  }
}

.th, .td {
  padding: 16rpx 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.th-no, .td-no {
  flex: 0 0 15%;
  text-align: center;
}

.th-text, .td-text {
  flex: 1;
  padding-left: 20rpx;
}

.th-count, .td-count {
  flex: 0 0 25%;
  text-align: center;
}
</style>
