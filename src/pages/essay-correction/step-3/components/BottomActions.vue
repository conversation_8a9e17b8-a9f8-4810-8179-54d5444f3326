<template>
  <view class="bottom-actions">
    <button class="upload-btn" @click="handleUpload">
      <image src="/static/icons/upload-white.svg" class="icon"></image>
      上传作文
    </button>
    <button class="filter-btn" @click="handleFilter">
      <image src="/static/icons/filter.svg" class="icon"></image>
      筛选
    </button>

    <button class="more-btn" @click="showActionSheet">
      <image src="/static/icons/more.svg" class="icon"></image>
      导出
    </button>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const safeAreaInsetBottom = ref(0)
uni.getSystemInfo().then((res) => {
  safeAreaInsetBottom.value = res.safeAreaInsets.bottom < 20 ? 20 : res.safeAreaInsets.bottom
})

const emit = defineEmits(['upload', 'filter', 'regularExport', 'batchExport']);

const handleUpload = () => {
  emit('upload');
};

const handleFilter = () => {
  emit('filter');
};

// 显示操作菜单
const showActionSheet = () => {
  uni.showActionSheet({
    itemList: ['导出成绩Excel（同显示顺序）', '批量导出学生作业详情'],
    success: (res) => {
      const { tapIndex } = res;
      // tapIndex: 0 - 导出Excel, 1 - 批量导出Excel
      if (tapIndex === 0) {
        emit('regularExport');
      } else if (tapIndex === 1) {
        emit('batchExport');
      }
    },
    fail: (err) => {
      console.error('操作菜单关闭', err);
    }
  });
};
</script>

<style lang="scss" scoped>
.bottom-actions {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding-bottom: v-bind('safeAreaInsetBottom + "rpx"');
  position: relative;

  .upload-btn, .filter-btn, .more-btn {
    flex: 1;
    height: 90rpx;
    border-radius: 10rpx;
    font-size: 26rpx;
    font-weight: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  }

  .upload-btn {
    background-color: #4285F4;
    color: #ffffff;
    flex: 1.2;
  }

  .filter-btn {
    background-color: #f5f5f7;
    color: #333;
    flex: 0.9;
  }

  .more-btn {
    background-color: #ffffff;
    color: #217346;
    border: 2rpx solid #217346;
    flex: 0.9;
  }

  .icon {
    width: 36rpx;
    height: 36rpx;
    margin-right: 6rpx;
  }

  // 更多操作下拉菜单
  .more-dropdown {
    position: absolute;
    bottom: 110rpx;
    right: 30rpx;
    background: #ffffff;
    border-radius: 10rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    z-index: 999;
    width: 240rpx;
    overflow: hidden;

    .dropdown-item {
      padding: 24rpx 20rpx;
      font-size: 26rpx;
      color: #333;
      transition: all 0.3s ease;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: #f0f0f0;
      }
    }
  }
}
</style>
