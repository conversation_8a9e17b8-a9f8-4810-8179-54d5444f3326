<template>
  <view class="score-summary">
    <view class="score-box avg-score">
      <text class="score blue">{{ avgScore }}</text>
      <text class="score-label">平均分数</text>
    </view>
    <view class="score-box highest-score">
      <text class="score green">{{ highestScore }}</text>
      <text class="score-label">最高分数</text>
    </view>
    <view class="score-box lowest-score">
      <text class="score red">{{ lowestScore === 100 ? '0' : lowestScore }}</text>
      <text class="score-label">最低分数</text>
    </view>
  </view>
</template>

<script setup>
defineProps({
  avgScore: {
    type: [Number, String],
    default: 0
  },
  highestScore: {
    type: [Number, String],
    default: 0
  },
  lowestScore: {
    type: [Number, String],
    default: 100
  }
});
</script>

<style lang="scss" scoped>
.score-summary {
  display: flex;
  justify-content: space-between;
  width: 100%;
  box-sizing: border-box;

  .score-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx 15rpx;
    margin: 0 8rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

    .score {
      font-size: 36rpx;
      line-height: 1.2;
      margin-bottom: 6rpx;
      font-weight: 600;

      &.blue {
        color: #4285F4;
      }

      &.green {
        color: #34A853;
      }

      &.red {
        color: #EA4335;
      }
    }

    .score-label {
      font-size: 24rpx;
      color: #666;
    }
  }

  // 为第一个和最后一个盒子添加适当的边距
  .avg-score {
    margin-left: 0;
  }

  .lowest-score {
    margin-right: 0;
  }
}
</style>
