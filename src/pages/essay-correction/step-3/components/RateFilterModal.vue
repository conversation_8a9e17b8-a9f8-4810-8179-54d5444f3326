<template>
  <view class="filter-modal" v-if="visible">
    <view class="filter-modal-overlay" @click="handleClose"></view>
    <view class="filter-modal-content">
      <view class="filter-modal-header">
        <text class="filter-modal-title">分数范围筛选</text>
      </view>

      <view class="filter-modal-body">
        <view class="filter-form-item">
          <!-- Input fields for range -->
          <view class="range-inputs">
            <view class="input-group">
              <input
                type="number"
                class="rate-input"
                :value="minRate"
                @input="handleMinInput"
                placeholder="请输入"
                cursor-spacing="30"
                :maxlength="3"
                @blur="handleBlur('min')"
              />
              <text class="input-suffix">分</text>
            </view>

            <text class="range-separator">至</text>

            <view class="input-group">
              <input
                type="number"
                class="rate-input"
                :value="maxRate"
                @input="handleMaxInput"
                placeholder="请输入"
                cursor-spacing="30"
                :maxlength="3"
                @blur="handleBlur('max')"
              />
              <text class="input-suffix">分</text>
            </view>
          </view>

          <view class="filter-hint">
            <uni-icons type="info" color="#909399" size="14"></uni-icons>
            <text class="filter-hint-text">输入数值或选择预设范围</text>
          </view>
        </view>
      </view>

      <view class="filter-modal-footer">
        <button class="filter-btn-reset" @click="handleReset">重置</button>
        <button class="filter-btn-confirm" @click="handleConfirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  initialMinValue: {
    type: [String, Number],
    default: 0
  },
  initialMaxValue: {
    type: [String, Number],
    default: 100
  }
});

const emit = defineEmits(['update:visible', 'confirm', 'reset', 'close']);

// 内部状态
const minRate = ref(0);
const maxRate = ref(100);

// 监听外部传入的初始值变化
watch(
  () => [props.initialMinValue, props.initialMaxValue],
  ([newMin, newMax]) => {
    minRate.value = parseInt(newMin) || 0;
    maxRate.value = parseInt(newMax) || 100;
  },
  { immediate: true }
);

// 处理最小值输入
const handleMinInput = (e) => {
  const value = e.detail.value;
  // 允许输入框为空
  if (value === '') {
    minRate.value = '';
    return;
  }

  let numValue = parseInt(value);
  // 如果输入的不是有效数字，保持原值不变
  if (isNaN(numValue)) {
    return;
  }

  // 限制范围，使用动态最大分数
  const maxScore = parseInt(props.initialMaxValue) || 100;
  numValue = Math.max(0, Math.min(numValue, maxScore));
  minRate.value = numValue;
};

// 处理最大值输入
const handleMaxInput = (e) => {
  const value = e.detail.value;
  // 允许输入框为空
  if (value === '') {
    maxRate.value = '';
    return;
  }

  let numValue = parseInt(value);
  // 如果输入的不是有效数字，保持原值不变
  if (isNaN(numValue)) {
    return;
  }

  // 限制范围，使用动态最大分数
  const maxScore = parseInt(props.initialMaxValue) || 100;
  numValue = Math.max(0, Math.min(numValue, maxScore));
  maxRate.value = numValue;
};

// 处理输入框失去焦点
const handleBlur = (type) => {
  const maxScore = parseInt(props.initialMaxValue) || 100;
  if (type === 'min') {
    if (minRate.value === '') {
      minRate.value = 0;
    }
  } else if (type === 'max') {
    if (maxRate.value === '') {
      maxRate.value = maxScore;
    }
  }
};

// 设置范围（快速选择）
const setRange = (min, max) => {
  minRate.value = min;
  maxRate.value = max;
};

// 检查当前范围是否激活
const isRangeActive = (min, max) => {
  return minRate.value === min && maxRate.value === max;
};

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 确认筛选
const handleConfirm = () => {
  if (minRate.value > maxRate.value) {
    uni.showToast({
      title: '最小值不能大于最大值',
      icon: 'none'
    });
    return;
  }

  emit('confirm', { minRate: minRate.value, maxRate: maxRate.value });
  emit('update:visible', false);
};

// 重置筛选
const handleReset = () => {
  const maxScore = parseInt(props.initialMaxValue) || 100;
  minRate.value = 0;
  maxRate.value = maxScore;
};
</script>

<style lang="scss" scoped>
/* 筛选弹窗样式 */
.filter-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: flex-end;

  &-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
  }

  &-content {
    position: relative;
    width: 100%;
    background-color: #FFFFFF;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    z-index: 1;
    padding-bottom: env(safe-area-inset-bottom);
  }

  &-header {
    padding: 32rpx 24rpx 20rpx;
    border-bottom: 1px solid #EAEAEA;
  }

  &-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
  }

  &-body {
    padding: 24rpx;
    overscroll-behavior: contain;
  }

  &-footer {
    display: flex;
    padding: 20rpx 24rpx 32rpx;
    gap: 20rpx;

    .filter-btn-reset, .filter-btn-confirm {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 8rpx;
      font-size: 28rpx;
    }

    .filter-btn-reset {
      background-color: #F2F3F5;
      color: #333333;
    }

    .filter-btn-confirm {
      background-color: #1890FF;
      color: #FFFFFF;
    }
  }
}

.filter-form-item {
  margin-bottom: 20rpx;
}

/* Input Styles */
.range-inputs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 30rpx 0;
}

.input-group {
  display: flex;
  align-items: center;
  flex: 1;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 10rpx;
}

.input-suffix {
  font-size: 28rpx;
  color: #333;
  margin-left: 5rpx;
}

.rate-input {
  flex: 1;
  height: 70rpx;
  border: 1px solid #EAEAEA;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #F9F9F9;
  text-align: center;
}

.range-separator {
  margin: 0 20rpx;
  color: #999;
  font-size: 28rpx;
}

/* Quick Select Styles */
.quick-select {
  margin-bottom: 20rpx;
}

.quick-select-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.quick-select-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.quick-btn {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #F5F5F5;
  color: #666;
  border: 1px solid #EAEAEA;
  padding: 0;
  margin: 0;
}

.quick-btn.active {
  background-color: #E6F7FF;
  color: #1890FF;
  border-color: #1890FF;
}

.filter-hint {
  display: flex;
  align-items: center;
  margin-top: 20rpx;

  &-text {
    font-size: 24rpx;
    color: #909399;
    margin-left: 8rpx;
  }
}
</style>
