<template>
  <!-- 试卷列表 -->
  <view class="paper-list" :class="{'grid-mode': viewMode === 'grid'}">
    <view class="paper-item" v-for="(paper, index) in papers" :key="paper.id">
      <!-- 列表模式下的内容 -->
      <template v-if="viewMode === 'list'">
        <view class="paper-main">
          <view class="paper-info" @click="handleDetailClick(paper)">
            <text class="paper-name">{{ paper.student && paper.student.name || `试卷 ${index + 1}` }}</text>
            <text class="paper-time">{{ formatDate(paper.createTime) }}</text>
          </view>

          <view class="paper-progress" v-if="paper.status === 'corrected'" @click="handleDetailClick(paper)">
            <text class="progress-text">{{ paper.correctRate }}%</text>
            <uni-icons type="right" color="#4285F4" size="16"></uni-icons>
          </view>
          <view class="paper-status"
            :class="{
              'status-processing': paper.status === 'processing' || paper.status === 'pending',
              'status-error': paper.status === 'error',
              'status-uploading': paper.status === 'uploading',
              'status-no-student': paper.status === 'no-student'
            }"
            v-else>
            <view v-if="paper.status === 'pending'" class="status-with-icon">
              <view class="loading-spinner">
                <uni-icons type="refresh" size="20" color="#4285F4"></uni-icons>
              </view>
              <text>排队中</text>
            </view>
            <view v-if="paper.status === 'processing'" class="status-with-icon">
              <view class="loading-spinner">
                <uni-icons type="refresh" size="20" color="#4285F4"></uni-icons>
              </view>
              <text>批改中</text>
            </view>
            <view v-if="paper.status === 'error'" class="status-with-icon">
              <text class="error-text">
                <uni-icons type="closeempty" size="20" color="#FF5252" style="margin-right: 6rpx;"></uni-icons>
                失败
              </text>
              <view class="retry-btn" @click.stop="handleRetry(paper)">
                <text>重试</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 列表模式下的操作按钮 -->
        <view class="paper-actions">
          <view class="left-actions">
            <view class="action-btn" @tap.stop="handlePreviewClick(paper)">
              <uni-icons type="eye" size="16" color="#666666"></uni-icons>
              <text>预览</text>
            </view>

            <view v-if="paper.status === 'corrected'">
              <view class="action-btn select-student-btn" @tap.stop="openStudentSelector(paper)">
                <uni-icons type="compose" size="16" color="#666666"></uni-icons>
                <text>{{ paper.studentId === -1 ? '选择学生' : '修改学生' }}</text>
              </view>
            </view>
          </view>
          <view class="action-btn" @tap.stop="handleDeleteClick(paper)">
            <uni-icons type="trash" size="16" color="#666666"></uni-icons>
            <text>删除</text>
          </view>
        </view>
      </template>

      <!-- 网格模式下的内容 -->
      <template v-else>
        <view class="grid-paper-content">
          <view class="grid-paper-image-wrapper">
            <image 
              :src="paper.images" 
              mode="aspectFill" 
              class="grid-paper-image"
              @tap.stop="handlePreviewClick(paper)"
            ></image>
            <view class="grid-paper-overlay">
              <view class="grid-paper-header">
                <view class="grid-paper-name">{{ paper.student && paper.student.name || `试卷 ${index + 1}` }}</view>
                <view class="grid-paper-status" :class="{
                  'status-processing': paper.status === 'processing' || paper.status === 'pending',
                  'status-error': paper.status === 'error',
                  'status-corrected': paper.status === 'corrected'
                }">
                  <template v-if="paper.status === 'corrected'">
                    <text class="score">{{ paper.correctRate }}%</text>
                  </template>
                  <template v-else-if="paper.status === 'processing' || paper.status === 'pending'">
                    <view class="loading-spinner">
                      <uni-icons type="refresh" size="16" color="#fff"></uni-icons>
                    </view>
                    <text>批改中</text>
                  </template>
                  <template v-else-if="paper.status === 'error'">
                    <uni-icons type="closeempty" size="16" color="#fff"></uni-icons>
                    <text>失败</text>
                  </template>
                </view>
              </view>
              <view class="grid-paper-footer">
                <view class="grid-action-btn select-student" @tap.stop="openStudentSelector(paper)">
                  <uni-icons type="compose" size="20" color="#4285F4"></uni-icons>
                </view>
                <view class="grid-action-btn delete" @tap.stop="handleDeleteClick(paper)">
                  <uni-icons type="trash" size="20" color="#FF5252"></uni-icons>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>
  </view>

  <!-- 共享的学生选择器组件 -->
  <student-selector
    v-if="showStudentSelector"
    :paper="currentPaper"
    :class-id="classId"
    :task-id="taskId"
    @select="handleStudentSelect"
    @visibleChange="handleSelectorVisibleChange"
  />
</template>

<script setup>
import { deleteTaskItem } from '@/api/task-item';
import { ref, onMounted } from 'vue';
import StudentSelector from '@/components/StudentSelector.vue';
import { formatDate } from "@/utils/tools";

const props = defineProps({
  papers: {
    type: Array,
    default: () => []
  },
  classId: {
    type: [String, Number],
    default: ''
  },
  taskId: {
    type: [String, Number],
    default: ''
  },
  viewMode: {
    type: String,
    default: 'list',
    validator: (value) => ['list', 'grid'].includes(value)
  }
});

// 在组件挂载时记录props数据
onMounted(() => {
});

const emit = defineEmits(['retry', 'selectStudent', 'delete', 'refresh', 'student-selector-visible-change']);

// 当前选中的试卷，用于学生选择器
const currentPaper = ref(null);
// 控制学生选择器显示状态
const showStudentSelector = ref(false);

// 打开学生选择器
const openStudentSelector = (paper) => {
  currentPaper.value = paper;
  showStudentSelector.value = true;
  emit('student-selector-visible-change', { visible: true });
};

// 处理选择器可见性变化
const handleSelectorVisibleChange = (data) => {
  const { visible } = data;

  if (!visible) {
    showStudentSelector.value = false;
    emit('student-selector-visible-change', { visible: false });
    // 在选择器关闭后清除当前试卷
    setTimeout(() => {
      currentPaper.value = null;
    }, 200);
  }
};

// 处理学生选择
const handleStudentSelect = (data) => {
  emit('selectStudent', data);
  // 选择完成后关闭选择器
  showStudentSelector.value = false;
};

// 处理查看详情点击事件
const handleDetailClick = (paper) => {
  // 检查试卷状态是否为已批改完成
  if (paper.status === 'corrected') {
    uni.navigateTo({
      url: `/pages/grade-query/detail?id=${paper.id}`
    });
  } else {
    // 如果未批改完成，显示提示信息
    uni.showToast({
      title: '请等待批改完成',
      icon: 'none'
    });
  }
};

// 处理预览点击事件
const handlePreviewClick = (paper) => {
  // 获取原始图片路径
  if (!paper.images) {
    uni.showToast({
      title: '图片路径不存在',
      icon: 'none'
    });
    return;
  }

  // 使用微信小程序预览图片API
  uni.previewImage({
    current: paper.images,
    urls: [paper.images],
    indicator: 'number',
    loop: true,
    fail: (err) => {
      uni.showToast({
        title: '图片预览失败',
        icon: 'none'
      });
    }
  });
};

// 处理重试按钮点击
const handleRetry = (paper) => {
  emit('retry', paper);
};

// 处理删除按钮点击
const handleDeleteClick = (paper) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这份试卷吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' });
          await deleteTaskItem(paper.id);
          emit('delete', paper);
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });
        } catch (error) {
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none'
          });
        } finally {
          uni.hideLoading();
        }
      }
    }
  });
};
</script>

<style lang="scss" scoped>
// 文本省略 mixin
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.paper-list {
  border-radius: 20rpx;
  overflow: hidden;
  background: #fff;

  &.grid-mode {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    padding: 20rpx;

    .paper-item {
      border: none;
      padding: 0;
      margin: 0;
      height: auto;
      background: none;
      border-radius: 12rpx;
      overflow: hidden;
      position: relative;

      .grid-paper-content {
        position: relative;
        width: 100%;
        background: #fff;
        border-radius: 12rpx;
        overflow: hidden;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .grid-paper-image-wrapper {
          position: relative;
          width: 100%;
          height: 400rpx;
          border-radius: 16rpx;
          overflow: hidden;
          box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.98);
          }

          .grid-paper-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;

            &:active {
              transform: scale(1.05);
            }
          }

          .grid-paper-overlay {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            pointer-events: none;

            .grid-paper-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 24rpx;
              background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
              pointer-events: auto;

              .grid-paper-name {
                font-size: 30rpx;
                color: #fff;
                @include text-ellipsis;
                flex: 1;
                margin-right: 16rpx;
                text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
              }

              .grid-paper-status {
                display: flex;
                align-items: center;
                gap: 6rpx;
                font-size: 26rpx;
                color: #fff;
                padding: 6rpx 12rpx;
                border-radius: 20rpx;
                background: rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(4rpx);

                .loading-spinner {
                  animation: spin 1.2s linear infinite;
                  display: flex;
                  align-items: center;
                }

                &.status-processing {
                  color: #fff;
                }

                &.status-error {
                  color: #fff;
                }

                &.status-corrected {
                  color: #fff;
                }

                .score {
                  font-weight: 600;
                }
              }
            }

            .grid-paper-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 24rpx;
              background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
              pointer-events: auto;

              .grid-action-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 80rpx;
                height: 80rpx;
                border-radius: 12rpx;
                transition: all 0.3s ease;
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(4rpx);
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

                &.select-student {
                  &:active {
                    background: rgba(66, 133, 244, 0.4);
                    transform: scale(0.95);
                  }
                }

                &.delete {
                  &:active {
                    background: rgba(255, 82, 82, 0.4);
                    transform: scale(0.95);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .paper-item {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    border-bottom: 1px solid #EEEEEE;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #F8F9FA;
    }

    .paper-main {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
    }

    .paper-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      cursor: pointer;

      .paper-name {
        font-size: 32rpx;
        color: #333;
        margin-bottom: 12rpx;
        font-weight: 500;
      }

      .paper-time {
        font-size: 26rpx;
        color: #999;
      }
    }

    .paper-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 20rpx;
      border-top: 1px solid #F5F5F5;
      margin: 0 -32rpx;
      padding-left: 32rpx;
      padding-right: 32rpx;
    }

    .paper-preview {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #F0F7FF;
      border-radius: 50%;
      width: 72rpx;
      height: 72rpx;
      cursor: pointer;
      margin-right: 24rpx;
      transition: all 0.2s ease;

      &:hover {
        background-color: #E1EFFE;
      }

      &:active {
        transform: scale(0.95);
      }
    }

    .paper-status {
      font-size: 28rpx;
      color: #999;
      min-width: 120rpx;
      text-align: right;
      cursor: pointer;

      &.status-processing {
        color: #4285F4;
      }

      &.status-error {
        color: #FF5252;
      }

      &.status-uploading {
        color: #22C55E;
      }

      &.status-no-student {
        color: #F5A623;
      }

      .status-with-icon {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        text {
          margin-left: 8rpx;
        }

        .error-text {
          display: flex;
          align-items: center;
          margin-right: 12rpx;
        }

        .retry-btn {
          background-color: #FF5252;
          color: #FFFFFF;
          font-size: 24rpx;
          padding: 8rpx 20rpx;
          border-radius: 8rpx;
          margin-left: 12rpx;
          transition: opacity 0.2s ease;

          &:active {
            opacity: 0.8;
          }
        }

        .select-student-btn {
          background-color: #4285F4;
          color: #FFFFFF;
          font-size: 24rpx;
          padding: 8rpx 20rpx;
          border-radius: 8rpx;
          transition: opacity 0.2s ease;

          &:active {
            opacity: 0.8;
          }
        }
      }

      .loading-spinner {
        animation: spin 1.2s linear infinite;
        display: inline-block;
      }

      .loading-uploader {
        animation: bounce 1s ease infinite;
        display: inline-block;
      }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes bounce {
      0%, 100% { transform: translateY(0); }
      50% { transform: translateY(-5rpx); }
    }

    .paper-progress {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      cursor: pointer;
      min-width: 120rpx;

      .progress-text {
        font-size: 32rpx;
        color: $uni-primary-color;
        margin-right: 8rpx;
      }
    }
  }
}

.paper-delete {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFF2F2;
  border-radius: 50%;
  width: 72rpx;
  height: 72rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #FFE5E5;
  }

  &:active {
    transform: scale(0.95);
  }
}

.paper-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid #F5F5F5;
  margin: 0 -32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;

  .left-actions {
    display: flex;
    align-items: center;
    gap: 16rpx;
  }

  .action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6rpx;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
    transition: all 0.2s ease;
    background-color: #F5F5F5;
    color: #666666;
    line-height: 1;

    .uni-icons {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    text {
      display: inline-block;
      vertical-align: middle;
    }

    &:active {
      background-color: #EEEEEE;
      opacity: 0.8;
    }
  }

  .preview-btn {
    background-color: #F0F7FF;
    color: #4285F4;

    &:active {
      background-color: #E1EFFE;
    }
  }

  .delete-btn {
    background-color: #FFF2F2;
    color: #FF5252;

    &:active {
      background-color: #FFE5E5;
    }
  }

  .select-student-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6rpx;
    font-size: 24rpx;
    padding: 8rpx 16rpx;
    border-radius: 6rpx;
    transition: all 0.2s ease;
    background-color: #F5F5F5;
    color: #666666;
    line-height: 1;

    .uni-icons {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    text {
      display: inline-block;
      vertical-align: middle;
    }

    &:active {
      background-color: #EEEEEE;
      opacity: 0.8;
    }
  }

  // 已选择学生时的样式
  .paper-item[data-has-student="true"] {
    .select-student-btn {
      background-color: #F0FFF4;
      color: #22C55E;

      &:active {
        background-color: #DCFCE7;
      }
    }
  }
}

// 删除不再需要的样式
.paper-preview,
.paper-delete {
  display: none;
}

// 学生选择器相关样式
.student-selector-container {
  position: relative;
}

.student-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 400rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  margin-top: 8rpx;
  padding: 16rpx;
}

.search-input-box {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 16rpx;
  margin-bottom: 16rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.student-list {
  max-height: 400rpx;
}

.student-item {
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #F5F5F5;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #F8F9FA;
  }
}

.empty-result {
  color: #999;
  text-align: center;
}

.loading-students {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  color: #4285F4;
  font-size: 26rpx;

  .uni-icons {
    margin-right: 8rpx;
    animation: spin 1.2s linear infinite;
  }
}
</style>
