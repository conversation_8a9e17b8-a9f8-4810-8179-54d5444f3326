<template>
  <!-- 试卷列表项 -->
  <view class="paper-item" :class="{'simplified-mode': mode === 'simplified'}">
    <view class="paper-main">
      <view class="paper-info" @click="handleDetailClick(paper)">
        <text v-if="paper.status === 'corrected'" class="paper-name" :class="{'no-name': !paper.student || !paper.student.name}">
          {{ paper.student && paper.student.name || `未识别到姓名` }}
        </text>
        <text class="paper-time">{{ formatDate(paper.createTime) }}</text>
      </view>

      <view class="paper-progress" v-if="paper.status === 'corrected'" @click="handleDetailClick(paper)">
        <text class="progress-text">{{ paper.correctRate }}分</text>
        <uni-icons type="right" color="#4285F4" size="16"></uni-icons>
      </view>
      <view class="paper-status"
        :class="{
          'status-processing': paper.status === 'processing' || paper.status === 'pending',
          'status-error': paper.status === 'error',
          'status-uploading': paper.status === 'uploading',
          'status-no-student': paper.status === 'no-student'
        }"
        v-else>
        <view v-if="paper.status === 'pending'" class="status-with-icon">
          <view class="loading-spinner">
            <uni-icons type="refresh" size="20" color="#4285F4"></uni-icons>
          </view>
          <text>排队中</text>
        </view>
        <view v-if="paper.status === 'processing'" class="status-with-icon">
          <view class="loading-spinner">
            <uni-icons type="refresh" size="20" color="#4285F4"></uni-icons>
          </view>
          <text>批改中</text>
        </view>
        <view v-if="paper.status === 'error'" class="status-with-icon">
          <text class="error-text">
            {{ isCorrectionLimitError ? '批改次数不足' : paper.errorAnalysis }}
          </text>
          <view class="error-buttons">
            <view class="retry-btn" @click.stop="handleRetry(paper)">
              <text>重试</text>
            </view>
            <view v-if="isCorrectionLimitError" class="buy-btn" @click.stop="handleBuyClick()">
              <text>去订阅</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 (仅在普通模式下显示) -->
    <view class="paper-actions" v-if="mode === 'normal'">
      <view class="left-actions">
        <view class="action-btn" @tap.stop="handlePreviewClick(paper)">
          <uni-icons type="eye" size="16" color="#666666"></uni-icons>
          <text>预览</text>
        </view>

        <view v-if="paper.status === 'corrected'">
          <view class="action-btn select-student-btn" @tap.stop="openStudentSelector(paper)">
            <uni-icons type="compose" size="16" color="#666666"></uni-icons>
            <text>{{ paper.studentId === -1 ? '选择学生' : '修改学生' }}</text>
          </view>
        </view>
      </view>
      <view class="action-btn" @tap.stop="handleDeleteClick(paper)">
        <uni-icons type="trash" size="16" color="#666666"></uni-icons>
        <text>删除</text>
      </view>
    </view>
  </view>

  <!-- 共享的学生选择器组件 -->
  <student-selector
    v-if="showStudentSelector"
    :paper="currentPaper"
    :class-id="classId"
    :task-id="taskId"
    @select="handleStudentSelect"
    @visibleChange="handleSelectorVisibleChange"
  />
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import StudentSelector from '@/components/StudentSelector.vue';
import { formatDate } from "@/utils/tools";

const props = defineProps({
  paper: {
    type: Object
  },
  classId: {
    type: [String, Number],
    default: ''
  },
  taskId: {
    type: [String, Number],
    default: ''
  },
  mode: {
    type: String,
    default: 'normal',
    validator: (value) => ['normal', 'simplified'].includes(value)
  },
  sortField: {
    type: String,
    default: ''
  }
});

// 在组件挂载时记录props数据
onMounted(() => {
});

const emit = defineEmits(['retry', 'selectStudent', 'delete', 'refresh', 'student-selector-visible-change']);

// 当前选中的试卷，用于学生选择器
const currentPaper = ref(null);
// 控制学生选择器显示状态
const showStudentSelector = ref(false);

// 判断是否为批改次数不足错误
const isCorrectionLimitError = computed(() => {
  return props.paper?.errorAnalysis?.includes('次数不足') ||
         props.paper?.errorAnalysis?.includes('次数已用完');
});

// 打开学生选择器
const openStudentSelector = (paper) => {
  currentPaper.value = paper;
  showStudentSelector.value = true;
  emit('student-selector-visible-change', { visible: true });
};

// 处理选择器可见性变化
const handleSelectorVisibleChange = (data) => {
  const { visible } = data;

  if (!visible) {
    showStudentSelector.value = false;
    emit('student-selector-visible-change', { visible: false });
    // 在选择器关闭后清除当前试卷
    setTimeout(() => {
      currentPaper.value = null;
    }, 200);
  }
};

// 处理学生选择
const handleStudentSelect = (data) => {
  emit('selectStudent', data);
  // 选择完成后关闭选择器
  showStudentSelector.value = false;
};

// 处理查看详情点击事件
const handleDetailClick = (paper) => {
  // 检查试卷状态是否为已批改完成
  if (paper.status === 'corrected') {
    // 构建URL，添加sortField参数
    const url = `/pages/essay-correction/detail/index?id=${paper.id}${props.sortField ? `&sortField=${props.sortField}` : ''}`;
    uni.navigateTo({
      url
    });
  } else {
    // 如果未批改完成，显示提示信息
    uni.showToast({
      title: '请等待批改完成',
      icon: 'none'
    });
  }
};

// 处理预览点击事件
const handlePreviewClick = (paper) => {
  // 获取原始图片路径
  if (!paper.images) {
    uni.showToast({
      title: '图片路径不存在',
      icon: 'none'
    });
    return;
  }

  // 使用微信小程序预览图片API
  uni.previewImage({
    current: paper.images,
    urls: [paper.images],
    indicator: 'number',
    loop: true,
    fail: (err) => {
      uni.showToast({
        title: '图片预览失败',
        icon: 'none'
      });
    }
  });
};

// 处理重试按钮点击
const handleRetry = (paper) => {
  emit('retry', paper);
};

// 处理删除按钮点击
const handleDeleteClick = (paper) => {
  emit('delete', paper);
};

// 处理购买按钮点击
const handleBuyClick = () => {
  uni.navigateTo({
    url: '/pages/package/index'
  });
};
</script>

<style lang="scss" scoped>
// 文本省略 mixin
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.paper-item {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 32rpx;

  &.simplified-mode {
    padding: 20rpx 32rpx;
  }
  border-bottom: 1px solid #EEEEEE;
  transition: background-color 0.2s ease;

  &:active {
    background-color: #F8F9FA;
  }

  // Simplified mode adjustments
  &.simplified-mode {
    border: none;
    padding: 24rpx;
    margin: 0;
    border-radius: 12rpx;
    overflow: hidden;
  }

  .paper-main {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
  }

  .paper-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    cursor: pointer;

    .paper-name {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 12rpx;
      font-weight: 500;

      &.no-name {
        color: #999;
        font-size: 28rpx;
        font-weight: normal;
        font-style: italic;
      }
    }

    .paper-time {
      font-size: 26rpx;
      color: #999;
    }
  }

  .paper-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20rpx;
    border-top: 1px solid #F5F5F5;
    margin: 0 -32rpx;
    padding-left: 32rpx;
    padding-right: 32rpx;

    .left-actions {
      display: flex;
      align-items: center;
      gap: 16rpx;
    }

    .action-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 6rpx;
      font-size: 24rpx;
      padding: 8rpx 16rpx;
      border-radius: 6rpx;
      transition: all 0.2s ease;
      background-color: #F5F5F5;
      color: #666666;
      line-height: 1;

      .uni-icons {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      text {
        display: inline-block;
        vertical-align: middle;
      }

      &:active {
        background-color: #EEEEEE;
        opacity: 0.8;
      }
    }
  }

  .paper-status {
    font-size: 28rpx;
    color: #999;
    min-width: 120rpx;
    text-align: right;
    cursor: pointer;

    &.status-processing {
      color: #4285F4;
    }

    &.status-error {
      color: #FF6B6B;
    }

    &.status-uploading {
      color: #22C55E;
    }

    &.status-no-student {
      color: #F5A623;
    }

    .status-with-icon {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      text {
        margin-left: 8rpx;
      }

      .error-text {
        display: flex;
        align-items: center;
        margin-right: 12rpx;
        color: #FF6B6B;
      }

      .error-buttons {
        display: flex;
        align-items: center;
        gap: 12rpx;
      }

      .retry-btn, .buy-btn {
        color: #FFFFFF;
        font-size: 24rpx;
        padding: 8rpx 20rpx;
        border-radius: 8rpx;
        transition: all 0.2s ease;
        opacity: 0.9;

        &:active {
          opacity: 0.7;
          transform: scale(0.98);
        }
      }

      .retry-btn {
        background-color: #FF6B6B;
      }

      .buy-btn {
        background-color: #4285F4;
      }
    }

    .loading-spinner {
      animation: spin 1.2s linear infinite;
      display: inline-block;
    }
  }

  .paper-progress {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    cursor: pointer;
    min-width: 120rpx;

    .progress-text {
      font-size: 32rpx;
      color: $uni-primary-color;
      margin-right: 8rpx;
    }
  }

  // 简洁模式样式调整
  &.simplified-mode {
    padding: 24rpx;
    border-bottom: 1px solid #EEEEEE; // 添加底部分割线
    box-shadow: none; // 移除阴影效果，让分割线更清晰
    border-radius: 0; // 移除圆角
    margin-bottom: 0; // 移除项目间距

    .paper-main {
      margin-bottom: 0; // 移除底部间距，因为没有操作按钮
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

