<template>
  <page-meta :page-style="'overflow:'+(showFilterModal || showStudentSelectorModal?'hidden':'visible')"></page-meta>
  <loading-mask :visible="loadingTaskInfo" text="请稍候..."></loading-mask>
  <!-- 试卷列表 -->
  <z-paging
      ref="paging"
      use-virtual-list
      :force-close-inner-list="true"
      :auto-show-back-to-top="true"
      :auto-clean-list-when-reload="true"
      :default-page-size="10"
      :cell-height="viewMode === 'list' ? 200 : 300"
      :cell-height-mode="viewMode === 'list' ? 'fixed' : 'dynamic'"
      @virtualListChange="virtualListChange"
      loading-more-no-more-text="我也是有底线的！"
      @query="queryList"
      :preload-page="4"
  >

    <view class="ai-correction-container">
      <!-- 步骤指示器  -->
      <Stepper :active-step="2"/>

      <!-- 相机组件，仅在连续拍摄模式下显示 -->
      <CameraComponent
          v-if="showCamera"
          @close="showCamera=false"
          @photo-taken="handlePhotoTaken"
      />


      <!-- 内容区域 -->
      <view class="content" v-if="taskData.totalCorrectionCount === 0">
        <!-- 上传提示信息 -->
        <view class="upload-info-wrapper">
          <UploadInfo
              :current-count="taskData.totalCorrectionCount"
              :max-count="taskData.correctionLimit"
          />
        </view>
      </view>

      <view class="content" v-else>
        <!-- 分数概览 -->
        <ScoreSummaryLocal
            :avg-score="taskData.averageAccuracy"
            :highest-score="taskData.maxAccuracy"
            :lowest-score="taskData.minAccuracy"
        />
        <FrequentErrors :error-words="frequentErrors" />

        <!-- 批改进度 -->
        <ProgressBar
            :corrected-count="taskData.completedCorrectionCount"
            :submitted-count="taskData.totalCorrectionCount"
            :studentsWithoutTaskItemsCount="taskData.studentsWithoutTaskItemsCount"
            :total-students="taskData.correctionLimit"
            @check-unsubmitted="handleCheckUnsubmitted"
        />

        <!-- 视图模式切换和更多操作 -->
        <view class="filter-and-view-mode">
          <!-- 未识别姓名开关 -->
          <view class="filter-switch-container" :class="{'active': studentFilter === -1}">
            <view class="filter-info">
              <text>筛选无姓名</text>
              <text v-if="studentFilter === -1" class="count">(共{{total}}份)</text>
            </view>
            <switch style="transform:scale(0.8)" :checked="studentFilter === -1" @change="handleStudentFilterChange" :color="studentFilter === -1 ? '#ff4d4f' : '#4285F4'" />
          </view>

          <!-- 视图模式切换 -->
          <view class="view-mode-btn" @click="toggleViewMode">
            <text>{{ viewMode === 'list' ? '简洁' : '普通' }}</text>
          </view>

          <!-- 分数排序 -->
          <view class="sort-btn" @click="toggleSort" :class="{'active': sortField === '1' || sortField === '2'}">
            <text>分数</text>
            <image
              v-if="sortField === '1' || sortField === '2'"
              class="sort-icon"
              :src="`/static/icons/arrow-${sortField === '1' ? 'down' : 'up'}.svg`"
              mode="aspectFit"
            />
          </view>

          <!-- ID排序 -->
          <view class="sort-btn" @click="toggleIdSort" :class="{'active': sortField === '3' || sortField === '4'}">
            <text>上传时间</text>
            <image
              v-if="sortField === '3' || sortField === '4'"
              class="sort-icon"
              :src="`/static/icons/arrow-${sortField === '3' ? 'down' : 'up'}.svg`"
              mode="aspectFit"
            />
          </view>
        </view>

        <!-- 创建中 -->
        <view class="upload-status-info" v-if="creating">
          <view class="loading-icon">
            <uni-icons type="refresh" size="20" color="#4285F4"></uni-icons>
          </view>
          <text>正在创建，请稍候...</text>
        </view>

        <!-- 试卷列表 -->
        <view class="paper-list-container" :class="{'grid-mode': viewMode === 'grid'}">
          <view
            v-for="item in virtualList"
            :key="item.id"
            :id="`zp-id-${item.zp_index}`"
            class="paper-item"
          >
            <PaperItem
              :paper="item"
              :class-id="taskData.classId"
              :task-id="taskData.id"
              :mode="viewMode === 'list' ? 'normal' : 'simplified'"
              :sort-field="sortField"
              @retry="handleRetry"
              @selectStudent="handleStudentConfirm"
              @delete="handleDelete"
              @student-selector-visible-change="handleStudentSelectorVisibleChange"
            />
          </view>
        </view>
      </view>

      <!-- 筛选弹窗 -->
      <RateFilterModal
          v-model:visible="showFilterModal"
          :initial-value="correctRateFilter"
          :initial-min-value="0"
          :initial-max-value="taskData.rubric?.totalScore || 100"
          @confirm="handleFilterConfirm"
      />
    </view>
    <!-- 底部操作栏 -->
    <template #bottom v-if="!showCamera && !showFilterModal && !showStudentSelectorModal">
      <view class="footer">
        <BottomActions
            @upload="chooseFile"
            @filter="handleFilter"
            @regularExport="handleExport"
            @batchExport="handleBatchExport"
        />
      </view>
    </template>
  </z-paging>

  <!-- 文件分享模态框 -->
  <view class="share-file-modal" v-if="showShareFileModal">
    <view class="share-file-content">
      <view class="share-file-title">文件已准备好</view>
      <view class="share-file-message">点击下方按钮分享文件到微信</view>
      <view class="share-file-actions">
        <button class="cancel-btn" @click="showShareFileModal = false">取消</button>
        <button class="share-btn" @click="shareFile">分享文件</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import {onUnmounted, ref, watch} from 'vue';
import {onLoad} from '@dcloudio/uni-app';
import Stepper from '@/components/Stepper.vue';
import ProgressBar from './components/ProgressBar.vue';
import PaperItem from './components/PaperItem.vue';
import RateFilterModal from './components/RateFilterModal.vue';
import ScoreSummaryLocal from './components/ScoreSummary.vue';
import BottomActions from './components/BottomActions.vue';
import CameraComponent from './components/CameraComponent.vue';
import UploadInfo from './components/UploadInfo.vue';
import FrequentErrors from './components/FrequentErrors.vue';
import { getCurrentTeacher } from '@/api/teacher';
import { useUserStore } from '@/store/user';

import {
  exportTaskListExcel,
  getTaskItemList,
  updateTaskItem,
  deleteTaskItem,
  getTaskItemStatistics,
  batchExportTaskExcel,
  createTaskItemV2, retryTaskItemV2
} from '@/api/task-item';
import {uploadImage} from "@/libs/utils";
import {getTaskInfo} from "@/api/task";
import LoadingMask from "@/components/LoadingMask.vue";

// 生成安全的文件名，限制长度并移除特殊字符
const generateSafeFileName = (title, maxLength = 15) => {
  if (!title) return '批改任务';

  // 移除特殊字符，只保留中文、英文、数字、下划线和短横线
  const cleanTitle = title.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_-]/g, '_');

  // 限制长度
  if (cleanTitle.length <= maxLength) {
    return cleanTitle;
  }

  // 如果超长，截取前面部分并添加任务ID后缀以保证唯一性
  return cleanTitle.substring(0, maxLength - 8) + '_' + Date.now().toString().slice(-6);
};

const taskData = ref({});
const loadingTaskInfo = ref(true);
const refreshTimer = ref(null);
const userStore = useUserStore();
const userInfo = ref({});
const taskId = ref('');

// 虚拟列表数据
const virtualList = ref([]);
const paging = ref(null);


// 分页相关参数
const minCorrectRate = ref(0);
const maxCorrectRate = ref(100);
const studentFilter = ref(0); // 添加学生筛选参数，默认为0表示不筛选
const total = ref(0);

// 相机控制
const showCamera = ref(false);

// 筛选功能相关状态
const showFilterModal = ref(false);
const correctRateFilter = ref('');

// 更多操作下拉菜单状态
const showMoreOptions = ref(false);


// 显示提示信息
const showToast = (title, icon = 'none') => {
  uni.showToast({
    title,
    icon,
    duration: 2000
  });
};


// 学生选择器状态控制
const showStudentSelectorModal = ref(false);

watch(virtualList, () => {
  if (checkNeedAutoRefresh()) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
}, {deep: true});

// 检查是否需要自动刷新
const checkNeedAutoRefresh = () => {
  if (!virtualList.value || virtualList.value.length === 0) return false;
  return virtualList.value.some(paper => paper.status === 'pending' || paper.status === 'processing');
};

// 启动自动刷新
const startAutoRefresh = () => {
  // 只有在定时器不存在时才创建新的定时器
  if (!refreshTimer.value) {
    console.log('开始自动刷新');
    refreshTimer.value = setInterval(() => {
      console.log('执行自动刷新');
      refreshData();
    }, 10000);
  }
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    console.log('停止自动刷新');
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }
};

// 组件卸载时清理定时器和事件监听器
onUnmounted(() => {
  stopAutoRefresh();
  // 移除分数更新事件监听器
  uni.$off('taskItemScoreUpdated');
});

// 处理未提交学生查看
const handleCheckUnsubmitted = () => {
  uni.navigateTo({
    url: `/pages/student-list-without-paper/index?classId=${taskData.value.classId}&taskId=${taskId.value}`
  });
}

// 处理导出按钮点击
const handleExport = async () => {
  if (virtualList.value.length === 0) {
    showToast('没有可导出的结果，请先上传作业');
    return;
  }

  try {
    uni.showLoading({ title: '正在导出...' });
    const requestParams = {
      taskId: taskId.value,
      minCorrectRate: minCorrectRate.value,
      maxCorrectRate: maxCorrectRate.value,
      pageNo: 1,
      pageSize: 1,
    }
    // 只有在有排序值时才添加排序参数
    if(sortField.value !== '') {
      requestParams.sortField = sortField.value;
    }
    const res = await exportTaskListExcel(requestParams);

    const fs = wx.getFileSystemManager();
    const safeFileName = generateSafeFileName(taskData.value.title);
    const tempFilePath = `${wx.env.USER_DATA_PATH}/${safeFileName}.xlsx`;

    fs.writeFile({
      filePath: tempFilePath,
      data: res,
      encoding: 'binary',
      success: () => {
        uni.hideLoading();
        uni.openDocument({
          filePath: tempFilePath,
          showMenu: true,
          success: () => {
            console.log('文件打开成功');
            // 关闭更多选项菜单
            showMoreOptions.value = false;
          },
          fail: (err) => {
            console.error('文件打开失败:', err);
            uni.showToast({
              title: '文件打开失败',
              icon: 'none'
            });
          }
        });
      },
      fail: (err) => {
        uni.hideLoading();
        console.error('文件保存失败:', err);
        uni.showToast({
          title: '文件保存失败',
          icon: 'none'
        });
      }
    });
  } catch (err) {
    uni.hideLoading();
    console.error('导出失败:', err);
    uni.showToast({
      title: '导出失败',
      icon: 'none'
    });
    // 关闭更多选项菜单
    showMoreOptions.value = false;
  }
};

// 处理筛选按钮点击
const handleFilter = () => {
  showFilterModal.value = true;
  // 关闭更多选项菜单
  showMoreOptions.value = false;
};

/**
 * 切换更多选项菜单的显示状态
 */
const toggleMoreOptions = () => {
  showMoreOptions.value = !showMoreOptions.value;
};

/**
 * 处理批量导出Excel功能
 */
// 下载完成后的文件路径
const downloadedFilePath = ref('');
// 下载文件模态状态
const showShareFileModal = ref(false);

// 处理批量导出
const handleBatchExport = async () => {
  if (virtualList.value.length === 0) {
    showToast('没有可导出的结果，请先上传作业');
    return;
  }

  try {
    uni.showLoading({ title: '正在导出...' });

    // 获取当前教师ID
    const teacherInfo = await getCurrentTeacher();
    const teacherId = teacherInfo.data.id;

    const res = await batchExportTaskExcel({
      taskId: taskId.value,
      teacherId: teacherId
    });

    const fs = wx.getFileSystemManager();
    const safeFileName = generateSafeFileName(taskData.value.title);
    const tempFilePath = `${wx.env.USER_DATA_PATH}/${safeFileName}_批改数据.zip`;

    fs.writeFile({
      filePath: tempFilePath,
      data: res,
      encoding: 'binary',
      success: () => {
        uni.hideLoading();
        // 存储文件路径并显示分享模态
        downloadedFilePath.value = tempFilePath;
        showShareFileModal.value = true;

        uni.showToast({
          title: '导出成功，请点击分享按钮',
          icon: 'none',
          duration: 2000
        });
      },
      fail: (err) => {
        uni.hideLoading();
        console.error('文件保存失败:', err);
        uni.showToast({
          title: '文件保存失败',
          icon: 'none'
        });
      }
    });
  } catch (err) {
    uni.hideLoading();
    console.error('导出失败:', err);
    uni.showToast({
      title: '导出失败',
      icon: 'none'
    });
  }
};

// 手动分享文件
// 必须由用户点击手势直接触发
const shareFile = () => {
  if (!downloadedFilePath.value) {
    showToast('没有可分享的文件');
    return;
  }

  wx.shareFileMessage({
    filePath: downloadedFilePath.value,
    success: () => {
      console.log('文件分享成功');
      showToast('文件分享成功', 'success');
      // 关闭分享模态
      showShareFileModal.value = false;
    },
    fail: (err) => {
      console.error('文件分享失败:', err);
      uni.showToast({
        title: '文件分享失败',
        icon: 'none'
      });
    }
  });
};

const checkUserRemainingCount = () => {
  console.log('检查用户剩余体验次数', userInfo.value.remainCorrectionCount)
  return new Promise((resolve) => {
    if(userInfo.value.isMember) {
      resolve(true);
      return;
    }

    if (!userInfo.value.remainCorrectionCount) {
      uni.showModal({
        title: '提示',
        content: '您的剩余体验次数不足，订阅会员后继续使用',
        confirmText: '去订阅',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/package/index'
            });
          }
          resolve(false);
        }
      });
      return;
    }

    const unsubmittedCount = taskData.value.correctionLimit - taskData.value.totalCorrectionCount;
    if (userInfo.value.remainCorrectionCount < unsubmittedCount) {
      uni.showModal({
        title: '提示',
        content: `您的剩余体验次数(${userInfo.value.remainCorrectionCount}次)不足以处理所有未提交的作业(${unsubmittedCount}份)，是否继续使用剩余体验次数？`,
        confirmText: '去充值',
        cancelText: '继续使用',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: '/pages/package/index'
            });
            resolve(false);
          } else {
            resolve(true);
          }
        }
      });
      return;
    }

    resolve(true);
  });
};

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const res = await getCurrentTeacher();
    userInfo.value = res.data;
    userStore.setUserInfo(res.data);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  }
};

// 显示选择操作菜单
const showActionMenu = () => {
  uni.showActionSheet({
    itemList: ['连续拍摄', '选择图片'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 打开相机进行连续拍摄
        showCamera.value = true;
      } else if (res.tapIndex === 1) {
        // 从相册选择图片
        uni.chooseMedia({
          mediaType: ['image'],
          count: 20, // 最多可选择的图片数量
          sizeType: ['original'], // 可选择原图或压缩图
          sourceType: ['album'], // 仅从相册选择
          success: (res) => {
            // 存储所有选中的图片信息并立即处理
            res.tempFiles.forEach(item => {
              // 立即处理这个文件
              addTaskItem(item.tempFilePath);
            });
          },
          fail: (err) => {
            console.error('图片选择失败:', err);
          }
        });
      }
    }
  });
};

// 文件选择
const chooseFile = async () => {
  const canContinue = await checkUserRemainingCount();
  if (canContinue) {
    showActionMenu();
  }
};

/**
 * 处理拍照结果
 */
const handlePhotoTaken = (fileInfo) => {
  // 立即处理这个文件
  addTaskItem(fileInfo);
};


// 处理重试按钮点击
const handleRetry = async (paper) => {
  try {
    uni.showLoading({ title: '重试中...' });
    const { code, msg } = await retryTaskItemV2(paper.id);
    uni.hideLoading();

    if(code === 0) {
      // 重试后刷新数据
      refreshData();
      return
    }

    if(code === 103002) {
      // 刷新用户数据
      await fetchUserInfo();
    } else {
      uni.showModal({
        title: '提示',
        content: msg,
      })
    }

  } catch (error) {
    uni.hideLoading();
    console.error('重试失败:', error);
    uni.showToast({
      title: '重试失败',
      icon: 'none'
    });
  }
};

// 处理学生选择确认
const handleStudentConfirm = async (data) => {
  const {paper, student} = data;
  try {
    uni.showLoading({ title: '更新中...' });
    const inData = {
      id: paper.id,
      taskId: paper.taskId,
      teacherId: paper.teacherId,
      classId: paper.classId,
      studentId: student.id,
    }
    await updateTaskItem(inData);
    // 重试后刷新数据
    await refreshData();
    uni.showToast({
      title: '学生更新成功',
      icon: 'success'
    });
  } catch (err) {
    console.error('学生更新失败:', err);
    uni.showToast({
      title: '学生更新失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 获取任务详情
const fetchTaskInfo = async (taskId) => {
  try {
    const res = await getTaskInfo(taskId);
    uni.hideLoading();

    taskData.value = res.data;

    // 动态设置页面标题为班级名称
    uni.setNavigationBarTitle({
        title: taskData.value.title
      });
  } catch (error) {
    uni.hideLoading();
    console.error('获取任务详情失败:', error);
    uni.showToast({
      title: '获取任务详情失败',
      icon: 'none'
    });
  } finally {
    loadingTaskInfo.value = false;
  }
};

const handleFilterConfirm = (value) => {
  minCorrectRate.value = value.minRate;
  maxCorrectRate.value = value.maxRate;
  paging.value?.reload();
};

// 处理学生筛选开关变化
const handleStudentFilterChange = (e) => {
  studentFilter.value = e.detail.value ? -1 : 0;
  paging.value?.reload();
};

// 获取任务项列表
const queryList = async (pageNo, pageSize) => {
  try {
    const requestParams = {
      taskId: taskId.value,
      pageNo,
      pageSize,
      minCorrectRate: minCorrectRate.value,
      maxCorrectRate: maxCorrectRate.value,
      studentId: studentFilter.value === -1 ? -1 : '',
    };

    // 只有在有排序值时才添加排序参数
    if(sortField.value !== '') {
      requestParams.sortField = sortField.value;
    }

    const res = await getTaskItemList(requestParams);

    const {list, total: totalCount} = res.data;
    total.value = totalCount;

    // 使用complete方法更新列表数据
    paging.value.complete(list);
  } catch (error) {
    console.error('获取任务项列表失败:', error);
    paging.value.complete(false);
  }
};

// 刷新数据
const refreshData = async () => {
  if (!taskId.value) return;
  await fetchTaskInfo(taskId.value);
  await fetchFrequentErrors(taskId.value);
  paging.value?.refresh();
};

// 处理学生选择器可见性变化
const handleStudentSelectorVisibleChange = (data) => {
  showStudentSelectorModal.value = data.visible;
};

// 页面加载时获取URL参数
onLoad((option) => {
  if (option.taskId) {
    // 使用taskId获取任务详情
    fetchTaskInfo(option.taskId);
    fetchFrequentErrors(option.taskId);
    taskId.value = option.taskId;
    // 获取用户信息
    fetchUserInfo();
  } else {
    uni.showToast({
      title: '缺少任务ID参数',
      icon: 'none'
    });
  }

  // 监听列表刷新事件
  uni.$on('refreshTaskList', () => {
    refreshData();
  });
});

const viewMode = ref('list');
const creating = ref(false);
const sortField = ref(''); // 排序字段：'' 表示不排序，'1' 表示分数降序，'2' 表示分数升序，'3' 表示ID降序，'4' 表示ID升序

// 切换视图模式
const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'list' ? 'grid' : 'list';
};

// 切换分数排序方向
const toggleSort = () => {
  // 在三种状态之间循环：不排序 -> 分数降序(1) -> 分数升序(2) -> 不排序
  if(sortField.value === '1') {
    sortField.value = '2'; // 切换到分数升序
  } else if(sortField.value === '2') {
    sortField.value = ''; // 重置为不排序
  } else {
    sortField.value = '1'; // 设置为分数降序
  }
  paging.value?.reload();
};

// 切换ID排序方向
const toggleIdSort = () => {
  // 在三种状态之间循环：不排序 -> ID降序(3) -> ID升序(4) -> 不排序
  if(sortField.value === '3') {
    sortField.value = '4'; // 切换到ID升序
  } else if(sortField.value === '4') {
    sortField.value = ''; // 重置为不排序
  } else {
    sortField.value = '3'; // 设置为ID降序
  }
  paging.value?.reload();
};

// 添加任务项
const addTaskItem = async (tempPath) => {
  // 先上传图片
  let fileUrl = ''
  const startTime = Date.now()
  if(taskData.value.totalCorrectionCount === 0) {
    uni.showLoading({
      title: '创建中...'
    })
  }
  try {
    creating.value = true
    const {data} = await uploadImage(tempPath);
    fileUrl = data.ossUrl;
  } catch (err) {
    console.error('图片上传失败:', err);
    uni.showModal({
      title: '提示',
      content: '图片上传失败，请稍后重试',
      showCancel: false
    });
    creating.value = false
    return;
  } finally {
    uni.hideLoading()
  }

  try {
    // 调用创建任务项API
    const { code, msg } = await createTaskItemV2({
      taskId: taskId.value,
      teacherId: taskData.value.teacherId,
      classId: taskData.value.classId,
      images: fileUrl,
      imageUrl: fileUrl,
    });

    if(code === 0) {
      // 计算已经过去的时间
      const elapsedTime = Date.now() - startTime
      // 如果时间不足1秒，则等待剩余时间
      if (elapsedTime < 1000) {
        await new Promise(resolve => setTimeout(resolve, 1000 - elapsedTime))
      }

      creating.value = false

      // 创建成功后直接刷新列表
      refreshData();
      return
    }

    if(code === 103002) {
      // 刷新用户数据
      await fetchUserInfo();
    } else {
      uni.showModal({
        title: '提示',
        content: msg,
      })
    }
  } catch (error) {
    console.error('任务项创建失败:', error);
    uni.showToast({
      title: '任务项创建失败',
      icon: 'none'
    });
    creating.value = false
  }
};

// 监听虚拟列表变化
const virtualListChange = (vList) => {
  virtualList.value = vList;
};

// 监听视图模式变化
watch(viewMode, () => {
  paging.value?.reload();
});

const deleteTaskItemById = async (id) => {
  try {
    uni.showLoading({ title: '删除中...' });
    await deleteTaskItem(id);
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    });
    // 刷新数据
    refreshData();
  } catch (error) {
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 处理删除操作
const handleDelete = (paper) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这份试卷吗？',
    success: (res) => {
      if (res.confirm) {
        deleteTaskItemById(paper.id)
      }
    }
  });
};

const frequentErrors = ref([]);

// 获取高频错词统计
const fetchFrequentErrors = async (taskId) => {
  try {
    const { data } = await getTaskItemStatistics(taskId);
    frequentErrors.value = data
  } catch (error) {
    console.error('获取高频错词统计失败:', error);
  }
};
</script>

<style>
page {
  padding: 28rpx;
  box-sizing: border-box;
}
</style>

<style lang="scss" scoped>
// 内容区域
.content {
  margin-top: 140rpx; // 为固定的header留出空间
  padding: 28rpx;
  padding-bottom: 0;
  flex: 1;
}

// 上传提示信息容器
.upload-info-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 40rpx;
}

// 上传状态信息
.upload-status-info {
  background-color: #EBF3FF;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  margin: 20rpx 0;
  display: flex;
  align-items: center;

  .loading-icon {
    animation: spin 1.5s linear infinite;
    margin-right: 20rpx;
  }

  text {
    color: #4285F4;
    font-size: 28rpx;
  }
}

// 筛选和视图模式容器
.filter-and-view-mode {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 10rpx;
  position: relative;
}

// 筛选开关容器
.filter-switch-container {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 16rpx 20rpx;
  border-radius: 10rpx;
  transition: all 0.3s ease;
  height: 72rpx;
  box-sizing: border-box;

  &.active {
    background-color: #fff1f0;
  }

  .filter-info {
    display: flex;
    align-items: center;
    gap: 10rpx;
    white-space: nowrap;

    text {
      font-size: 24rpx;
      color: #333;
    }

    .count {
      color: #ff4d4f;
    }
  }
}

// 视图模式切换按钮
.view-mode-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 16rpx 20rpx;
  background: #ffffff;
  border-radius: 10rpx;
  transition: all 0.3s ease;
  height: 72rpx;
  box-sizing: border-box;

  &:active {
    background: #f0f0f0;
  }

  text {
    font-size: 24rpx;
    color: #333;
  }
}

// 排序按钮
.sort-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  background: #ffffff;
  border-radius: 10rpx;
  transition: all 0.3s ease;
  height: 72rpx;
  box-sizing: border-box;
  color: #333;

  &:active {
    background: #f0f0f0;
  }

  &.active {
    color: #4285F4;
  }

  text {
    font-size: 24rpx;
    transition: color 0.3s ease;
  }

  .sort-icon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 4rpx;
  }
}

// 底部按钮
.footer {
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 试卷列表容器样式
.paper-list-container {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;

  // 简洁模式下的容器样式
  &.grid-mode {
    .paper-item:last-child {
      .paper-item {
        border-bottom: none; // 最后一项不需要分割线
      }
    }
  }
}

// 学生选择器相关样式
.student-selector-container {
  position: relative;
}

.student-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 400rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  margin-top: 8rpx;
  padding: 16rpx;
}

.search-input-box {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 8rpx;
  padding: 0 16rpx;
  margin-bottom: 16rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.student-list {
  max-height: 400rpx;
}

.student-item {
  padding: 16rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1px solid #F5F5F5;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #F8F9FA;
  }
}

.empty-result {
  color: #999;
  text-align: center;
}

.loading-students {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  color: #4285F4;
  font-size: 26rpx;

  .uni-icons {
    margin-right: 8rpx;
    animation: spin 1.2s linear infinite;
  }
}

// 联系客服按钮样式
.contact-service-btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFF7E6;
  padding: 16rpx 30rpx;
  border-radius: 10rpx;
  border: none;
  transition: all 0.3s ease;
  height: 72rpx;
  box-sizing: border-box;

  &::after {
    border: none;
  }

  &:active {
    background: #FFE7BA;
  }

  .service-info {
    display: flex;
    align-items: center;
    gap: 10rpx;
    white-space: nowrap;
    height: 100%;

    text {
      font-size: 24rpx;
      color: #FF6B35;
      font-weight: 500;
      line-height: 1;
    }
  }
}

.svg-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.svg-icon {
  min-width: 32rpx;
  min-height: 32rpx;
}

// 文件分享模态框样式
.share-file-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  .share-file-content {
    width: 80%;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    padding: 40rpx 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    .share-file-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 20rpx;
    }

    .share-file-message {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 40rpx;
      text-align: center;
    }

    .share-file-actions {
      display: flex;
      width: 100%;
      gap: 20rpx;

      .cancel-btn, .share-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 10rpx;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
      }

      .cancel-btn {
        background-color: #f5f5f5;
        color: #666;

        &:active {
          background-color: #e8e8e8;
        }
      }

      .share-btn {
        background-color: #07C160;
        color: #fff;

        &:active {
          background-color: #06ad56;
        }
      }
    }
  }
}
</style>
