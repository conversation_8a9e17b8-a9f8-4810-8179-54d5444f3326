<template>
  <view class="edit-modal" v-if="visible">
    <view class="modal-mask" @click="handleCancel"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">编辑作文内容</text>
        <view class="close-btn" @click="handleCancel">×</view>
      </view>

      <view class="modal-body">
        <!-- 作文标题 -->
        <view class="form-item">
          <text class="form-label">作文标题</text>
          <input
            class="form-input"
            v-model="formData.title"
            placeholder="请输入作文标题（可选）"
          />
        </view>

        <!-- 作文内容 -->
        <view class="form-item">
          <text class="form-label">作文内容</text>
          <textarea
            class="form-textarea"
            v-model="formData.content"
            placeholder="请输入作文内容"
            :maxlength="-1"
            auto-height
          ></textarea>
        </view>
        
        <!-- 字数统计 -->
        <view class="word-count">
          字数：{{ getWordCount(formData.content) }}
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" @click="handleCancel">取消</button>
        <button class="modal-btn confirm-btn" @click="handleConfirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  essayData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 表单数据
const formData = reactive({
  title: '',
  content: ''
})

// 计算字数
const getWordCount = (text) => {
  if (!text) return 0
  return text.replace(/\s/g, '').length
}

// 监听 essayData 变化，更新表单数据
watch(() => props.essayData, (newVal) => {
  if (newVal) {
    formData.title = newVal.title || ''
    formData.content = newVal.content || ''
  }
}, { immediate: true, deep: true })

// 取消编辑
function handleCancel() {
  emit('update:visible', false)
  emit('cancel')
}

// 确认编辑
function handleConfirm() {
  if (!formData.content.trim()) {
    uni.showToast({
      title: '请输入作文内容',
      icon: 'none'
    })
    return
  }

  // 创建更新后的数据对象
  const updatedData = {
    ...props.essayData,
    title: formData.title.trim(),
    content: formData.content.trim()
  }

  // 发送确认事件
  emit('confirm', updatedData)
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  background: white;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;

  .modal-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .close-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #999;
    border-radius: 50%;
    background: #f5f5f5;
  }
}

.modal-body {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;

  .form-label {
    display: block;
    font-size: 28rpx;
    color: #333;
    margin-bottom: 12rpx;
    font-weight: 500;
  }

  .form-input {
    width: 100%;
    padding: 20rpx;
    border: 2rpx solid #eee;
    border-radius: 8rpx;
    font-size: 28rpx;
    background: #fafafa;
    
    &:focus {
      border-color: #3c77ef;
      background: white;
    }
  }

  .form-textarea {
    width: 100%;
    min-height: 300rpx;
    padding: 20rpx;
    border: 2rpx solid #eee;
    border-radius: 8rpx;
    font-size: 28rpx;
    line-height: 1.6;
    background: #fafafa;
    
    &:focus {
      border-color: #3c77ef;
      background: white;
    }
  }
}

.word-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: -20rpx;
  margin-bottom: 20rpx;
}

.modal-footer {
  display: flex;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  gap: 20rpx;

  .modal-btn {
    flex: 1;
    padding: 24rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    border: none;

    &.cancel-btn {
      background: #f5f5f5;
      color: #666;
    }

    &.confirm-btn {
      background: #3c77ef;
      color: white;
    }
  }
}
</style>
