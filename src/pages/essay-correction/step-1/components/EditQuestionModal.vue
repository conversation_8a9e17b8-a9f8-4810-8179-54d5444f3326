<template>
  <view class="edit-modal" v-if="visible">
    <view class="modal-mask" @click="handleCancel"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">编辑题目</text>
        <view class="close-btn" @click="handleCancel">×</view>
      </view>

      <view class="modal-body">
        <!-- 题号显示 -->
        <view class="form-item">
          <text class="form-label">题号 {{ formData.no }}</text>
        </view>

        <!-- 题目输入框 - 仅在问答对照模式显示 -->
        <view class="form-item" v-if="showQuestionField">
          <text class="form-label">题目</text>
          <textarea
              class="form-textarea"
              type="textarea"
              :maxlength="-1"
              v-model="formData.question"
              placeholder="请输入题目内容"
          ></textarea>
        </view>

        <!-- 答案输入框 -->
        <view class="form-item">
          <text class="form-label">参考答案</text>
          <textarea
              class="form-textarea"
              type="textarea"
              :maxlength="-1"
              v-model="formData.answer"
              placeholder="请输入参考答案"
          ></textarea>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn cancel-btn" @click="handleCancel">取消</button>
        <button class="modal-btn confirm-btn" @click="handleConfirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  itemData: {
    type: Object,
    default: () => ({})
  },
  displayMode: {
    type: String,
    default: ''
  },

});

const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 表单数据
const formData = reactive({
  no: 0,
  question: '',
  answer: ''
});

// 是否显示问题字段
const showQuestionField = computed(() => {
  return props.displayMode === 'answer_with_questions';
});

// 监听 itemData 变化，更新表单数据
watch(() => props.itemData, (newVal) => {
  if (newVal) {
    formData.no = newVal.no || 0;
    // 清理可能的多余换行符
    formData.question = newVal.question ? newVal.question.replace(/\n{3,}/g, '\n\n') : '';
    formData.answer = newVal.answer ? newVal.answer.replace(/\n{3,}/g, '\n\n') : '';
  }
}, { immediate: true, deep: true });

// 取消编辑
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 确认编辑
function handleConfirm() {
  // 创建更新后的数据对象
  const updatedData = {
    ...props.itemData,
    question: showQuestionField.value ? formData.question : undefined,
    answer: formData.answer
  };

  // 发送确认事件
  emit('confirm', updatedData);
  emit('update:visible', false);
}
</script>

<style lang="scss" scoped>
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1001;
  }

  .modal-content {
    position: relative;
    width: 90%;
    max-width: 650rpx;
    background-color: #fff;
    border-radius: 16rpx;
    z-index: 1002;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      font-size: 40rpx;
      color: #999;
      line-height: 1;
      padding: 0 10rpx;
    }
  }

  .modal-body {
    padding: 30rpx;
    overflow-y: auto;

    .form-item {
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-label {
      display: block;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 12rpx;
      font-weight: 500;
    }

    .form-value {
      font-size: 28rpx;
      color: #666;
    }

    .form-textarea {
      width: 100%;
      padding: 16rpx;
      font-size: 28rpx;
      border: 1px solid #e0e0e0;
      border-radius: 8rpx;
      box-sizing: border-box;
      background-color: #f9f9f9;
      height: 160rpx;
      overflow: auto;
    }
  }

  .modal-footer {
    display: flex;
    justify-content: center;
    padding: 20rpx 30rpx;
    border-top: 1px solid #f0f0f0;

    .modal-btn {
      min-width: 180rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 30rpx;
      border-radius: 40rpx;
      margin: 0 15rpx;
      border: none;

      &::after {
        display: none;
      }
    }

    .cancel-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    .confirm-btn {
      background-color: #1890ff;
      color: #fff;
      font-weight: 500;
      box-shadow: 0 4rpx 8rpx rgba(24, 144, 255, 0.2);
    }
  }
}
</style>
