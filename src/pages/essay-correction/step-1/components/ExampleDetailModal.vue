<template>
  <view class="example-modal" v-if="visible">
    <view class="mask" @click="handleClose"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="title">{{ title }}</text>
        <text class="close-icon" @click="handleClose">×</text>
      </view>

      <scroll-view class="modal-body" scroll-y>
        <!-- 参考答案要求部分 -->
        <view class="requirement-section">
          <view class="section-title">【参考答案要求】</view>
          <view class="requirement-list">
            <view class="requirement-item" v-for="(item, index) in teacherRequirements" :key="index">
              <text class="item-number">{{ index + 1 }}.</text>
              <text class="item-text">{{ item }}</text>
            </view>
          </view>

          <!-- 参考答案示例图片 -->
          <view class="example-image-container">
            <image
              class="example-image"
              :src="teacherExampleImage"
              mode="widthFix"
              @click="previewImage(teacherExampleImage)"
            ></image>
          </view>
        </view>

        <template v-if="studentRequirements.length > 0">
          <!-- 学生答案要求部分 -->
          <view class="requirement-section">
            <view class="section-title">【学生答案要求】</view>
            <view class="requirement-list">
              <view class="requirement-item" v-for="(item, index) in studentRequirements" :key="index">
                <text class="item-number">{{ index + 1 }}.</text>
                <text class="item-text">{{ item }}</text>
              </view>
            </view>

            <!-- 学生答案示例图片 -->
            <view class="example-image-container">
              <image
                  class="example-image"
                  :src="studentExampleImage"
                  mode="widthFix"
                  @click="previewImage(studentExampleImage)"
              ></image>
            </view>
          </view>
        </template>
      </scroll-view>

      <view class="modal-footer">
        <button class="confirm-btn" @click="handleClose">我知道了</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['update:visible']);

// 根据类型获取标题
const title = ref('');
const teacherRequirements = ref([]);
const studentRequirements = ref([]);
const teacherExampleImage = ref('');
const studentExampleImage = ref('');

// 根据类型更新内容 - 将函数定义提前
const updateContentByType = (type) => {
  switch(type) {
    case 1: // 仅参考答案
      title.value = '参考答案格式说明';
      teacherRequirements.value = [
        '每个答案前需要有序号',
        '答案需要清晰可辨',
        '只需上传参考答案，不需要包含题目'
      ];
      studentRequirements.value = [
        '学生答案需按相同序号填写',
        '字迹需清晰便于系统识别',
        '不会写的也得写上序号',
        '姓名要笔记清晰，不要连笔'
      ];
      teacherExampleImage.value = 'https://quick-marker-oss.research.top/static/%E7%AD%94%E6%A1%88.png';
      studentExampleImage.value = 'https://quick-marker-oss.research.top/static/single-student%202.png';
      break;
    case 2: // 参考答案和题目
      title.value = '参考答案和题目格式说明';
      teacherRequirements.value = [
        '必须要有序号，序号可以不从 1 开始，但是最好保持连续',
        '题目与答案需配对展示，保持在一行',
        '排版整齐便于系统识别'
      ];
      studentRequirements.value = [
        '学生需按题号作答',
        '学生字迹需清晰可辨,不要连笔',
      ];
      teacherExampleImage.value = 'https://quick-marker-oss.research.top/static/question-answer.jpg';
      studentExampleImage.value = 'https://quick-marker-oss.research.top/static/question-answer-student.jpg';
      break;
  }
};

// 重置所有内容
const resetContent = () => {
  title.value = '';
  teacherRequirements.value = [];
  studentRequirements.value = [];
  teacherExampleImage.value = '';
  studentExampleImage.value = '';
};

// 监听类型变化，更新内容
watch(() => props.type, (newType) => {
  updateContentByType(newType);
}, { immediate: true });

// 监听可见性变化，当显示时更新内容
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    resetContent();
    updateContentByType(props.type);
  }
}, { immediate: true });

// 关闭模态框
const handleClose = () => {
  emit('update:visible', false);
};

// 预览图片
const previewImage = (url) => {
  uni.previewImage({
    urls: [url],
    current: 0
  });
};
</script>

<style lang="scss" scoped>
.example-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
  }

  .modal-content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 650rpx;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 20rpx;
    overflow: hidden;

    .modal-header {
      padding: 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1rpx solid #eee;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .close-icon {
        font-size: 40rpx;
        color: #999;
        width: 60rpx;
        height: 60rpx;
        line-height: 60rpx;
        text-align: center;
      }
    }

    .modal-body {
      box-sizing: border-box;
      padding: 30rpx;
      max-height: 60vh;

      .requirement-section {
        margin-bottom: 30rpx;

        .section-title {
          font-size: 30rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 20rpx;
        }

        .requirement-list {
          margin-bottom: 20rpx;

          .requirement-item {
            display: flex;
            margin-bottom: 10rpx;

            .item-number {
              margin-right: 10rpx;
              color: #333;
              font-size: 28rpx;
            }

            .item-text {
              font-size: 28rpx;
              color: #333;
              flex: 1;
            }
          }
        }

        .example-image-container {
          box-sizing: border-box;
          margin-top: 20rpx;
          border: 1rpx dashed #ddd;
          padding: 20rpx;
          border-radius: 12rpx;
          background-color: #f9f9f9;

          .example-image {
            width: 100%;
            border-radius: 8rpx;
          }
        }
      }
    }

    .modal-footer {
      padding: 20rpx 30rpx;
      border-top: 1rpx solid #eee;

      .confirm-btn {
        height: 80rpx;
        line-height: 80rpx;
        background-color: $uni-primary-color;
        color: #fff;
        font-size: 30rpx;
        border-radius: 40rpx;
        width: 100%;

        &::after {
          display: none;
        }
      }
    }
  }
}
</style>
