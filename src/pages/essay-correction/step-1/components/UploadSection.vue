<template>
  <view class="upload-section">
    <!-- 示例类型展示 -->
    <view class="example-types">
      <view class="example-type-title">上传参考答案，支持两种形式：</view>

      <view class="example-items">
        <!-- 类型1：仅参考答案 -->
        <view class="example-item">
          <view class="example-name">仅参考答案</view>
          <view class="example-desc">听写场景，学生只写答案，答案前面要有序号</view>
          <view class="view-detail-btn" @click="showExampleDetail(1)">查看示例</view>
          <view class="upload-btn" @click="handleUploadType(1)">
            <image class="upload-icon" src="/static/icons/paper-plane-white.svg" mode="aspectFit"></image>
            <text>上传</text>
          </view>
        </view>

        <!-- 类型2：参考答案和题目 -->
        <view class="example-item">
          <view class="example-name">题目答案对照</view>
          <view class="example-desc">打印的默写卡，题目答案左右对照</view>
          <view class="view-detail-btn" @click="showExampleDetail(2)">查看示例</view>
          <view class="upload-btn upload-btn-2" @click="handleUploadType(2)">
            <image class="upload-icon" src="/static/icons/paper-plane-white.svg" mode="aspectFit"></image>
            <text>上传</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义示例详情模态框 -->
    <ExampleDetailModal
      v-model:visible="showDetailModal"
      :type="currentExampleType"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import ExampleDetailModal from './ExampleDetailModal.vue';

const emit = defineEmits(['upload', 'viewExample']);

// 模态框控制
const showDetailModal = ref(false);
const currentExampleType = ref(1);

// 处理上传
const handleUploadType = (type) => {
  emit('upload', type);
};

// 显示示例详情
const showExampleDetail = (type) => {
  currentExampleType.value = type;
  showDetailModal.value = true;
};
</script>

<style lang="scss" scoped>
.upload-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .section-title {
      font-size: 30rpx;
      color: #333;
    }
  }

  // 示例类型展示样式
  .example-types {
    margin-bottom: 30rpx;

    .example-type-title {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .example-items {
      display: flex;
      justify-content: space-around;

      .example-item {
        width: 45%;
        border-radius: 12rpx;
        overflow: hidden;
        background-color: #f7f7f7;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        height: 300rpx; // 固定高度确保对齐

        .view-detail-btn {
          font-size: 22rpx;
          color: #666;
          text-align: center;
          padding: 4rpx 16rpx;
          border-radius: 20rpx;
          margin: 8rpx auto;
          border: 1px solid #ddd;
          width: fit-content;
          background-color: #f0f0f0;
        }

        .example-name {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
          padding: 20rpx 10rpx 6rpx;
          text-align: center;
        }

        .example-desc {
          font-size: 22rpx;
          color: #666;
          padding: 0 10rpx 12rpx;
          text-align: center;
          flex: 1; // 占据剩余空间，保证按钮对齐
        }

        .upload-btn {
          margin: 16rpx auto;
          margin-top: 28rpx;
          box-sizing: border-box;
          margin-bottom: 20rpx; // 底部固定间距
          padding: 12rpx 20rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: $uni-primary-color; // Default color
          color: #fff;
          font-size: 26rpx;
          font-weight: bold;
          border-radius: 40rpx;
          width: 85%;
          height: 70rpx; // 固定高度使按钮大小一致
          box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);

          .upload-icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }

          text {
            margin-left: 4rpx;
          }

          &:active {
            opacity: 0.8;
          }
        }

        // 为第二个上传按钮设置不同的颜色
        .upload-btn-2 {
              background-color: #4CAF50; // 绿色，与第一个按钮形成对比
            }
      }
    }
  }
}
</style>
