<template>
  <view class="extraction-result">
    <view class="section-header">
      <view class="title-container">
        <text class="section-title">解析结果</text>
        <view class="result-badge" v-if="result.length>0">{{ result.length }}题</view>
      </view>
      <view class="tip" v-if="result.length>0" :class="{ 'warning-tip': !isResultMatched }">
        <image class="tip-icon" :src="isResultMatched ? '/static/icons/info-circle.svg' : '/static/icons/warning-circle.svg'" mode="aspectFit"></image>
        <text v-if="isResultMatched">请核对解析结果，无误后进行下一步</text>
        <view v-else class="warning-text">
          <text>解析结果与所选形式不匹配，请重新上传</text>
          <text class="warning-detail">解析结果是{{ getTaskTypeName(detectedTaskType) }}，您选择的是{{ getTaskTypeName(selectedTaskType) }}</text>
        </view>
      </view>
    </view>

    <!-- 解析中状态 -->
    <view v-if="isLoading" class="loading-state">
      <view class="loading-icon"></view>
      <text class="loading-text" v-if="countdownSeconds > 0">参考答案解析中，请您耐心等待... {{ countdownSeconds }}s</text>
      <text class="loading-text" v-else>参考答案即将生成，请继续等待...</text>
    </view>

    <!-- 结果展示区域 -->
    <template v-else-if="displayMode && result.length">
      <!-- 表格头部 -->
      <view class="table-display" :class="{ 'single-column': displayMode === 'answer_only' }">
        <view class="table-header">
          <view class="table-cell seq-cell" style="white-space: nowrap;">序号</view>
          <view v-if="displayMode === 'answer_with_questions'" class="table-cell question-cell">题目</view>
          <view class="table-cell answer-cell">
            参考答案
          </view>
          <view class="table-cell action-cell">操作</view>
        </view>

        <!-- 表格内容 -->
        <view
          v-for="(item, index) in result"
          :key="index"
          class="table-row"
          :class="{ 'odd-row': index % 2 !== 0 }"
        >
          <view class="table-cell seq-cell">{{ item.no || (index + 1) }}</view>

          <!-- 问题单元格 -->
          <view v-if="displayMode === 'answer_with_questions'" class="table-cell question-cell">
            {{ item.question }}
          </view>

          <!-- 答案单元格 -->
          <view class="table-cell answer-cell">
            {{ item.answer }}
          </view>

          <!-- 操作按钮 -->
          <view class="table-cell action-cell">
            <view class="edit-actions">
              <button class="action-btn edit-btn" @click="openEditModal(index)">编辑</button>
            </view>
          </view>
        </view>
      </view>
    </template>

    <!-- 编辑弹出层 -->
    <EditQuestionModal
      v-model:visible="showEditModal"
      :item-data="currentEditItem"
      :display-mode="displayMode"
      @confirm="handleEditConfirm"
      @cancel="handleEditCancel"
    />
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, reactive } from 'vue';
import EditQuestionModal from './EditQuestionModal.vue';

const props = defineProps({
  // 现有属性
  displayMode: {
    type: String,
    default: ''
  },
  result: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  isResultMatched: {
    type: Boolean,
    default: true
  },
  detectedTaskType: {
    type: Number,
    default: 0
  },
  selectedTaskType: {
    type: Number,
    default: 0
  }
});

// 定义事件
const emit = defineEmits(['update:result']);


// 将 displayMode 转换为响应式对象，以便在函数中使用
const displayMode = ref(props.displayMode);

// 倒计时相关状态
const countdownSeconds = ref(30);
let countdownTimer = null;

// 开始倒计时
function startCountdown() {
  countdownSeconds.value = 30;
  clearCountdown();

  countdownTimer = setInterval(() => {
    if (countdownSeconds.value > 0) {
      countdownSeconds.value--;
    } else {
      clearCountdown();
    }
  }, 1000);
}

// 清除倒计时
function clearCountdown() {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
}

// 监听 props 变化

// 监听 loading 状态变化
watch(() => props.isLoading, (newValue) => {
  if (newValue) {
    startCountdown();
  } else {
    clearCountdown();
  }
}, { immediate: true });

// 监听 displayMode 变化
watch(() => props.displayMode, (newValue) => {
  displayMode.value = newValue;
}, { immediate: true });

// 组件挂载后检查是否需要开始倒计时
onMounted(() => {
  if (props.isLoading) {
    startCountdown();
  }
});

// 组件卸载时清除定时器
onUnmounted(() => {
  clearCountdown();
});

// 编辑弹出层相关状态
const showEditModal = ref(false);
const currentEditItem = ref({});
const currentEditIndex = ref(-1);

// 打开编辑弹出层
function openEditModal(index) {
  // 设置当前编辑项的数据
  const item = props.result[index];
  currentEditItem.value = { ...item };
  currentEditIndex.value = index;

  console.log(item, 'item')

  // 显示弹出层
  showEditModal.value = true;
}

// 处理编辑确认
function handleEditConfirm(updatedItem) {
  // 创建结果数组的副本
  const updatedResult = [...props.result];

  // 更新现有项
  updatedResult[currentEditIndex.value] = updatedItem;

  // 通知父组件更新结果
  emit('update:result', updatedResult);
}

// 处理编辑取消
function handleEditCancel() {
  // 关闭弹出层即可，无需其他操作
}

// 获取题型名称
function getTaskTypeName(type) {
  switch (type) {
    case 1:
      return '仅答案模式';
    case 2:
      return '问题答案对照模式';
    default:
      return '未知类型';
  }
}
</script>

<style lang="scss" scoped>
.extraction-result {
  margin-top: 20rpx;
  margin-bottom: 0;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .section-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 24rpx;


    .title-container {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
    }

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .result-badge {
      margin-left: 16rpx;
      font-size: 22rpx;
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
      background-color: #e24e4e;
      color: #fff;
      font-weight: 500;
    }

    .tip {
      display: flex;
      align-items: center;
      font-size: 24rpx;
      color: #666;
      background-color: #f8f8f8;
      padding: 10rpx 16rpx;
      border-radius: 8rpx;
      border-left: 4rpx solid #e24e4e;

      &.warning-tip {
        background-color: #fff8e6;
        border-left: 4rpx solid #f5a623;
        color: #d4880b;

        .warning-text {
          display: flex;
          flex-direction: column;
        }

        .warning-detail {
          font-size: 22rpx;
          margin-top: 6rpx;
          opacity: 0.9;
        }
      }
    }

    .tip-icon {
      width: 28rpx;
      height: 28rpx;
      margin-right: 8rpx;
    }

    .mode-tag {
      font-size: 24rpx;
      padding: 6rpx 16rpx;
      border-radius: 20rpx;
      color: #fff;

      &.mode-answer_only {
        background-color: #4080ff;
      }

      &.mode-answer_with_questions {
        background-color: #ff7f50;
      }
    }
  }

  .table-display {
    width: 100%;
    border: 1px solid #eaeaea;
    border-radius: 8rpx;
    overflow: hidden;

    .table-header {
      display: flex;
      background-color: #f7f7f7;
      font-weight: bold;

      .table-cell {
        padding: 20rpx 16rpx;
        font-size: 28rpx;
        color: #333;
        border-bottom: 1px solid #eaeaea;
      }
    }

    .table-row {
      display: flex;

      &.odd-row {
        background-color: #fafafa;
      }
    }

    .table-cell {
      padding: 20rpx 16rpx;
      font-size: 28rpx;
      color: #333;
      border-bottom: 1px solid #eaeaea;
      word-break: break-all;
    }

    .seq-cell {
      flex: 0.3;
      text-align: center;
      border-right: 1px solid #eaeaea;
    }

    .question-cell {
      flex: 1;
      border-right: 1px solid #eaeaea;
    }

    .answer-cell {
      flex: 1.5;
      border-right: 1px solid #eaeaea;
    }

    .action-cell {
      flex: 0.5;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    // 不需要分数列

    .full-width {
      flex: 1;
    }

    &.single-column {
      .table-cell {
        flex: 1;
      }
    }

    // 最后一行不需要底部边框
    .table-row:last-child {
      .table-cell {
        border-bottom: none;
      }
    }
  }
}

// 加载状态样式
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;

  .loading-icon {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #4080ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}



.edit-actions {
  display: flex;
  justify-content: center;

  .action-btn {
    font-size: 24rpx;
    padding: 8rpx 20rpx;
    border-radius: 6rpx;
    line-height: 1.5;
    border: none;
    white-space: nowrap;
    min-width: 80rpx;

    &::after {
      display: none;
    }

    &.edit-btn {
      background-color: #e6f7ff;
      color: #1890ff;
    }
  }
}
</style>
