export function checkResult(result) {
    // 检查结果是否存在
    if (!result) {
        console.error('Result is null or undefined')
        uni.showToast({
            title: '提取失败，结果为空',
            icon: 'none'
        })
        return
    }

    // 检查结果是否为数组（基于实现，它不应该是数组）
    if (Array.isArray(result)) {
        console.log('Result is an array with length:', result.length)
        // 检查数组是否为空
        if (result.length === 0) {
            console.error('Result array is empty')
            uni.showToast({
                title: '提取失败，结果数组为空',
                icon: 'none'
            })
            return
        }
        // 检查每个项目是否有必要的字段
        for (const item of result) {
            if (!('question' in item) || !('answer' in item)) {
                console.error('Item missing required fields:', item)
                uni.showToast({
                    title: '结果缺少必要字段',
                    icon: 'none'
                })
                return
            }
        }
    } else {
        console.error('Result is not array')
        uni.showToast({
            title: '提取失败，结果数组为空',
            icon: 'none'
        })
        return
    }

    // 如果验证通过，继续处理结果
    console.log('Validation passed:', result)
    return result
}
