<template>
  <view class="invitation-container">
    <!-- 顶部卡片 -->
    <view class="invitation-card">
      <view class="card-header">
        <view class="header-left">
          <image class="header-icon" :src="inviteIcon" mode="aspectFit"></image>
          <text class="header-title">邀请老师赠送100次批改点数</text>
        </view>
        <view class="header-right" @click="goToInviteRecord">
          <text class="record-text">邀请记录</text>
          <image class="arrow-icon" src="/static/icons/arrow-right.svg" mode="aspectFit"></image>
        </view>
      </view>

      <view class="card-content">
        <view class="poster-section">
          <view class="poster-wrapper">
            <!-- 生成的海报图片 -->
            <image
              v-if="posterImageUrl"
              class="poster-image"
              :src="posterImageUrl"
              mode="widthFix"
              show-menu-by-longpress
            ></image>
            <!-- 加载状态 -->
            <view v-else class="poster-loading">
              <view class="loading-spinner"></view>
              <text class="loading-text">海报生成中...</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮区 -->
        <view class="action-buttons">
          <view class="action-btn" @click="saveToAlbum">
            <view class="icon-wrapper yellow">
              <image class="action-icon" src="/static/icons/save-album.svg" mode="aspectFit"></image>
            </view>
            <text class="btn-text">存入相册</text>
          </view>
          <button class="action-btn share-btn" open-type="share">
            <view class="icon-wrapper green">
              <image class="action-icon" src="/static/icons/wechat.svg" mode="aspectFit"></image>
            </view>
            <text class="btn-text">微信好友</text>
          </button>
          <view class="action-btn" @click="shareToTimeline">
            <view class="icon-wrapper gray">
              <image class="action-icon" src="/static/icons/moments.svg" mode="aspectFit"></image>
            </view>
            <text class="btn-text">朋友圈</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 邀请说明 -->
    <view class="invitation-instructions">
      <view class="instruction-title">邀请说明</view>
      <view class="instruction-item">
        <text class="item-dot">•</text>
        <text class="item-text">邀请其他老师通过您的专属二维码进如小程序</text>
      </view>
      <view class="instruction-item">
        <text class="item-dot">•</text>
        <text class="item-text">被邀请的老师完成第一次批改后，双方均可获得额外批改次数</text>
      </view>
      <view class="instruction-item">
        <text class="item-dot">•</text>
        <text class="item-text">邀请奖励将在被邀请老师完成首次批改后立即到账</text>
      </view>
      <view class="instruction-item">
        <text class="item-dot">•</text>
        <text class="item-text">如遇问题，请联系客服</text>
      </view>
    </view>

    <!-- 二维码弹窗 -->
    <view class="qrcode-popup" v-if="showQrCodePopup">
      <view class="popup-mask" @click="closeQrCodePopup"></view>
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">邀请二维码</text>
          <view class="close-btn" @click="closeQrCodePopup">
            <image class="close-icon" src="/static/icons/close.svg" mode="aspectFit"></image>
          </view>
        </view>
        <view class="popup-body">
          <image class="large-qrcode" :src="qrCodeUrl" mode="aspectFit"></image>
          <text class="popup-tip">扫描二维码或长按保存</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import {onLoad, onShareAppMessage, onShareTimeline} from "@dcloudio/uni-app";
import { getInviteCode } from '@/api/invitation';
import { useUserStore } from '@/store/user';
const userStore = useUserStore();

// 用户信息
const userInfo = ref(userStore.userInfo);

// 邀请数据
const inviteCode = ref('');
const qrCodeUrl = ref('');
const posterImageUrl = ref('');

const qrLink = ref('')

// 默认头像
const defaultAvatar = 'https://quick-marker-oss.research.top/static/default-avatar.png';

// 使用本地SVG图标替代网络图片
const inviteIcon = ref('/static/icons/gift-100.svg');

// 二维码弹窗控制
const showQrCodePopup = ref(false);

// 获取邀请码
const fetchInviteCode = async () => {
  try {
    const res = await getInviteCode();
    inviteCode.value = res.data;
    qrLink.value = `https://quick-marker-oss.research.top/mini-qr?code=${inviteCode.value}`
  } catch (error) {
    console.error('获取邀请码失败:', error);
    uni.showToast({
      title: '获取邀请码失败',
      icon: 'none'
    });
  }
};

// 关闭二维码弹窗
const closeQrCodePopup = () => {
  showQrCodePopup.value = false;
};

// 分享小程序
onShareAppMessage(() => {
  return {
    title: '邀请您使用【英语批改助手】，老师专享批改工具',
    path: `/pages/index/index?code=${inviteCode.value}`,
  };
});

onShareTimeline(() => {
  return {
    title: '邀请您使用【英语批改助手】，老师专享批改工具',
    path: `/pages/index/index?code=${inviteCode.value}`,
    imageUrl: 'https://quick-marker-mini.oss-cn-beijing.aliyuncs.com/static/banner1.webp'
  };
})

// 保存海报到相册
const saveToAlbum = async () => {
  try {
    if (!posterImageUrl.value) {
      uni.showToast({
        title: '海报正在生成中',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    uni.showLoading({
      title: '保存中...'
    });

    // 处理Base64图片数据
    if (posterImageUrl.value.startsWith('data:image')) {
      // 从data URL中提取base64部分
      const base64Data = posterImageUrl.value.split(',')[1];
      const filePath = `${uni.env.USER_DATA_PATH}/poster_${Date.now()}.png`;

      // 将base64转为临时文件
      try {
        await new Promise((resolve, reject) => {
          uni.getFileSystemManager().writeFile({
            filePath: filePath,
            data: uni.base64ToArrayBuffer(base64Data),
            encoding: 'binary',
            success: resolve,
            fail: reject
          });
        });

        // 保存到相册
        await new Promise((resolve, reject) => {
          uni.saveImageToPhotosAlbum({
            filePath: filePath,
            success: resolve,
            fail: reject
          });
        });

        // 删除临时文件
        uni.getFileSystemManager().unlink({
          filePath: filePath,
          fail: (err) => console.log('删除临时文件失败', err)
        });
      } catch (err) {
        throw err;
      }
    } else {
      // 处理网络图片或本地图片路径
      const imageInfo = await new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: posterImageUrl.value,
          success: resolve,
          fail: reject
        });
      });

      // 保存到相册
      await new Promise((resolve, reject) => {
        uni.saveImageToPhotosAlbum({
          filePath: imageInfo.path,
          success: resolve,
          fail: reject
        });
      });
    }

    uni.hideLoading();
    uni.showToast({
      title: '已保存到相册',
      icon: 'success'
    });
  } catch (error) {
    console.error('保存到相册失败:', error);
    uni.hideLoading();
    uni.showToast({
      title: '保存到相册失败',
      icon: 'none'
    });
  }
};

// 分享到朋友圈（实际上是保存图片，让用户手动分享）
const shareToTimeline = () => {
  uni.showToast({
    title: '请点击右上角三个点，选择“分享到朋友圈”',
    icon: 'none',
    duration: 2000
  })
};



// 获取海报图片
const fetchPosterImage = async () => {
  try {

    // 准备请求参数
    const params = {
      userInfo: {
        name: userInfo.value.name || '未设置昵称',
        avatar: userInfo.value.avatar || defaultAvatar
      },
      qrContent: qrLink.value // 使用现有的qrLink作为二维码内容
    };

    // 调用海报生成API获取图片
    const response = await uni.request({
      url: 'https://quick-marker-fc.research.top/generate-poster',
      method: 'POST',
      data: params,
      responseType: 'arraybuffer'
    });

    if (response.statusCode === 200) {
      // 将图片二进制数据转换为base64
      const base64 = uni.arrayBufferToBase64(response.data);
      posterImageUrl.value = `data:image/png;base64,${base64}`;
    } else {
      throw new Error(`Failed to fetch poster image: ${response.statusCode}`);
    }
  } catch (error) {
    console.error('获取海报图片失败:', error);
    uni.showToast({
      title: '获取海报图片失败',
      icon: 'none'
    });
  }
};

// 页面加载
onLoad(async () => {
  await fetchInviteCode()
  fetchPosterImage();
});

// 在 script setup 部分添加跳转方法
const goToInviteRecord = () => {
  uni.navigateTo({
    url: '/pages/invitation/records'
  });
};
</script>

<style lang="scss">
.invitation-container {
  min-height: 100vh;
  background-color: #F5F7FF;
  padding: 24rpx;

  // 邀请卡片
  .invitation-card {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;

      .header-left {
        display: flex;
        align-items: center;
      }

      .header-right {
        display: flex;
        align-items: center;
        padding: 8rpx 16rpx;
        background-color: #F5F7FF;
        border-radius: 24rpx;

        .record-text {
          font-size: 26rpx;
          color: #3c77ef;
          margin-right: 8rpx;
        }

        .arrow-icon {
          width: 24rpx;
          height: 24rpx;
        }
      }

      .header-icon {
        width: 44rpx;
        height: 44rpx;
        margin-right: 12rpx;
      }

      .header-title {
        font-size: 30rpx;
        font-weight: 500;
        color: #333333;
      }
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 32rpx;

      .poster-section {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 24rpx;

        .poster-wrapper {
          width: 600rpx;
          background: #ffffff;
          border-radius: 24rpx;
          overflow: hidden;
          margin-bottom: 32rpx;
          box-shadow: 0 8rpx 32rpx rgba(60, 119, 239, 0.1);
          position: relative;

          .poster-image {
            width: 100%;
            border-radius: 12rpx;
          }

          .poster-loading {
            width: 100%;
            height: 600rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #F5F7FF;
            border-radius: 12rpx;

            .loading-spinner {
              width: 64rpx;
              height: 64rpx;
              border: 6rpx solid #f3f3f3;
              border-top: 6rpx solid #3c77ef;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-bottom: 24rpx;
            }

            .loading-text {
              font-size: 28rpx;
              color: #666666;
            }

            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 500rpx;
        padding: 0 60rpx;
        margin-top: 32rpx;

        .action-btn {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: none;
          padding: 0;
          line-height: 1;
          border: none;
          width: auto;
          height: auto;
          box-shadow: none;

          &::after {
            display: none;
          }

          .icon-wrapper {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12rpx;

            &.yellow {
              background-color: #4A90E2;
            }

            &.green {
              background-color: #07C160;
            }

            &.gray {
              background-color: #8E44AD;
            }

            .action-icon {
              width: 48rpx;
              height: 48rpx;
            }
          }

          .btn-text {
            font-size: 26rpx;
            color: #333333;
          }
        }

        .share-btn {
          background: none;
          color: #333333;
          box-shadow: none;
        }
      }
    }
  }

  // 邀请说明
  .invitation-instructions {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

    .instruction-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 24rpx;
    }

    .instruction-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .item-dot {
        color: #3c77ef;
        margin-right: 12rpx;
      }

      .item-text {
        font-size: 26rpx;
        color: #666666;
        line-height: 1.5;
      }
    }
  }

  // 二维码弹窗
  .qrcode-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;

    .popup-mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .popup-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 600rpx;
      background-color: #FFFFFF;
      border-radius: 24rpx;
      overflow: hidden;

      .popup-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #F0F0F0;

        .popup-title {
          font-size: 32rpx;
          font-weight: 600;
          color: #333333;
        }

        .close-btn {
          padding: 8rpx;

          .close-icon {
            width: 32rpx;
            height: 32rpx;
          }
        }
      }

      .popup-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40rpx;

        .large-qrcode {
          width: 400rpx;
          height: 400rpx;
          margin-bottom: 24rpx;
        }

        .popup-tip {
          font-size: 26rpx;
          color: #999999;
        }
      }
    }
  }
}
</style>
