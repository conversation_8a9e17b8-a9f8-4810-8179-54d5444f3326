<template>
  <view class="records-container">
    <!-- 统计信息卡片 -->
    <view class="stats-wrapper">
      <view class="stats-card">
        <view v-if="statsLoading" class="stats-loading">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>
        <template v-else>
          <view class="stats-item">
            <text class="stats-value">{{ totalInviteCount }}</text>
            <text class="stats-label">邀请人数</text>
          </view>
          <view class="stats-divider"></view>
          <view class="stats-item">
            <text class="stats-value">{{ usedInviteCount }}</text>
            <text class="stats-label">领取人数</text>
          </view>
          <view class="stats-divider"></view>
          <view class="stats-item">
            <text class="stats-value">{{ totalPointsAwarded }}</text>
            <text class="stats-label">累计批改次数</text>
          </view>
        </template>
      </view>
      
      <!-- 数据说明 -->
      <view class="data-explanation">
        <view class="instruction-title">数据说明</view>
        <view class="instruction-item">
          <text class="item-dot">•</text>
          <text class="item-text">邀请人数：您已成功邀请的老师总数</text>
        </view>
        <view class="instruction-item">
          <text class="item-dot">•</text>
          <text class="item-text">领取人数：完成首次批改并获得奖励的老师数量</text>
        </view>
        <view class="instruction-item">
          <text class="item-dot">•</text>
          <text class="item-text">累计批改次数：通过邀请活动获得的总批改次数</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getInviteStatistics } from '@/api/invitation';

// 统计数据
const statsLoading = ref(true);
const totalInviteCount = ref(0); // 邀请人数
const usedInviteCount = ref(0); // 领取人数
const totalPointsAwarded = ref(0); // 批改次数

// 获取邀请统计信息
const fetchInviteStatistics = async () => {
  statsLoading.value = true;
  try {
    const res = await getInviteStatistics();

    if (res.code === 0 && res.data) {
      totalInviteCount.value = res.data.totalInviteCount || 0;
      usedInviteCount.value = res.data.usedInviteCount || 0;
      totalPointsAwarded.value = res.data.totalPointsAwarded || 0;
    }
  } catch (error) {
    console.error('获取邀请统计信息失败:', error);
    uni.showToast({
      title: '获取统计信息失败',
      icon: 'none'
    });
  } finally {
    statsLoading.value = false;
  }
};

// 页面加载
onMounted(() => {
  fetchInviteStatistics();
});
</script>

<style lang="scss">
.records-container {
  min-height: 100vh;
  background-color: #F5F7FF;
  display: flex;
  flex-direction: column;



  // 统计信息和邀请说明
  .stats-wrapper {
    flex: 1;
    padding: 24rpx;

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }
  
  // 数据说明样式
  .data-explanation {
    background-color: #FFFFFF;
    border-radius: 24rpx;
    padding: 32rpx;
    margin: 24rpx 0;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
    
    .instruction-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      margin-bottom: 24rpx;
    }
    
    .instruction-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .item-dot {
        color: #3c77ef;
        font-size: 32rpx;
        margin-right: 16rpx;
        line-height: 1.2;
      }
      
      .item-text {
        font-size: 28rpx;
        color: #666666;
        line-height: 1.5;
        flex: 1;
      }
    }
  }

  // 统计信息卡片
  .stats-card {
    display: flex;
    justify-content: space-around;
    align-items: center;
    background-color: #FFFFFF;
    min-height: 180rpx;
    border-radius: 24rpx;
    padding: 32rpx;
    margin: 0 0 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

    .stats-loading {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 30rpx 0;

      .loading-spinner {
        width: 50rpx;
        height: 50rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #4080FF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .loading-text {
        margin-top: 10rpx;
        font-size: 28rpx;
        color: #999999;
      }
    }

    .stats-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;

      .stats-value {
        font-size: 36rpx;
        font-weight: 600;
        color: #3c77ef;
        margin-bottom: 8rpx;
      }

      .stats-label {
        font-size: 24rpx;
        color: #666666;
      }
    }

    .stats-divider {
      width: 1rpx;
      height: 60rpx;
      background-color: #F0F0F0;
      flex-shrink: 0;
    }
  }


}
</style>
