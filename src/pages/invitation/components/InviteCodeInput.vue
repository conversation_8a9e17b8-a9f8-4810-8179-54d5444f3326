<template>
  <view class="invite-code-input">
    <view class="input-title">
      <text>邀请码</text>
      <text class="optional-tag">(选填)</text>
    </view>
    <view class="input-container">
      <input 
        class="code-input" 
        type="text" 
        v-model="inviteCode" 
        placeholder="请输入邀请码" 
        maxlength="8"
        @input="handleInput"
      />
      <view class="clear-btn" v-if="inviteCode" @click="clearInput">
        <image class="clear-icon" src="/static/icons/clear.svg" mode="aspectFit"></image>
      </view>
    </view>
    <view class="input-tip">填写邀请码，完成首次批改后双方均可获得额外批改次数</view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';

// 定义属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'input']);

// 邀请码
const inviteCode = ref(props.modelValue);

// 监听属性变化
watch(() => props.modelValue, (newVal) => {
  inviteCode.value = newVal;
});

// 监听输入变化
watch(inviteCode, (newVal) => {
  emit('update:modelValue', newVal);
});

// 处理输入
const handleInput = (e) => {
  const value = e.detail.value.trim().toUpperCase();
  inviteCode.value = value;
  emit('input', value);
};

// 清除输入
const clearInput = () => {
  inviteCode.value = '';
  emit('update:modelValue', '');
  emit('input', '');
};
</script>

<style lang="scss" scoped>
.invite-code-input {
  margin-bottom: 24rpx;
  
  .input-title {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
    
    text {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }
    
    .optional-tag {
      font-size: 24rpx;
      color: #999999;
      font-weight: normal;
      margin-left: 8rpx;
    }
  }
  
  .input-container {
    position: relative;
    display: flex;
    align-items: center;
    background-color: #F5F7FF;
    border-radius: 16rpx;
    padding: 0 24rpx;
    height: 88rpx;
    margin-bottom: 8rpx;
    
    .code-input {
      flex: 1;
      height: 100%;
      font-size: 28rpx;
      color: #333333;
    }
    
    .clear-btn {
      padding: 8rpx;
      
      .clear-icon {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
  
  .input-tip {
    font-size: 24rpx;
    color: #3c77ef;
  }
}
</style>
