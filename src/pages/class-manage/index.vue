<template>
  <view class="class-manage">
    <!-- 空状态 -->
    <Empty v-if="!loading && classList.length === 0" text="暂无班级数据" />
    <block v-else>
      <view class="class-list">
        <class-item
            v-for="item in classList"
            :key="item.id"
            :class-info="item"
            @click="handleClassClick"
            @edit="handleEdit"
            @delete="handleDelete"
        />
      </view>
      <!-- 加载更多 -->
      <uni-load-more :status="loadMoreStatus" />
    </block>

    <!-- 添加班级按钮 -->
    <add-class-button @click="handleAddClass" />

    <!-- 班级编辑模态框 -->
    <class-edit-modal
      v-model:show="showEditModal"
      :class-info="currentClass"
      @save="handleSaveClass"
      @cancel="handleCancelEdit"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';

import { getClassList, deleteClass, createClass, updateClass } from '@/api/class';
import ClassItem from './components/ClassItem.vue';
import AddClassButton from './components/AddClassButton.vue';
import ClassEditModal from './components/ClassEditModal.vue';
import { onPullDownRefresh , onReachBottom, onShow } from "@dcloudio/uni-app"
import Empty from "@/components/Empty.vue";


// 班级列表数据
const classList = ref([]);
const loading = ref(false);
const loadMoreStatus = ref('more');
const page = ref(1);
const pageSize = ref(10);
const hasMore = ref(true);

// 模态框相关状态
const showEditModal = ref(false);
const currentClass = ref(null);

// 获取班级列表
const fetchClassList = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1;
    classList.value = [];
    hasMore.value = true;
  }

  if (!hasMore.value) return;

  loading.value = true;
  loadMoreStatus.value = 'loading';

  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value
    };
    const res = await getClassList(params);

    if (res.code === 0) {
      const { list, total } = res.data;
      classList.value = isRefresh ? list : [...classList.value, ...list];
      hasMore.value = classList.value.length < total;
      loadMoreStatus.value = hasMore.value ? 'more' : 'noMore';
      page.value++;
    } else {
      loadMoreStatus.value = 'more';
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    uni.showToast({
      title: '获取班级列表失败',
      icon: 'none'
    });
    loadMoreStatus.value = 'more';
  } finally {
    loading.value = false;
  }
};

// 处理班级点击事件
const handleClassClick = (classInfo) => {
  uni.navigateTo({
    url: `/pages/student-list/index?name=${classInfo.className}&id=${classInfo.id}`
  });
};

// 处理添加班级点击事件
const handleAddClass = () => {
  currentClass.value = null;
  showEditModal.value = true;
};

// 处理编辑班级事件
const handleEdit = (classInfo) => {
  currentClass.value = classInfo;
  showEditModal.value = true;
};

// 处理保存班级事件
const handleSaveClass = async (formData) => {
  try {
    uni.showLoading({
      title: currentClass.value ? '更新中...' : '添加中...'
    });

    const api = currentClass.value ? updateClass : createClass;
    const params = currentClass.value
      ? { id: currentClass.value.id, ...formData }
      : formData;

    await api(params);

    uni.showToast({
      title: currentClass.value ? '更新成功' : '添加成功',
      icon: 'success'
    });
    // 刷新列表
    fetchClassList(true);
  } catch (error) {
    console.error(currentClass.value ? '更新班级失败:' : '添加班级失败:', error);
    uni.showToast({
      title: currentClass.value ? '更新失败' : '添加失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 处理取消编辑事件
const handleCancelEdit = () => {
  currentClass.value = null;
};

// 处理删除班级事件
const handleDelete = async (classInfo) => {
  try {
    uni.showLoading({
      title: '删除中...'
    });

    const res = await deleteClass(classInfo.id);

    if (res.code === 0) {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      });
      // 刷新列表
      fetchClassList(true);
    } else {
      uni.showToast({
        title: res.message || '删除失败',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('删除班级失败:', error);
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 下拉刷新
onPullDownRefresh(async () => {
  await fetchClassList(true);
  uni.stopPullDownRefresh();
})

// 触底加载更多
onReachBottom(() => {
  console.log('触底加载更多');
  if (!loading.value && hasMore.value) {
    fetchClassList();
  }
})


// 页面显示时刷新数据
onShow(() => {
  fetchClassList(true);
});
</script>

<style lang="scss">
page {
  background-color: #f8f9fa;
}
</style>

<style lang="scss" scoped>
.class-manage {

  padding-top: 15rpx;
  padding-bottom: 150rpx;

  .class-list {
    padding: 15rpx 30rpx;
  }
}
</style>
