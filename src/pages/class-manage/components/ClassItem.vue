<template>
  <view class="class-item">
    <view class="class-info" @tap="handleClick">
      <view class="class-name">{{ classInfo.className }}</view>
      <view class="info-row students-info">
        <view class="student-count">学生人数: {{ classInfo.studentCount }}人</view>
      </view>
      <view class="info-row">
        <view class="time">{{ formatDate(classInfo.createTime) }}</view>
      </view>
    </view>
    <view class="actions">
      <view class="edit-btn" @tap.stop="handleEdit">
        <uni-icons type="compose" size="18" color="#4e80f0"></uni-icons>
      </view>
      <view class="delete-btn" @tap.stop="handleDelete">
        <uni-icons type="trash" size="18" color="#ff4d4f"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script setup>
import { formatDate } from '@/utils/tools';

const props = defineProps({
  classInfo: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      className: '',
      teacherId: '',
      createTime: 0
    })
  }
});

const emit = defineEmits(['click', 'edit', 'delete']);

const handleClick = () => {
  emit('click', props.classInfo);
};

const handleEdit = () => {
  emit('edit', props.classInfo);
};

const handleDelete = () => {
  uni.showModal({
    title: '提示',
    content: `确定要删除班级"${props.classInfo.className}"吗？`,
    success: (res) => {
      if (res.confirm) {
        emit('delete', props.classInfo);
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.class-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin: 15rpx 0;
  border-radius: 8rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

  .class-info {
    flex: 1;
    margin-right: 20rpx;

    .class-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 16rpx;
    }

    .info-row {
      margin-bottom: 8rpx;

      &.students-info {
        display: flex;
        gap: 24rpx;
      }
    }

    .student-count {
      font-size: 26rpx;
      color: #666;
    }

    .time {
      font-size: 24rpx;
      color: #a5a5a5;
    }
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 30rpx;

    .edit-btn, .delete-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }

    .edit-btn {
      background-color: rgba(78, 128, 240, 0.1);
    }

    .delete-btn {
      background-color: rgba(255, 77, 79, 0.1);
    }
  }
}
</style>
