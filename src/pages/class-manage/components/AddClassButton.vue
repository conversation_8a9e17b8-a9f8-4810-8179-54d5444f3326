<template>
  <view class="add-button" @tap="handleClick">
    <text class="add-icon">+</text>
    <text>添加班级</text>
  </view>
</template>

<script setup>
const emit = defineEmits(['click']);

const handleClick = () => {
  emit('click');
};
</script>

<style lang="scss" scoped>
.add-button {
  position: fixed;
  left: 30rpx;
  right: 30rpx;
  bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
  background-color: #007AFF;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx;
  z-index: 10;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }

  .add-icon {
    margin-right: 12rpx;
    font-size: 40rpx;
    font-weight: 300;
    line-height: 1;
  }
}
</style>
