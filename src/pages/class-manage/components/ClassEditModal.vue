<template>
  <view class="edit-modal" v-if="show">
    <view class="modal-content">
      <view class="modal-title">{{ isEdit ? '编辑班级' : '添加班级' }}</view>

      <view class="form-item">
        <view class="label">班级名称</view>
        <view class="input-wrap">
          <input
            type="text"
            v-model="formData.className"
            placeholder="请输入班级名称，最多12个字"
            maxlength="12"
            @input="handleInput"
          />
          <view class="char-count">{{ formData.className.length }}/12</view>
        </view>
      </view>

      <view class="button-group">
        <button class="cancel" @click="handleCancel">取消</button>
        <button class="confirm" @click="handleConfirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  classInfo: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:show', 'save', 'cancel']);

// 是否是编辑模式
const isEdit = computed(() => props.classInfo !== null);

// 表单数据
const formData = ref({
  className: ''
});

// 当班级数据变化时，更新表单数据
watch(() => props.classInfo, (newVal) => {
  if (newVal) {
    formData.value = {
      className: newVal.className || ''
    };
  }
}, { immediate: true });

// 监听模态框显示状态
watch(() => props.show, (newVal) => {
  if (newVal && !isEdit.value) {
    // 如果是打开模态框且是添加操作，清空表单
    formData.value = {
      className: ''
    };
  }
});

// 输入处理
const handleInput = () => {
  // 移除首尾空格
  formData.value.className = formData.value.className.trim();
};

// 确认按钮
const handleConfirm = () => {
  // 表单验证
  if (!formData.value.className.trim()) {
    uni.showToast({
      title: '请输入班级名称',
      icon: 'none'
    });
    return;
  }

  // 添加教师ID
  const data = {
    ...formData.value,
    teacherId: uni.getStorageSync('userInfo').id
  };

  // 提交表单
  emit('save', data);

  // 关闭弹窗
  emit('update:show', false);
};

// 取消按钮
const handleCancel = () => {
  emit('update:show', false);
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;

  .modal-content {
    width: 80%;
    background-color: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    box-sizing: border-box;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 30rpx;
      text-align: center;
    }

    .form-item {
      margin-bottom: 20rpx;

      .label {
        font-size: 28rpx;
        margin-bottom: 10rpx;
      }

      .input-wrap {
        position: relative;

        input {
          width: 100%;
          height: 80rpx;
          border: 1px solid #eee;
          border-radius: 8rpx;
          padding: 0 30rpx;
          box-sizing: border-box;
          font-size: 28rpx;
        }

        .char-count {
          position: absolute;
          right: 30rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .button-group {
      display: flex;
      justify-content: space-between;
      margin-top: 30rpx;

      button {
        width: 45%;
        height: 80rpx;
        border-radius: 8rpx;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &.cancel {
          background-color: #f2f2f2;
          color: #333;
        }

        &.confirm {
          background-color: #4080ff;
          color: #fff;
        }
      }
    }
  }
}
</style>
