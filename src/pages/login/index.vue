<template>
  <view class="login-container">
    <view class="login-header">
      <image class="logo" src="/static/logo.png" mode="aspectFit" />
      <text class="title">英语批改助手</text>
    </view>

    <view class="login-content">
      <!-- 未同意隐私政策时显示 -->
      <button
        v-if="!isPrivacyChecked"
        class="login-btn"
        type="primary"
        @click="handleNotAgreedClick"
      >
        授权快速登录
      </button>

      <!-- 已同意隐私政策时显示 -->
      <button
        v-else
        class="login-btn"
        type="primary"
        open-type="getPhoneNumber"
        @getphonenumber="handleLogin"
        phone-number-no-quota-toast="false"
      >
        授权快速登录
      </button>

      <view class="privacy-container" @click="handlePrivacyChange">
        <view class="custom-checkbox" :class="{ 'checked': isPrivacyChecked }">
          <text v-if="isPrivacyChecked" class="checkbox-icon">✓</text>
        </view>
        <view class="tips">
          <text>我已经阅读同意</text>
          <text class="highlight" @click.stop="handlePrivacyClick">《用户协议》</text>
          <text>和</text>
          <text class="highlight" @click.stop="handlePrivacyClick">《隐私政策》</text>
        </view>
      </view>

      <view class="skip-login">
        <text @click="skipLogin">返回首页</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { wxMiniAppLogin } from '@/api/auth'
import { useUserStore } from '@/store/user'
import { getCurrentTeacher } from '@/api/teacher'
import {setLoginCache } from "@/libs/auth";

const userStore = useUserStore()
const isPrivacyChecked = ref(false)

const handlePrivacyChange = () => {
  isPrivacyChecked.value = !isPrivacyChecked.value
}

const handleNotAgreedClick = () => {
  uni.showModal({
    title: '提示',
    content: '请阅读并同意用户协议和隐私政策',
    confirmText: '同意',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        isPrivacyChecked.value = true
      }
    }
  })
}

const handleLogin = async (e) => {
  try {
    // 检查是否获取到手机号码
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      if (e.detail.errno === 1400001) {
        uni.showToast({
          title: '获取手机号次数已达上限',
          icon: 'none'
        })
      } else {
        uni.showToast({
          title: '授权失败，请重试',
          icon: 'none'
        })
      }
      return
    }

    uni.showLoading({
      title: '登录中...'
    })

    // 获取到code，调用登录接口
    const res = await wxMiniAppLogin(e.detail.code)
    setLoginCache(res.data)

    // 获取用户信息
    const teacherRes = await getCurrentTeacher()
    userStore.setUserInfo(teacherRes.data)

    uni.hideLoading()
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 登录成功后重新打开首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }, 1500)

  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none'
    })
    console.error('登录失败:', error)
  }
}

const handlePrivacyClick = () => {
  uni.openPrivacyContract()
}

const skipLogin = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}
</script>

<style lang="scss">
.login-container {
  min-height: 100vh;
  padding: 60rpx 40rpx;
  background-color: #fff;

  .login-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 100rpx;

    .logo {
      width: 300rpx;
      height: 300rpx;
      border-radius: 20rpx;
    }

    .title {
      font-size: 40rpx;
      font-weight: bold;
      color: #333;
    }

    .subtitle {
      margin-top: 20rpx;
      font-size: 28rpx;
      color: #666;
    }
  }

  .login-content {
    margin-top: 120rpx;

    .login-btn {
      width: 100%;
      height: 90rpx;
      line-height: 90rpx;
      border-radius: 45rpx;
      font-size: 32rpx;

      &.login-btn-disabled {
        opacity: 0.6;
      }
    }

    .privacy-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 20rpx;
      padding: 20rpx;
      cursor: pointer;

      .custom-checkbox {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #999;
        border-radius: 6rpx;
        margin-right: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        background-color: #fff;

        &.checked {
          background-color: #007AFF;
          border-color: #007AFF;
        }

        .checkbox-icon {
          color: #fff;
          font-size: 24rpx;
          line-height: 1;
        }
      }

      .tips {
        font-size: 24rpx;
        color: #999;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .highlight {
          color: #007AFF;
          padding: 0 2rpx;
        }
      }
    }

    .skip-login {
      text-align: center;
      margin-top: 60rpx;

      text {
        display: inline-block;
        font-size: 26rpx;
        color: #666;
        padding: 16rpx 40rpx;
        background: #f5f5f5;
        border-radius: 100rpx;

        &:active {
          background: #eee;
        }
      }
    }
  }
}
</style>
