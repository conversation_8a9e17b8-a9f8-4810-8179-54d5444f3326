<script setup>
import { ref } from 'vue';
import { extractQuestion, analysis, extractStudentList } from '@/llm/kimi';
import { chooseImageToBase64 } from '@/libs/utils';

const imageUrl = ref('');
const base64Image = ref('');
const result = ref(null);
const loading = ref(false);

const chooseImage = async () => {
  try {
    const { tempFilePath, base64 } = await chooseImageToBase64();
    imageUrl.value = tempFilePath;
    base64Image.value = base64;
  } catch (error) {
    uni.showToast({
      title: '图片读取失败',
      icon: 'error'
    });
    console.error('图片读取失败:', error);
  }
};

const handleAction = async (action) => {
  if (!base64Image.value) {
    uni.showToast({
      title: '请先选择图片',
      icon: 'none'
    });
    return;
  }

  loading.value = true;
  const startTime = Date.now();
  try {
    const actionFunction = action === 'extract' ? extractQuestion :
                          action === 'analysis' ? analysis :
                          extractStudentList;

    const analysisResult = await actionFunction(base64Image.value);
    const endTime = Date.now();
    const processingTime = ((endTime - startTime) / 1000).toFixed(2);
    result.value = {
      data: analysisResult,
      processingTime: `${processingTime}秒`
    };
  } catch (error) {
    uni.showToast({
      title: '处理失败',
      icon: 'error'
    });
    console.error('处理失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <view class="container">
    <view class="upload-section">
      <view class="button-group">
        <button class="upload-btn" @click="chooseImage">选择图片</button>
        <button class="action-btn" @click="handleAction('extract')">提取问题</button>
        <button class="action-btn" @click="handleAction('analysis')">批改</button>
        <button class="action-btn" @click="handleAction('student')">提取学生</button>
      </view>
      <image v-if="imageUrl" :src="imageUrl" mode="aspectFit" class="preview-image" />
    </view>

    <view v-if="loading" class="loading">
      <text>处理中...</text>
    </view>

    <view v-if="result" class="result-section">
      <text class="result-title">处理结果：</text>
      <view class="result-content">
        <view class="processing-time">处理耗时：{{ result.processingTime }}</view>
        <text>{{ JSON.stringify(result.data, null, 2) }}</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}

.upload-section {
  margin-bottom: 30rpx;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.upload-btn {
  flex: 1;
  min-width: 200rpx;
  background-color: #007AFF;
  color: #ffffff;
  font-size: 28rpx;
  padding: 15rpx 30rpx;
}

.action-btn {
  flex: 1;
  min-width: 200rpx;
  background-color: #4CD964;
  color: #ffffff;
  font-size: 28rpx;
  padding: 15rpx 30rpx;
}

.preview-image {
  width: 100%;
  height: 400rpx;
  margin-top: 20rpx;
  border-radius: 10rpx;
}

.loading {
  text-align: center;
  padding: 20rpx;
  color: #666666;
}

.result-section {
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.result-content {
  font-size: 28rpx;
  white-space: pre-wrap;
  word-break: break-all;
}

.processing-time {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 10rpx;
}
</style>
