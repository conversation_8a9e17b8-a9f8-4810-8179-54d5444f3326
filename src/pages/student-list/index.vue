<template>
  <view class="student-list-container">
    <!-- 搜索框组件 -->
    <search-box
      ref="searchBoxRef"
      placeholder="搜索学生姓名或学号"
      @search="handleSearch"
      @clear="clearSearch"
    />

    <!-- 学生列表组件 -->
    <student-table
      :students="filteredStudents"
      :loading="loading"
      :load-more-status="loadMoreStatus"
      @edit="handleEditStudent"
      @delete="handleDeleteStudent"
    />

    <!-- 添加学生按钮组件 -->
    <add-button-group
      @add="handleAddStudent"
      @paste="handlePasteImport"
      @excel="handleExcelImport"
    />

    <!-- 学生编辑模态框 -->
    <student-edit-modal
      :show="showEditModal"
      :student="editingStudent"
      @update:show="showEditModal = $event"
      @save="handleSaveStudent"
      @cancel="handleCancelEdit"
    />

    <!-- 导入模态框 -->
    <import-modal
      :show="showImportModal"
      :type="importType"
      @update:show="showImportModal = $event"
      @confirm="handleImportConfirm"
    />
  </view>
</template>

<script setup>
import {computed, reactive, ref} from 'vue';
import {onLoad, onPullDownRefresh, onReachBottom} from '@dcloudio/uni-app';
import StudentEditModal from './components/StudentEditModal.vue';
import ImportModal from './components/ImportModal.vue';
import {
  batchCreateStudents,
  createStudent,
  deleteStudent,
  getStudentList,
  parseStudentsFromExcel,
  updateStudent
} from '@/api/student';
import {uploadImage} from '@/libs/utils';
import SearchBox from './components/SearchBox.vue';
import StudentTable from './components/StudentTable.vue';
import AddButtonGroup from './components/AddButtonGroup.vue';
import {debounce} from "@/utils/tools";

// 从路由参数获取班级信息
const className = ref('');
const classId = ref('');

// 分页及加载状态
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});
const loading = ref(false);
const loadingMore = ref(false);
const isEnd = ref(false);
const loadMoreStatus = ref('more'); // 加载更多状态：more-加载前，loading-加载中，noMore-没有更多

onLoad((option) => {
  if (option.name) {
    className.value = option.name || '未知班级';
    // 动态设置导航栏标题包含班级名称
    uni.setNavigationBarTitle({
      title: className.value
    });
  }
  if (option.id) {
    classId.value = option.id;
  }

  // 加载学生数据
  loadStudents();
});

// 学生列表数据
const studentList = ref([]);

// 编辑相关数据
const editingStudent = ref(null);
const showEditModal = ref(false);

// 搜索框引用
const searchBoxRef = ref(null);

// 显示的学生列表
const filteredStudents = computed(() => studentList.value);

// 加载学生列表数据
const loadStudents = async (isRefresh = false) => {
  // 如果是刷新，重置分页和列表状态
  if (isRefresh) {
    pagination.current = 1;
    studentList.value = [];
    isEnd.value = false;
    loadMoreStatus.value = 'more';
  }

  // 如果已经加载完所有数据且不是刷新操作，直接返回
  if (isEnd.value && !isRefresh) return;

  try {
    // 设置加载状态
    loading.value = true;
    loadingMore.value = pagination.current > 1;
    loadMoreStatus.value = 'loading';

    // 构建请求参数
    const params = {
      pageNo: pagination.current,
      pageSize: pagination.size,
      classId: classId.value || undefined,
      name: searchBoxRef.value?.searchValue || undefined
    };

    // 请求数据
    const res = await getStudentList(params);

    if (res?.data) {
      // 更新总数
      pagination.total = res.data.total || 0;

      // 处理新数据
      const newStudents = res.data.list || [];
      studentList.value = isRefresh || pagination.current === 1
        ? newStudents
        : [...studentList.value, ...newStudents];

      // 更新分页状态
      isEnd.value = studentList.value.length >= pagination.total;
      if (!isEnd.value) {
        pagination.current++;
        loadMoreStatus.value = 'more';
      } else {
        loadMoreStatus.value = 'noMore';
      }
    }
  } catch (error) {
    console.error('加载学生列表失败:', error);
    uni.showToast({
      title: '加载学生列表失败',
      icon: 'none'
    });
    loadMoreStatus.value = 'more';
  } finally {
    // 重置加载状态
    loading.value = false;
    loadingMore.value = false;
    uni.stopPullDownRefresh();
  }
};

// 下拉刷新
onPullDownRefresh(() => {
  loadStudents(true);
});

// 上拉加载更多
onReachBottom(() => {
  if (!loading.value && !isEnd.value) {
    loadStudents();
  }
});

// 添加学生
const handleAddStudent = () => {
  // 重置编辑数据，并显示模态框
  editingStudent.value = null;
  showEditModal.value = true;
};

// 编辑学生
const handleEditStudent = (student, index) => {
  // 设置当前编辑的学生，并显示模态框
  editingStudent.value = { ...student, index };
  showEditModal.value = true;
};

// 删除学生
const handleDeleteStudent = (student, index) => {
  uni.showModal({
    title: '确认删除',
    content: `确定要删除学生 ${student.name} 吗？`,
    success: async (res) => {
      if (!res.confirm) return;

      try {
        uni.showLoading({ title: '删除中...' });
        await deleteStudent(student.id);

        // 删除成功后刷新列表
        loadStudents(true);

        uni.showToast({
          title: '删除成功',
          icon: 'success',
          duration: 2000
        });
      } catch (error) {
        console.error('删除学生失败:', error);
        uni.showToast({
          title: '删除失败，请重试',
          icon: 'none'
        });
      } finally {
        uni.hideLoading();
      }
    }
  });
};

// 保存学生信息
const handleSaveStudent = async (formData) => {
  const isEdit = !!editingStudent.value;
  const actionText = isEdit ? '更新' : '添加';

  try {
    uni.showLoading({
      title: `${actionText}中...`
    });

    const studentData = {
      name: formData.name,
      studentNumber: formData.studentId ? formData.studentId.trim() : undefined, // 学号为可选
      classId: classId.value || undefined
    };

    if (isEdit) {
      // 更新现有学生
      await updateStudent({
        ...studentData,
        id: editingStudent.value.id
      });
    } else {
      // 添加新学生
      await createStudent(studentData);
    }

    uni.showToast({
      title: `${actionText}成功`,
      icon: 'success',
      duration: 2000
    });

    // 刷新学生列表
    loadStudents(true);
  } catch (error) {
    console.error(`${actionText}学生失败:`, error);
    uni.showToast({
      title: `${actionText}失败，请重试`,
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 取消编辑
const handleCancelEdit = () => {
  editingStudent.value = null;
};

// 搜索处理 - 使用防抖函数优化
const handleSearch = debounce(() => {
  loadStudents(true);
}, 500);

// 清除搜索
const clearSearch = () => {
  loadStudents(true);
};



// 导入相关数据
const showImportModal = ref(false);
const importType = ref('paste');

// 粘贴导入
const handlePasteImport = () => {
  importType.value = 'paste';
  showImportModal.value = true;
};

// Excel导入
const handleExcelImport = () => {
  importType.value = 'excel';
  showImportModal.value = true;
};

// 处理导入确认
const handleImportConfirm = async (data) => {
  try {
    if (data.type === 'paste') {
      // 处理粘贴导入
      // 支持多种分隔符：换行、空格、逗号
      const names = data.content
        .split(/[\n\s,]+/) // 使用正则表达式匹配换行、空格和逗号
        .map(name => name.trim())
        .filter(name => name && name.length > 0); // 过滤空字符串

      if (names.length === 0) {
        uni.showToast({
          title: '没有有效的学生姓名',
          icon: 'none'
        });
        return;
      }

      // 显示确认对话框
      uni.showModal({
        title: '确认导入',
        content: `检测到 ${names.length} 个学生姓名，是否确认导入？`,
        success: async (modalRes) => {
          if (!modalRes.confirm) return;

          try {
            uni.showLoading({ title: '导入中...' });
            // 批量创建学生
            await batchCreateStudents({
              students: names.map(name => ({
                name,
                studentNumber: ''
              })),
              classId: classId.value
            });

            showImportModal.value = false;

            // 刷新列表
            await loadStudents(true);

            uni.showToast({
              title: '导入成功',
              icon: 'success'
            });
          } catch (error) {
            console.error('批量添加学生失败:', error);
            uni.showToast({
              title: '导入失败，请重试',
              icon: 'none'
            });
          } finally {
            uni.hideLoading();
          }
        }
      });
    } else {
      // 处理Excel导入
      try {
        uni.showLoading({ title: '上传中...' });
        console.log(data)
        // 上传Excel文件
        const uploadRes = await uploadImage(data.file.path, 'excel', false);

        uni.showLoading({ title: '解析中...' });
        // 使用新接口解析Excel文件
        const parseRes = await parseStudentsFromExcel(uploadRes.data.ossUrl);

        // 解析返回结果
        let students = [];
        try {
          students = JSON.parse(parseRes.data)
        } catch (error) {
          console.error('解析返回结果失败:', error);
          // 提示用户检查Excel内容
          uni.showModal({
            title: '解析失败',
            content: '无法解析Excel文件内容，请检查：\n1. Excel文件是否包含正确的学生信息\n2. 是否包含姓名和学号列\n3. 数据格式是否正确',
            showCancel: false
          });
          throw new Error('解析返回结果失败');
        }

        if (students.length === 0) {
          uni.showModal({
            title: '未找到学生信息',
            content: 'Excel文件中未找到有效的学生信息，请检查：\n1. 是否包含学生姓名\n2. 数据是否在正确的列中\n3. 是否有空行或格式问题',
            showCancel: false
          });
          return;
        }

        // 显示解析结果确认
        uni.showModal({
          title: '解析结果',
          content: `成功解析到 ${students.length} 个学生信息，是否确认导入？`,
          success: async (confirmRes) => {
            if (!confirmRes.confirm) return;

            try {
              uni.showLoading({ title: '导入中...' });
              // 批量创建学生
              await batchCreateStudents({
                students: students.map((student, index) => ({
                  name: student.name,
                  studentNumber: student.no || `${index + 1}`,
                })),
                classId: classId.value
              });

              showImportModal.value = false;

              // 刷新列表
              await loadStudents(true);

              uni.showToast({
                title: '导入成功',
                icon: 'success'
              });
            } catch (error) {
              console.error('批量添加学生失败:', error);
              uni.showToast({
                title: '导入失败，请重试',
                icon: 'none'
              });
            } finally {
              uni.hideLoading()
            }
          }
        });
      } catch (error) {
        console.error('处理Excel文件失败:', error);
        uni.showToast({
          title: '导入失败，请重试',
          icon: 'none'
        });
      }
    }
  } catch (error) {
    console.error('导入失败:', error);
    uni.showToast({
      title: '导入失败，请重试',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};
</script>

<style lang="scss" scoped>
.student-list-container {
  padding-bottom: 90rpx; /* Add bottom padding to account for fixed button */
}

// 班级标题
.class-title {
  background-color: #4080ff;
  color: #ffffff;
  padding: 24rpx 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}
</style>

