<template>
  <view class="add-button-group">
    <!-- 主按钮 -->
    <view class="main-button" @click="handleShowAction">
      <text class="add-icon">+</text>
      <text>添加学生</text>
    </view>
  </view>
</template>

<script setup>
const emit = defineEmits(['add', 'paste', 'excel', 'downloadTemplate']);

// 下载模板
const handleDownloadTemplate = () => {
  uni.showLoading({ title: '正在下载...' });
  //通过wx.openDocument 打开文档，openDocument 提供了一个属性showMenu 显示右上角的菜单
  wx.downloadFile({
    filePath: `${wx.env.USER_DATA_PATH}/学生名册模板.xlsx`,
    url: 'https://quick-marker-mini.oss-cn-beijing.aliyuncs.com/static/%E5%AD%A6%E7%94%9F%E5%90%8D%E5%86%8C.xlsx',
    success (res) {
      if (res.statusCode === 200) {
        const filePath = res.filePath
        wx.openDocument({
          filePath: filePath,
          showMenu: true,
          success: function (res) {
            console.log(res, '打开文档成功');
          },
          fail: function (err) {
            console.log(err, '打开文档失败');
          }
        });
      }
    },
    fail: function (err) {
      console.log(err, '下载文档失败');
    },
    complete: () => {
      uni.hideLoading();
    }
  })
};

// 显示操作菜单
const handleShowAction = () => {
  uni.showActionSheet({
    itemList: ['从聊天选择学生名册excel导入(推荐)', '下载学生名册excel模板', '粘贴导入', '手动添加'],
    itemColor: '#333333',
    success: (res) => {
      if (res.tapIndex === 1) {
        handleDownloadTemplate();
        return;
      }
      const actions = ['excel', 'downloadTemplate', 'paste', 'add'];
      emit(actions[res.tapIndex]);
    }
  });
};
</script>

<style lang="scss" scoped>
.add-button-group {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
}

.main-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #007AFF;
  color: #fff;
  height: 96rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);

  .add-icon {
    font-size: 36rpx;
    margin-right: 12rpx;
  }

  text {
    font-size: 28rpx;
  }
}
</style>
