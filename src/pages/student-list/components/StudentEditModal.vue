<template>
  <view class="edit-modal" v-if="show">
    <view class="modal-content">
      <view class="modal-title">{{ isEdit ? '编辑学生' : '添加学生' }}</view>

      <view class="form-item">
        <view class="label">姓名</view>
        <input
          type="text"
          v-model="formData.name"
          placeholder="请输入学生姓名"
        />
      </view>

      <view class="form-item">
        <view class="label">学号（选填）</view>
        <input
          type="text"
          v-model="formData.studentId"
          placeholder="请输入学号（选填）"
        />
      </view>

      <view class="button-group">
        <button class="cancel" @click="handleCancel">取消</button>
        <button class="confirm" @click="handleConfirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, computed } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  student: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:show', 'save', 'cancel']);

// 是否是编辑模式
const isEdit = computed(() => props.student !== null);

// 表单数据
const formData = ref({
  name: '',
  studentId: ''
});

// 当学生数据变化时，更新表单数据
watch([() => props.student, () => props.show], ([newStudent, newShow]) => {
  if (newShow) {
    if (newStudent) {
      formData.value = {
        name: newStudent.name,
        studentId: newStudent.studentNumber || ''
      };
    } else {
      formData.value = {
        name: '',
        studentId: ''
      };
    }
  }
}, { immediate: true });

// 确认按钮
const handleConfirm = () => {
  // 表单验证
  if (!formData.value.name.trim()) {
    uni.showToast({
      title: '请输入学生姓名',
      icon: 'none'
    });
    return;
  }

  // 提交表单
  emit('save', { ...formData.value });

  // 关闭弹窗
  emit('update:show', false);
};

// 取消按钮
const handleCancel = () => {
  emit('update:show', false);
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;

  .modal-content {
    width: 80%;
    background-color: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    box-sizing: border-box;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 30rpx;
      text-align: center;
    }

    .form-item {
      margin-bottom: 20rpx;

      .label {
        font-size: 28rpx;
        margin-bottom: 10rpx;
      }

      input {
        width: 100%;
        height: 80rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 0 20rpx;
        box-sizing: border-box;
        font-size: 28rpx;
      }
    }

    .button-group {
      display: flex;
      justify-content: space-between;
      margin-top: 30rpx;

      button {
        width: 45%;
        height: 80rpx;
        border-radius: 8rpx;
        font-size: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &.cancel {
          background-color: #f2f2f2;
          color: #333;
        }

        &.confirm {
          background-color: #4080ff;
          color: #fff;
        }
      }
    }
  }
}
</style>
