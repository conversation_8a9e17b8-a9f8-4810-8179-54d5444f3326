<template>
  <uni-popup ref="popup" type="center">
    <view class="paste-modal">
      <view class="modal-header">
        <text class="title">粘贴识别</text>
        <text class="close" @click="handleClose">×</text>
      </view>
      
      <view class="modal-content">
        <view class="tips">
          <text>请粘贴学生姓名和学号，每行一个，格式：姓名 学号</text>
          <text>示例：</text>
          <text>张三 2024001</text>
          <text>李四 2024002</text>
        </view>
        
        <textarea
          class="paste-area"
          v-model="pasteContent"
          placeholder="在此粘贴学生信息..."
          :maxlength="-1"
          @input="handleInput"
        />
        
        <view class="preview" v-if="recognizedStudents.length > 0">
          <view class="preview-title">识别结果：</view>
          <view class="preview-list">
            <view 
              v-for="(student, index) in recognizedStudents" 
              :key="index"
              class="preview-item"
            >
              <text>{{ student.name }}</text>
              <text>{{ student.studentNumber }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button 
          class="btn cancel" 
          @click="handleClose"
        >取消</button>
        <button 
          class="btn confirm" 
          :disabled="!recognizedStudents.length"
          @click="handleConfirm"
        >确认导入</button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch } from 'vue';
import { recognizeStudentsFromText } from '@/api/student';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show', 'confirm']);

const popup = ref(null);
const pasteContent = ref('');
const recognizedStudents = ref([]);

// 监听 show 属性变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    popup.value?.open();
  } else {
    popup.value?.close();
  }
});

// 处理输入变化
const handleInput = async () => {
  if (!pasteContent.value.trim()) {
    recognizedStudents.value = [];
    return;
  }
  
  try {
    const { data } = await recognizeStudentsFromText(pasteContent.value);
    recognizedStudents.value = JSON.parse(data);
  } catch (error) {
    console.error('识别失败:', error);
    uni.showToast({
      title: '识别失败，请检查格式',
      icon: 'none'
    });
  }
};

// 关闭模态框
const handleClose = () => {
  pasteContent.value = '';
  recognizedStudents.value = [];
  emit('update:show', false);
};

// 确认导入
const handleConfirm = () => {
  if (!recognizedStudents.value.length) return;
  
  emit('confirm', recognizedStudents.value);
  handleClose();
};
</script>

<style lang="scss" scoped>
.paste-modal {
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  
  .modal-header {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #eee;
    
    .title {
      font-size: 32rpx;
      font-weight: 500;
    }
    
    .close {
      font-size: 40rpx;
      color: #999;
      padding: 0 20rpx;
    }
  }
  
  .modal-content {
    padding: 30rpx;
    
    .tips {
      margin-bottom: 20rpx;
      color: #666;
      font-size: 24rpx;
      
      text {
        display: block;
        line-height: 1.6;
      }
    }
    
    .paste-area {
      width: 100%;
      height: 300rpx;
      background: #f5f5f5;
      padding: 20rpx;
      border-radius: 10rpx;
      font-size: 28rpx;
      margin-bottom: 20rpx;
    }
    
    .preview {
      .preview-title {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
      }
      
      .preview-list {
        max-height: 300rpx;
        overflow-y: auto;
        
        .preview-item {
          display: flex;
          justify-content: space-between;
          padding: 10rpx 0;
          font-size: 26rpx;
          color: #666;
          border-bottom: 1rpx solid #eee;
          
          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }
  
  .modal-footer {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: flex-end;
    border-top: 1rpx solid #eee;
    
    .btn {
      margin-left: 20rpx;
      padding: 0 40rpx;
      height: 70rpx;
      line-height: 70rpx;
      border-radius: 35rpx;
      font-size: 28rpx;
      
      &.cancel {
        background: #f5f5f5;
        color: #666;
      }
      
      &.confirm {
        background: #4080ff;
        color: #fff;
        
        &:disabled {
          background: #ccc;
        }
      }
    }
  }
}
</style> 