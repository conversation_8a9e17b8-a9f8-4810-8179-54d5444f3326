<template>
  <uni-popup ref="popup" type="center">
    <view class="import-modal">
      <view class="modal-header">
        <text class="title">{{ title }}</text>
        <text class="close-icon" @click="handleClose">×</text>
      </view>

      <view class="modal-content">
        <textarea
          v-if="type === 'paste'"
          v-model="content"
          class="import-textarea"
          placeholder="请粘贴或输入学生姓名，每行一个名字"
          :maxlength="-1"
        />
        <view v-else class="excel-import">
          <text class="tip">请从微信聊天中选择学生名册Excel文件</text>
          <view class="file-info" v-if="selectedFile">
            <text class="file-name">{{ selectedFile.name }}</text>
          </view>
          <text v-if="isPC" class="pc-tip">注意：电脑端微信小程序不支持选择文件，请使用手机操作</text>
          <button v-else class="select-btn" @click="handleSelectExcel">选择文件</button>
        </view>
      </view>

      <view class="modal-footer">
        <button class="cancel-btn" @click="handleClose">取消</button>
        <button class="confirm-btn" @click="handleConfirm">确认导入</button>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// 检测是否为PC环境
const isPC = ref(false);

// 在组件挂载时检测设备类型
uni.getSystemInfo({
  success: (res) => {
    // 检查平台是否为Windows或macOS
    isPC.value = res.platform === 'windows' || res.platform === 'mac';
  }
});

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'paste' // 'paste' 或 'excel'
  }
});

const emit = defineEmits(['update:show', 'confirm']);

const popup = ref(null);
const content = ref('');
const selectedFile = ref(null);

// 计算标题
const title = computed(() => props.type === 'paste' ? '粘贴导入' : 'Excel导入');

// 监听show变化
watch(() => props.show, (newVal) => {
  if (newVal) {
    // 打开时清理数据
    content.value = '';
    selectedFile.value = null;
    popup.value?.open();
  } else {
    // 关闭时清理数据
    content.value = '';
    selectedFile.value = null;
    popup.value?.close();
  }
});

// 关闭模态框
const handleClose = () => {
  content.value = '';
  selectedFile.value = null;
  emit('update:show', false);
};

// 选择Excel文件
const handleSelectExcel = () => {

  if (isPC.value) {
    uni.showToast({
      title: '请使用手机操作',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  uni.chooseMessageFile({
    count: 1,
    type: 'file',
    extension: ['xlsx', 'xls'],
    success: (res) => {
      selectedFile.value = res.tempFiles[0];
      uni.showToast({
        title: '文件选择成功',
        icon: 'success'
      });
    }
  });
};

// 确认导入
const handleConfirm = () => {
  if (props.type === 'paste') {
    if (!content.value.trim()) {
      uni.showToast({
        title: '请输入学生姓名',
        icon: 'none'
      });
      return;
    }
    emit('confirm', {
      type: 'paste',
      content: content.value
    });
  } else {
    if (!selectedFile.value) {
      uni.showToast({
        title: '请选择Excel文件',
        icon: 'none'
      });
      return;
    }
    emit('confirm', {
      type: 'excel',
      file: selectedFile.value
    });
  }
};
</script>

<style lang="scss" scoped>
.import-modal {
  width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

  .title {
    font-size: 34rpx;
    font-weight: 600;
    color: #000;
  }

  .close-icon {
    font-size: 44rpx;
    color: #8E8E93;
    padding: 12rpx;
    line-height: 1;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }
}

.modal-content {
  padding: 32rpx;
  box-sizing: border-box;

  .import-textarea {
    width: 100%;
    box-sizing: border-box;
    height: 320rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.1);
    border-radius: 12rpx;
    padding: 24rpx;
    font-size: 30rpx;
    line-height: 1.5;
    background-color: #f8f8f8;
    transition: all 0.2s ease;

    &:focus {
      border-color: #007AFF;
      background-color: #fff;
    }
  }

  .excel-import {
    text-align: center;
    padding: 32rpx 0;

    .tip {
      font-size: 30rpx;
      color: #8E8E93;
      margin-bottom: 20rpx;
      display: block;
    }

    .pc-tip {
      font-size: 26rpx;
      color: #FF3B30;
      margin-bottom: 40rpx;
      display: block;
    }

    .file-info {
      margin-bottom: 32rpx;

      .file-name {
        font-size: 28rpx;
        color: #333;
        background-color: #f8f8f8;
        padding: 16rpx 24rpx;
        border-radius: 8rpx;
        display: inline-block;
        max-width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .select-btn {
      background-color: #007AFF;
      color: #fff;
      font-size: 28rpx;
      padding: 16rpx 32rpx;
      border-radius: 8rpx;
      font-weight: 500;
      transition: all 0.2s ease;
      display: inline-block;
      min-width: 200rpx;

      &:active {
        opacity: 0.8;
        transform: scale(0.98);
      }
    }
  }
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);

  button {
    flex: 1;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    font-size: 32rpx;
    background: none;
    border: none;
    transition: all 0.2s ease;

    &::after {
      border: none;
    }

    &.cancel-btn {
      color: #007AFF;
      border-right: 1rpx solid rgba(0, 0, 0, 0.1);

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }

    &.confirm-btn {
      color: #007AFF;
      font-weight: 500;

      &:active {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
  }
}
</style>
