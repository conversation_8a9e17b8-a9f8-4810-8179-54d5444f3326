<template>
  <view class="class-add">
    <!-- 表单区域 -->
    <view class="form-container">
      <view class="form-item">
        <view class="label">班级名称</view>
        <view class="input-wrap">
          <input
            class="input"
            v-model="className"
            placeholder="请输入班级名称，最多12个字"
            maxlength="12"
            @input="handleInput"
          />
          <view class="char-count">{{ className.length }}/12</view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-btn" @tap="handleSubmit">{{ isEdit ? '确认修改' : '确认添加' }}</view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { createClass, updateClass } from '@/api/class';

// 班级名称
const className = ref('');
const isEdit = ref(false);
const classId = ref('');

// 输入处理
const handleInput = () => {
  // 移除首尾空格
  className.value = className.value.trim();
};

// 提交表单
const handleSubmit = async () => {
  if (!className.value.trim()) {
    uni.showToast({
      title: '请输入班级名称',
      icon: 'none'
    });
    return;
  }

  try {
    uni.showLoading({
      title: isEdit.value ? '修改中...' : '添加中...'
    });

    const data = {
      className: className.value.trim()
    };

    if (isEdit.value) {
      data.id = classId.value;
    }

    data.teacherId = uni.getStorageSync('userInfo').id

    const res = await (isEdit.value ? updateClass(data) : createClass(data));

    if (res.code === 0) {
      uni.showToast({
        title: isEdit.value ? '修改成功' : '添加成功',
        icon: 'success'
      });

      // 返回班级管理页面
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    } else {
      uni.showToast({
        title: res.message || (isEdit.value ? '修改失败' : '添加失败'),
        icon: 'none'
      });
    }
  } catch (error) {
    console.error(isEdit.value ? '修改班级失败:' : '添加班级失败:', error);
    uni.showToast({
      title: isEdit.value ? '修改失败' : '添加失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};

// 页面加载时获取参数
onMounted(() => {
  const pages = getCurrentPages();
  const page = pages[pages.length - 1];
  const { id, name } = page.options;

  if (id && name) {
    isEdit.value = true;
    classId.value = id;
    className.value = decodeURIComponent(name);
  }
});
</script>

<style lang="scss" scoped>
.class-add {
  background-color: #f8f9fa;
  min-height: 100vh;

  .form-container {
    background-color: #fff;
    padding: 30rpx;

    .form-item {
      margin-bottom: 30rpx;

      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
      }

      .input-wrap {
        position: relative;

        .input {
          height: 90rpx;
          border: 1px solid #e5e5e5;
          border-radius: 8rpx;
          padding: 0 30rpx;
          font-size: 28rpx;
          width: 100%;
          box-sizing: border-box;
        }

        .char-count {
          position: absolute;
          right: 30rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .submit-btn {
      margin-top: 60rpx;
      height: 90rpx;
      background-color: #4e80f0;
      color: #fff;
      font-size: 32rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8rpx;
    }
  }
}
</style>
