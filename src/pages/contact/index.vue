<template>
  <view class="container">
    <!-- Benefits Section -->
    <view class="benefits-card">
      <view class="benefits-list">
        <view class="benefit-item">
          <view class="icon-wrapper">
            <uni-icons type="checkmarkempty" size="16" color="#ffffff"/>
          </view>
          <text>获取最新教学功能更新通知</text>
        </view>
        <view class="benefit-item">
          <view class="icon-wrapper">
            <uni-icons type="checkmarkempty" size="16" color="#ffffff"/>
          </view>
          <text>参与教学经验交流讨论</text>
        </view>
        <view class="benefit-item">
          <view class="icon-wrapper">
            <uni-icons type="checkmarkempty" size="16" color="#ffffff"/>
          </view>
          <text>获得专业教学指导与支持</text>
        </view>
      </view>
    </view>

    <!-- English Teachers Group -->
    <view class="qrcode-container">
      <text class="qrcode-title">英语教师交流群</text>
      <image class="qrcode-image" src="https://quick-marker-mini.oss-cn-beijing.aliyuncs.com/static/qr-temp.png" mode="aspectFit" show-menu-by-longpress/>
      <text class="qrcode-tip">长按识别二维码加入教师交流群</text>
    </view>

    <!-- Xiaohongshu Account -->
    <view class="qrcode-container">
      <text class="qrcode-title">小红书账号</text>
      <image class="qrcode-image" src="/static/hong-shu-group.jpg" mode="aspectFit" show-menu-by-longpress/>
      <text class="qrcode-tip">请使用小红书 App 扫描二维码关注我们的账号</text>
      
      <view class="account-id-container">
        <text class="account-id-label">账号ID：</text>
        <text class="account-id">*********</text>
        <button class="copy-btn" @click="copyAccountId">复制账号</button>
      </view>
    </view>
  </view>
</template>

<script setup>
// 复制小红书账号到剪贴板
const copyAccountId = () => {
  uni.setClipboardData({
    data: '*********',
    success: () => {
      uni.showToast({
        title: '账号已复制',
        icon: 'success',
        duration: 2000
      });
    }
  });
};
</script>

<style lang="scss">
.container {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
}

.qrcode-container {
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  width: 90%;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.qrcode-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -12rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 4rpx;
    background-color: #4080ff;
    border-radius: 2rpx;
  }
}

.qrcode-image {
  width: 420rpx;
  height: 420rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.qrcode-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 50rpx;
}

.benefits-card {
  width: 100%;
  background-color: #f9fafc;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.benefits-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 30rpx;
  display: block;
  text-align: center;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.benefit-item {
  display: flex;
  align-items: center;

  .icon-wrapper {
    width: 32rpx;
    height: 32rpx;
    border-radius: 50%;
    background-color: #4080ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
  }

  text {
    font-size: 28rpx;
    color: #333;
    flex: 1;
  }
}

.account-id-container {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  background-color: #f8f9fb;
  padding: 16rpx 24rpx;
  border-radius: 10rpx;
  width: 90%;
}

.account-id-label {
  font-size: 28rpx;
  color: #666;
}

.account-id {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin: 0 12rpx;
}

.copy-btn {
  background-color: #4080ff;
  color: white;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 8rpx;
  margin-left: auto;
  line-height: 1.5;
}
</style>
