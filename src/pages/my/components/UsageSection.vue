<template>
  <view class="usage-section">
    <view class="usage-header">
      <text class="usage-title">使用记录</text>
      <view class="view-all" @click="navigateToRecords">
        <text>查看全部</text>
      </view>
    </view>

    <view class="usage-content">
      <template v-if="!loading && records.length > 0">
        <view
          v-for="(record, index) in records"
          :key="index"
          class="usage-item"
          hover-class="item-hover"
        >
          <view class="usage-info">
            <view class="usage-name">{{ record.title }}</view>
            <view class="usage-date">{{ formatDate(record.createTime) }}</view>
          </view>
          <view class="usage-stats">
            <view class="usage-count">批改数量: {{ record.usedCorrectionCount }}</view>
            <view class="usage-credits">消耗次数: {{ record.usedCorrectionCount }}</view>
          </view>
        </view>
      </template>

      <view v-else-if="loading" class="loading-state">
        <uni-load-more status="loading" :contentText="loadingText"></uni-load-more>
      </view>

      <Empty v-else class="empty-state" text="暂无使用记录"></Empty>
    </view>
  </view>
</template>

<script setup>
import Empty from "@/components/Empty.vue";
import { formatDate } from "@/utils/tools";

// 定义props
const props = defineProps({
  records: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 加载状态文本
const loadingText = {
  contentdown: '上拉显示更多',
  contentrefresh: '正在加载...',
  contentnomore: '没有更多数据了'
};

// 跳转到使用记录页面
const navigateToRecords = () => {
  uni.navigateTo({
    url: '/pages/my/usage-records/index'
  });
};
</script>

<style lang="scss" scoped>
.usage-section {
  margin: 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.usage-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.view-all {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999;
}

.usage-content {
  padding: 0 20rpx;
}

.usage-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx 10rpx;
  border-bottom: 1rpx solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.item-hover {
  background-color: #f9f9f9;
}

.usage-info {
  flex: 1;
  overflow: hidden;
}

.usage-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.usage-date {
  font-size: 24rpx;
  color: #999;
}

.usage-stats {
  text-align: right;
  min-width: 180rpx;
}

.usage-count, .usage-credits {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.loading-state {
  padding: 40rpx 0;
  display: flex;
  justify-content: center;
}
</style>
