<template>
  <view class="user-card">
    <!-- 个人信息部分 -->
    <view class="profile-section">
      <!-- 用户基本信息 -->
      <view class="user-basic-info">
        <image class="avatar" :src="userInfo.avatar || defaultAvatar" mode="aspectFill"></image>
        <view class="user-info">
          <view class="name-container">
            <text class="user-name">{{ userInfo.name || '' }}</text>
            <view class="vip-tag" v-if="isVip">
              <text>VIP会员</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 编辑按钮 -->
      <view class="edit-button-wrapper" @click="handleEdit">
        <view class="edit-button">编辑</view>
      </view>
    </view>

    <!-- 统计数据 -->
    <view class="stats-container">
      <!-- 剩余次数 -->
      <view class="stat-item">
        <text class="stat-label">剩余体验次数</text>
        <view class="stat-value-container">
          <text class="stat-value highlight">{{ userInfo.remainCorrectionCount }}</text>
          <text class="stat-unit">次</text>
        </view>
      </view>

      <!-- 会员信息 -->
      <view class="stat-item" v-if="isVip" @click="handleRecharge">
        <text class="stat-label">会员有效期至</text>
        <text class="stat-date">{{ formatDateYMD(userInfo.vipExpireTime) }}</text>
      </view>
      <view class="stat-item subscription-item" v-else @click="handleRecharge">
        <text class="stat-label">会员订阅</text>
        <view class="subscription-value">
          <text class="stat-value highlight">不限次，每天低至0.6元</text>
        </view>
      </view>
    </view>

    <!-- 功能按钮区域 (使用水平菜单样式) -->
    <view class="feature-menu-container">
      <!-- 加入用户交流群 -->
      <view class="menu-item" @click="handleJoinGroup">
        <view class="icon-wrapper">
          <image class="menu-icon" src="/static/icons/chat.svg" mode="aspectFit"></image>
        </view>
        <text class="menu-text">用户交流群</text>
      </view>

      <!-- 兑换码 -->
      <view class="menu-item" @click="handleRedeem">
        <view class="icon-wrapper">
          <image class="menu-icon" src="/static/icons/card.svg" mode="aspectFit"></image>
        </view>
        <text class="menu-text">兑换码</text>
      </view>

      <!-- 邀请好友 -->
      <view class="menu-item" @click="handleInvite">
        <view class="icon-wrapper">
          <image class="menu-icon" src="/static/icons/user-plus.svg" mode="aspectFit"></image>
        </view>
        <text class="menu-text">邀请老师</text>
      </view>


      <!-- 会员订阅 (主要按钮) -->
      <view class="menu-item primary-item" @click="handleRecharge">
        <view class="icon-wrapper primary-icon-wrapper">
          <image class="menu-icon" src="/static/icons/vip.svg" mode="aspectFit"></image>
        </view>
        <text class="menu-text primary-text">会员订阅</text>
      </view>
    </view>

    <!-- 二维码弹窗 -->
    <qr-code-popup
      :visible="showPopup"
      @close="closeQrCode"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import QrCodePopup from './QrCodePopup.vue';
import { formatDateYMD } from "@/utils/tools";

// 属性定义
const props = defineProps({
  userInfo: {
    type: Object,
    required: true,
    default: () => ({})
  },
  isVip: {
    type: Boolean,
    default: false
  }
});



// 事件
const emit = defineEmits(['edit', 'promo', 'contact']);

// 默认头像
const defaultAvatar = '/static/default-avatar.png';

// 二维码弹窗控制
const showPopup = ref(false);
const closeQrCode = () => { showPopup.value = false; };

// 事件处理方法
const handleEdit = () => emit('edit');
const handleJoinGroup = () => {
  uni.navigateTo({
    url: '/pages/contact/index'
  });
};
const handleRedeem = () => {
  uni.navigateTo({
    url: '/pages/redeem/index'
  });
};
const handleRecharge = () => {
  uni.navigateTo({
    url: '/pages/package/index'
  });
};
const handleInvite = () => {
  uni.navigateTo({
    url: '/pages/invitation/index'
  });
};
</script>

<style lang="scss" scoped>
// 颜色变量
$primary-color: $uni-primary-color;
$secondary-color: #AE95D0;
$text-primary: #000000;
$text-secondary: #666666;
$bg-light: #F9F9FB;
$white: #FFFFFF;
$promo-gradient-start: #FF6B6B;
$promo-gradient-end: #FF3366;
$highlight-color: #3c77ef;
$vip-color: #FFB800;
$vip-gradient-start: #FFD700;
$vip-gradient-end: #FFA500;

// 尺寸变量
$border-radius-sm: 14rpx;
$border-radius-md: 16rpx;
$border-radius-lg: 24rpx;
$border-radius-xl: 38rpx;
$spacing-xs: 4rpx;
$spacing-sm: 8rpx;
$spacing-md: 12rpx;
$spacing-lg: 24rpx;
$spacing-xl: 32rpx;

// 动画
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@keyframes shine {
  0% { transform: translateX(-200%) rotate(45deg); }
  100% { transform: translateX(200%) rotate(45deg); }
}

@keyframes glow {
  0% { box-shadow: 0 6rpx 16rpx rgba(60, 119, 239, 0.3); }
  50% { box-shadow: 0 8rpx 24rpx rgba(60, 119, 239, 0.5); }
  100% { box-shadow: 0 6rpx 16rpx rgba(60, 119, 239, 0.3); }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-3rpx); }
  100% { transform: translateY(0); }
}

.user-card {
  position: relative;
  background-color: $white;
  padding: 36rpx 0 32rpx;
  margin: 24rpx;
  border-radius: $border-radius-lg;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8rpx;
    background: linear-gradient(90deg, $highlight-color, #5c8ef0);
  }

  .profile-section {
    display: flex;
    padding: 0 $spacing-xl $spacing-lg;
    justify-content: space-between;
    align-items: center;
  }

  .user-basic-info {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    border: 3rpx solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    margin-right: 24rpx;
  }

  .user-info {
    display: flex;
    flex-direction: column;
    justify-content: center;

    .name-container {
      display: flex;
      align-items: center;
      margin-bottom: $spacing-sm;

      .user-name {
        font-size: 38rpx;
        font-weight: 600;
        margin-right: $spacing-md;
        color: $text-primary;
      }

      .vip-tag {
        background: linear-gradient(90deg, $vip-gradient-start, $vip-gradient-end);
        border-radius: $border-radius-lg;
        padding: 4rpx 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 36rpx;
        color: $white;
        font-size: 22rpx;
        font-weight: 600;
        line-height: 1;
        box-shadow: 0 2rpx 8rpx rgba(255, 184, 0, 0.3);

        .vip-icon {
          margin-right: 4rpx;
          font-size: 18rpx;
          animation: float 2s ease-in-out infinite;
        }
      }
    }
  }

  .edit-button-wrapper {
    padding: 20rpx;
    margin: -20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .edit-button {
    background-color: rgba(60, 119, 239, 0.08);
    border-radius: 25rpx;
    font-size: 24rpx;
    color: $highlight-color;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
    width: 90rpx;
    height: 52rpx;
    line-height: 52rpx;
    text-align: center;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.96);
    }
  }

  .stats-container {
    width: calc(100% - 60rpx);
    background-color: $bg-light;
    border-radius: $border-radius-md;
    padding: $spacing-lg 0;
    display: flex;
    margin: 24rpx auto $spacing-xl;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.03);
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: 50%;
      width: 2rpx;
      background-color: rgba(0, 0, 0, 0.06);
      transform: translateX(-50%);
    }

    .stat-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      padding: 6rpx 0;
      z-index: 1;

      .stat-date {
        font-size: 28rpx;
        color: $text-secondary;
      }

      .stat-label {
        font-size: 26rpx;
        color: $text-secondary;
        margin-bottom: $spacing-md;
      }

      .stat-value-container {
        display: flex;
        align-items: flex-end;
        position: relative;

        .stat-value {
          font-size: 42rpx;
          font-weight: 600;
          line-height: 1;
          color: $text-primary;

          &.highlight {
            color: $highlight-color;
          }
        }

        .stat-unit {
          font-size: 24rpx;
          color: $text-secondary;
          margin-left: $spacing-xs;
          margin-bottom: 6rpx;
        }
      }

      &.subscription-item {
        .subscription-value {
          display: flex;
          flex-direction: column;
          align-items: center;

          .stat-value {
            font-size: 28rpx;
            font-weight: normal;
            margin-bottom: 8rpx;

            &.highlight {
              color: $highlight-color;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .feature-menu-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx $spacing-xl;
    margin-top: 10rpx;
    width: 100%;
    box-sizing: border-box;

    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 25%;
      position: relative;

      &.primary-item {
        .icon-wrapper {
          background: #FF8C00;
          box-shadow: 0 6rpx 16rpx rgba(255, 140, 0, 0.3);
          transform: scale(1);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 50%;
            height: 200%;
            background: rgba(255, 255, 255, 0.2);
            transform: rotate(45deg);
            animation: shine 2s infinite;
          }
        }

        .primary-text {
          color: #FF8C00;
          font-weight: 500;
        }
      }

      .icon-wrapper {
        position: relative;
        margin-bottom: 12rpx;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #F5F5F5;
        display: flex;
        align-items: center;
        justify-content: center;

        .menu-icon {
          width: 44rpx;
          height: 44rpx;
          display: block;
          color: #333333;
        }
      }

      .primary-icon-wrapper {
        background: #FF8C00;
      }

      .menu-text {
        color: $text-primary;
        font-size: 24rpx;
        line-height: 1.4;
        text-align: center;
        margin-top: 8rpx;
      }
    }
  }
}
</style>
