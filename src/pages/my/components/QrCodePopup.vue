<template>
  <view>
    <view class="popup-mask" v-if="visible" @click="handleClose"></view>
    <view class="popup-content" v-if="visible">
      <view class="qr-popup">
        <view class="qr-title">长按加入用户交流群</view>
        <image 
          class="qr-image" 
          src="/static/contact_me_qr.png"
          mode="aspectFit"
          show-menu-by-longpress
        ></image>
        <button class="close-btn" @click="handleClose">关闭</button>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close']);

const handleClose = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
$primary-color: $uni-primary-color;
$secondary-color: #AE95D0;

.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
}

.popup-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.qr-popup {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  width: 560rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .qr-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .qr-image {
    width: 400rpx;
    height: 400rpx;
    margin: 20rpx 0;
  }

  .close-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    color: #FFFFFF;
    font-size: 28rpx;
    
    &::after {
      border: none;
    }

    &:active {
      opacity: 0.9;
    }
  }
}
</style> 