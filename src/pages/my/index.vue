<template>
  <view class="my-container">
    <!-- 个人信息卡片 -->
    <user-card
      :user-info="userInfo"
      :is-vip="userInfo.isMember"
      @edit="navigateToProfileEdit"
      @promo="handlePromo"
      @contact="handleContact"
    />

    <!-- 使用记录 -->
    <usage-section
      :records="usageRecords"
      :loading="loading"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onShow } from "@dcloudio/uni-app";
import { getCurrentTeacher } from '@/api/teacher';
import { correctionTaskList } from '@/api/correction-task';
import { useUserStore } from '@/store/user';

const userStore = useUserStore();

// 导入组件
import UserCard from './components/UserCard.vue';
import UsageSection from './components/UsageSection.vue';

// 使用记录数据
const usageRecords = ref([]);
const loading = ref(false);
const pageSize = 3; // 首页只显示3条记录
const userInfo = ref({})

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await getCurrentTeacher();
    userInfo.value = res.data;
    userStore.setUserInfo(res.data)
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
};

// 获取使用记录
const getUsageRecords = async () => {
  loading.value = true;
  try {
    const res = await correctionTaskList({
      pageSize,
      pageNo: 1
    });

    usageRecords.value = res.data.list
  } catch (error) {
    console.error('获取使用记录失败:', error);
  } finally {
    loading.value = false;
  }
};

// 跳转到个人资料编辑页
const navigateToProfileEdit = () => {
  uni.navigateTo({
    url: '/pages/my/profile-edit/index'
  });
};

// 处理推广
const handlePromo = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  });
};

// 处理联系客服
const handleContact = () => {
};

onShow(() => {
  getUserInfo();
  getUsageRecords();
});
</script>

<style lang="scss" scoped>
.my-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}
</style>
