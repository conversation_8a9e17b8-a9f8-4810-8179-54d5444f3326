<template>
  <view class="usage-records-container">
    <view class="content">
      <records-list
        :records="records"
        :loading="loading"
        :load-more-status="loadMoreStatus"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';
import { correctionTaskList } from '@/api/correction-task';
import RecordsList from './components/RecordsList.vue';

// 列表数据
const records = ref([]);
const page = ref(1);
const pageSize = ref(10);
const loading = ref(false);
const hasMore = ref(true);
const loadMoreStatus = ref('more');

// 获取记录列表
const getRecords = async (isRefresh = false) => {
  if (isRefresh) {
    page.value = 1;
    records.value = [];
    hasMore.value = true;
  }

  if (!hasMore.value && !isRefresh) return;

  loading.value = true;
  loadMoreStatus.value = 'loading';

  try {
    const res = await correctionTaskList({
      pageSize: pageSize.value,
      pageNo: page.value
    });

    if (res.code === 0 && res.data && res.data.list) {
      if (isRefresh) {
        records.value = res.data.list;
      } else {
        records.value = [...records.value, ...res.data.list];
      }

      hasMore.value = records.value.length < res.data.total;
      loadMoreStatus.value = hasMore.value ? 'more' : 'noMore';
      page.value++;
    }
  } catch (error) {
    console.error('获取使用记录失败:', error);
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    });
  } finally {
    loading.value = false;
    if (isRefresh) {
      uni.stopPullDownRefresh();
    }
  }
};

// 下拉刷新
onPullDownRefresh(async () => {
  await getRecords(true);
  uni.stopPullDownRefresh();
});

// 触底加载更多
onReachBottom(() => {
  if (!loading.value && hasMore.value) {
    getRecords();
  }
});

onShow(() => {
  getRecords(true);
});
</script>

<style lang="scss" scoped>
.usage-records-container {
  min-height: 100vh;
  background-color: #f5f5f5;

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}
</style>
