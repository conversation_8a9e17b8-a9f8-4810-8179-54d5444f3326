<template>
  <view class="records-list-component">
    <view class="records-list">
      <block v-if="records.length > 0">
        <view
          v-for="(record, index) in records"
          :key="record.id"
          class="record-item"
          hover-class="item-hover"
          :hover-stay-time="100"
        >
          <view class="record-info">
            <view class="record-name">{{ record.title }}</view>
            <view class="record-date">{{ formatDate(record.createTime) }}</view>
          </view>
          <view class="record-stats">
            <view class="record-count">批改数量: {{ record.usedCorrectionCount }}</view>
            <view class="record-credits">消耗积分: {{ record.usedCorrectionCount }}</view>
          </view>
        </view>
      </block>

      <!-- 加载状态 -->
      <uni-load-more :status="loadMoreStatus" :content-text="loadMoreText" />

      <!-- 空状态 -->
      <view v-if="!loading && records.length === 0" class="empty-state">
        <image class="empty-image" src="/static/icons/empty.svg" mode="aspectFit"></image>
        <text class="empty-text">暂无使用记录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import {formatDate} from "@/utils/tools";

const props = defineProps({
  records: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadMoreStatus: {
    type: String,
    default: 'more'
  }
});

const emit = defineEmits(['item-click']);

// 加载状态文本
const loadMoreText = {
  contentdown: '上拉显示更多',
  contentrefresh: '正在加载...',
  contentnomore: '没有更多数据了'
};
</script>

<style lang="scss" scoped>
.records-list-component {
  flex: 1;
  padding: 30rpx;
}

.records-list {
  position: relative;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transform: translateZ(0); /* 开启硬件加速 */
}

.record-item {
  display: flex;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
  background-color: #ffffff;
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }
}

.item-hover {
  background-color: #f9f9f9;
}

.record-info {
  flex: 1;
  overflow: hidden;
}

.record-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.record-date {
  font-size: 24rpx;
  color: #999;
}

.record-stats {
  text-align: right;
  min-width: 180rpx;
}

.record-count,
.record-credits {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.loading-wrapper {
  position: relative;
  height: 80rpx;
  opacity: 0;
  transition: opacity 0.2s ease;

  &.loading-visible {
    opacity: 1;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
