<template>
  <view class="profile-edit-container">
    <view class="edit-section">
      <view class="edit-item">
        <view class="avatar-wrapper">
          <image class="avatar" :src="userInfo.avatar || defaultAvatar" mode="aspectFill"></image>
          <button class="avatar-button" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
            更换头像
          </button>
        </view>
      </view>

      <view class="edit-item">
        <text class="item-label">昵称</text>
        <view class="input-wrapper">
          <input
            class="text-input"
            type="nickname"
            placeholder="请输入昵称"
            :value="userInfo.name"
            @change="onNicknameChange"
            @blur="onNicknameChange"
          />
        </view>
      </view>

      <view class="edit-item">
        <text class="item-label">手机号</text>
        <view class="phone-wrapper">
          <text class="phone-text">{{ maskPhoneNumber(userInfo.phone) }}</text>
          <button
            class="phone-button"
            open-type="getPhoneNumber"
            @getphonenumber="onGetPhoneNumber"
          >
            {{ userInfo.phone ? '更换' : '绑定' }}手机号
          </button>
        </view>
      </view>
    </view>

    <view class="action-section">
      <button class="save-btn" @tap="saveUserInfo">保存</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { getCurrentTeacher, updateTeacher } from '@/api/teacher';
import { uploadImage } from '@/libs/utils';
import {useUserStore} from "@/store/user";
import {onShow} from "@dcloudio/uni-app";
import { bindWxMiniAppPhone } from '@/api/auth';

// 默认头像
const defaultAvatar = '/static/default-avatar.png';

// 手机号脱敏处理
const maskPhoneNumber = (phone) => {
  if (!phone) return '未绑定';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 用户信息，从 API 获取
const userInfo = ref({});

const userStore  = useUserStore()

// 从API获取教师信息
const fetchTeacherInfo = async () => {
  try {
    uni.showLoading({ title: '加载中...' });
    const { data } = await getCurrentTeacher();
    userInfo.value = data;
    userStore.setUserInfo(data)
  } catch (e) {
    uni.hideLoading();
    console.error('获取教师信息失败', e);
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading()
  }
};

// 在页面加载时获取用户信息
onShow(() => {
  fetchTeacherInfo();
});

// 选择头像回调
const onChooseAvatar = async (e) => {
  try {
    const tempAvatarUrl = e.detail.avatarUrl;

    uni.showLoading({ title: '上传中...' });

    // 使用 utils.js 中的 uploadImage 方法上传临时头像
    const uploadResult = await uploadImage(tempAvatarUrl);
    uni.hideLoading();

    if (uploadResult.data.ossUrl) {
      userInfo.value = {
        ...userInfo.value,
        avatar: uploadResult.data.ossUrl
      }
      console.log('上传后的头像URL:', userInfo.avatar);
    } else {
      throw new Error('上传失败，未获取到有效的图片URL');
    }
  } catch (error) {
    uni.hideLoading();
    console.error('头像上传失败:', error);
    uni.showToast({
      title: '头像上传失败，请重试',
      icon: 'none'
    });
  }
};

// 昵称输入回调
const onNicknameChange = (e) => {
  userInfo.value = {
    ...userInfo.value,
    name: e.detail.value
  }
};

// 获取手机号回调
const onGetPhoneNumber = async (e) => {
  try {
    // 检查是否获取到手机号码
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      if (e.detail.errno === 1400001) {
        uni.showToast({
          title: '获取手机号次数已达上限',
          icon: 'none'
        })
      } else {
        uni.showToast({
          title: '授权失败，请重试',
          icon: 'none'
        })
      }
      return
    }

    uni.showLoading({ title: '更新中...' });

    // 使用新的绑定手机号接口
    await bindWxMiniAppPhone(e.detail.code)

    // 重新获取最新数据
    await fetchTeacherInfo();

    uni.hideLoading();
    uni.showToast({
      title: '手机号更新成功',
      icon: 'success'
    });
  } catch (error) {
    uni.hideLoading();
    console.error('更新手机号失败:', error);
    uni.showToast({
      title: error.message || '更新失败，请重试',
      icon: 'none'
    });
  }
};

// 保存用户信息
const saveUserInfo = async () => {
  if (!userInfo.value.name) {
    uni.showToast({
      title: '请输入昵称',
      icon: 'none'
    });
    return;
  }

  try {
    uni.showLoading({ title: '保存中...' });

    // 调用API更新用户信息
    await updateTeacher({
      id: userInfo.value.id,
      name: userInfo.value.name,
      avatar: userInfo.value.avatar,
    });

    // 重新获取最新数据
    await fetchTeacherInfo();


    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      complete: () => {
        setTimeout(() => uni.navigateBack(), 1500);
      }
    });
  } catch (error) {
    console.error('保存用户信息失败', error);
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none'
    });
  } finally {
    uni.hideLoading();
  }
};
</script>

<style lang="scss" scoped>
.profile-edit-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
  padding: 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
}

.edit-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.edit-item {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.item-label {
  font-size: 32rpx;
  color: #000000;
  margin-bottom: 20rpx;
  display: block;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 70rpx;
  margin-bottom: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-button {
  background-color: #0086ff;
  color: white;
  font-size: 28rpx;
  height: 70rpx;
  width: 180rpx;
  line-height: 70rpx;
  border-radius: 35rpx;
  padding: 0;

  &::after {
    border: none;
  }
}

.input-wrapper {
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.text-input {
  font-size: 30rpx;
  width: 100%;
  height: 50rpx;
  color: #333333;
}

.phone-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
}

.phone-text {
  font-size: 30rpx;
  color: #333333;
}

.phone-button {
  background-color: #0086ff;
  color: white;
  font-size: 28rpx;
  height: 60rpx;
  min-width: 160rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  padding: 0 20rpx;
  margin: 0;

  &::after {
    border: none;
  }
}

.action-section {
  padding: 0;
}

.save-btn {
  background-color: #0086ff;
  color: white;
  border-radius: 12rpx;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 34rpx;
  padding: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  &::after {
    border: none;
  }
}
</style>
