<template>
  <view>
    <!-- 头部组件 -->
    <view class="header-container">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="header" :style="{ height: titleBarHeight + 'px' }">
        <view class="avatar-container">
          <image class="avatar" :src="avatar"></image>
        </view>
        <text class="title">{{ title }}</text>
        <view class="right-area">
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getNavigationBarInfo } from '@/utils/tools.js';

const statusBarHeight = ref(20);
const titleBarHeight = ref(44);

onMounted(() => {
  const { statusBarHeight: sHeight, titleBarHeight: tHeight } = getNavigationBarInfo();
  statusBarHeight.value = sHeight;
  titleBarHeight.value = tHeight;
});

const props = defineProps({
  title: {
    type: String,
    default: '英语批改助手'
  },
  avatar: {
    type: String,
    default: ''
  },
  hasNewMessage: {
    type: Boolean,
    default: true
  },
  messageCount: {
    type: [String, Number],
    default: '2'
  }
});
</script>

<style lang="scss" scoped>
/* 防止滚动条出现的全局样式 */
page {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;

  .status-bar {
    background-color: #3f7bfc;
    width: 100%;
    box-sizing: border-box;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30rpx;
    background-color: #3f7bfc;
    color: #fff;
    width: 100%;
    box-sizing: border-box;

    .title {
      font-size: 32rpx;
      flex: 1;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .avatar-container {
      position: relative;
      min-width: 60rpx;
      max-width: 60rpx;

      .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: #f5f5f5;
      }

      .badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        background-color: #ff9500;
        color: white;
        font-size: 24rpx;
        min-width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
      }
    }

    .right-area {
      display: flex;
      align-items: center;
      min-width: 80rpx;
      max-width: 80rpx;
      justify-content: flex-end;

      .menu-dots {
        margin-right: 10rpx;
      }

      .menu-icon {
        font-size: 40rpx;
      }
    }
  }
}

/* 占位元素样式 */
.header-placeholder {
  width: 100%;
  box-sizing: border-box;
}
</style>
