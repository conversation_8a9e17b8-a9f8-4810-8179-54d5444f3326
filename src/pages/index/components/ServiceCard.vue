<template>
  <view class="service-card" :class="{ 'featured': featured }" @click="$emit('click')">
    <image class="service-icon" :src="iconSrc" mode="aspectFit" />
    <view class="service-title">{{ title }}</view>
    <view class="service-description">{{ description }}</view>
    <view class="service-footer" v-if="showFooter || featured">
      <text class="service-more">{{ featured ? '必读指南' : '了解更多' }}</text>
      <text class="service-arrow">></text>
    </view>
  </view>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  iconSrc: {
    type: String,
    required: true
  },
  path: {
    type: String,
    default: ''
  },
  showFooter: {
    type: Boolean,
    default: false
  },
  featured: {
    type: Boolean,
    default: false
  }
})

defineEmits(['click'])
</script>

<style lang="scss" scoped>
.service-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &.featured {
    background-color: #f0f7ff;
    border: 2rpx solid #4080ff;
    box-shadow: 0 4rpx 12rpx rgba(64, 128, 255, 0.15);
    padding: 28rpx;
  }

  .service-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
    margin-bottom: 16rpx;
  }

  .service-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 12rpx;
  }

  .service-description {
    font-size: 24rpx;
    color: #999;
    line-height: 1.5;
    margin-bottom: 16rpx;
  }

  .service-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .service-more {
      font-size: 24rpx;
      color: #666;
    }

    .service-arrow {
      font-size: 24rpx;
      color: #999;
    }
  }

  &.featured {
    .service-title {
      color: #4080ff;
      font-size: 32rpx;
    }

    .service-description {
      color: #666;
    }

    .service-footer {
      .service-more {
        color: #4080ff;
        font-weight: 600;
      }

      .service-arrow {
        color: #4080ff;
      }
    }
  }
}
</style>
