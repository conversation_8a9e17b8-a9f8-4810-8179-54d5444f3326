<template>
  <view class="tab-bar">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: currentTab === item.key }"
      @tap="onTabClick(item.key, item.path)"
    >
      <text class="iconfont" :class="item.icon"></text>
      <text class="tab-label">{{ item.label }}</text>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  currentTab: {
    type: String,
    default: 'home'
  },
  tabs: {
    type: Array,
    default: () => [
      { key: 'home', label: '首页', icon: 'icon-home', path: '/pages/index/index' },
      { key: 'grades', label: '成绩', icon: 'icon-list', path: '/pages/grades/index' },
      { key: 'my', label: '我的', icon: 'icon-user', path: '/pages/my/index' }
    ]
  }
});

const emit = defineEmits(['tabChange']);

const onTabClick = (key, path) => {
  emit('tabChange', key);

  if (key !== props.currentTab) {
    uni.switchTab({
      url: path
    });
  }
};
</script>

<style lang="scss" scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: #fff;
  display: flex;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;

    .iconfont {
      font-size: 48rpx;
      margin-bottom: 6rpx;
      color: #8f8f94;
    }

    .tab-label {
      font-size: 24rpx;
      color: #8f8f94;
    }

    &.active {
      .iconfont, .tab-label {
        color: #3f7bfc;
      }
    }
  }
}
</style>
