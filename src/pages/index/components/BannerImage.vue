<template>
  <view class="banner-container">
    <swiper class="banner-swiper" circular autoplay interval="3000" duration="500" indicator-dots>
      <swiper-item v-for="(banner, index) in imageList" :key="index">
        <image
          class="banner-image"
          :src="typeof banner === 'string' ? banner : banner.url"
          mode="aspectFill"
          @click="handleBannerClick(banner)"
        ></image>
      </swiper-item>
    </swiper>
  </view>
</template>

<script setup>
defineProps({
  imageList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['banner-click'])

const handleBannerClick = (banner) => {
  if (typeof banner === 'object' && banner.type) {
    emit('banner-click', banner)
  }
}
</script>

<style lang="scss" scoped>
.banner-container {
  border-radius: 20rpx;
  overflow: hidden;
  height: 300rpx;
  margin: 20rpx;

  .banner-swiper {
    width: 100%;
    height: 100%;
  }

  .banner-image {
    width: 100%;
    height: 100%;
  }
}
</style>
