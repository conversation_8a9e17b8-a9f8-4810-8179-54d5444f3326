<template>
  <view class="score-query-card">
    <view class="card-header">
      <text class="title">最近批改</text>
      <text class="more" @click="onViewMore">查看更多</text>
    </view>
    <view class="card-content">
      <Empty v-if="!recentRecords || recentRecords.length === 0" text="暂无批改记录"></Empty>
      <template v-else>
        <GradeItem
          v-for="record in recentRecords"
          :key="record.id"
          :item="record"
          :show-actions="true"
          @click="onRecordClick(record)"
          @delete="handleDelete"
          @rename="handleRename"
        />
      </template>
    </view>
  </view>
</template>

<script setup>
import Empty from "@/components/Empty.vue";
import GradeItem from "@/components/grade/GradeItem.vue";

// 接收最近批改记录数据
const props = defineProps({
  recentRecords: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['recordClick', 'viewMore', 'refresh', 'delete', 'rename']);

// 点击记录项
const onRecordClick = (record) => {
  if (!record || !record.id) return;

  // 根据 taskType 决定跳转到哪个页面
  let url = '';
  if (record.taskType === 3) {
    // 作文批改
    url = `/pages/essay-correction/step-3/step-3?taskId=${record.id}`;
  } else if (record.taskType === 1 || record.taskType === 2) {
    // 英语默写批改
    url = `/pages/ai-correction/step-3/step-3?taskId=${record.id}`;
  } else {
    // 默认跳转到英语默写批改
    url = `/pages/ai-correction/step-3/step-3?taskId=${record.id}`;
  }

  uni.navigateTo({
    url: url
  });
};

// 点击查看更多
const onViewMore = () => {
  emit('viewMore');
};

// 处理删除
const handleDelete = (id) => {
  emit('delete', id);
};

// 处理重命名
const handleRename = (data) => {
  emit('rename', data);
};
</script>

<style lang="scss" scoped>
.score-query-card {
  margin: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-top: 12rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  .more {
    font-size: 24rpx;
    color: #666;
    margin-left: auto;
    margin-right: 20rpx;
    display: flex;
    align-items: center;
  }

  .refresh-icon {
    margin-left: 8rpx;
    margin-bottom: -4rpx;
  }
}

.loading {
  padding: 30rpx 0;
}
</style>
