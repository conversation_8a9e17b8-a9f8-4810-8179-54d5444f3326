<template>
  <view class="container">
    <AppHeader :avatar="avatar"></AppHeader>

    <!-- 轮播图 -->
    <banner-image :image-list="bannerList" @banner-click="handleBannerClick" />

    <!-- 服务卡片区域 -->
    <view class="service-grid">
      <service-card
        v-for="service in serviceList"
        :key="service.type"
        :title="service.title"
        :description="service.description"
        :icon-src="service.iconSrc"
        :path="service.path"
        @click="handleServiceClick(service.type)"
      />
    </view>

    <view class="refresh-tip">下拉可刷新最近批改</view>
    <!-- 成绩查询板块 -->
    <score-query-card
        :recent-records="recentScoreRecords"
        :is-loading="isLoading"
        @record-click="handleRecordClick"
        @view-more="handleViewMore"
        @refresh="fetchRecentCorrectionTasks"
        @delete="handleDelete"
        @rename="handleRename"
    />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getNavigationBarInfo } from '@/utils/tools.js'
import BannerImage from './components/BannerImage.vue'
import ServiceCard from './components/ServiceCard.vue'
import AppHeader from "./components/AppHeader.vue";
import ScoreQueryCard from './components/ScoreQueryCard.vue'
import { correctionTaskList } from '@/api/correction-task.js'
import { onPullDownRefresh, onShow, onLoad } from "@dcloudio/uni-app";
import { onShareAppMessage,onShareTimeline } from '@dcloudio/uni-app';
import { deleteTask, updateTask } from '@/api/task.js'
import { validateInviteCode } from '@/api/invitation.js'


onLoad((options) => {
  console.log( '页面加载时的参数:', options)
  acceptInvitation(options)
})


// 接受邀请函数
const acceptInvitation = (options) => {
  let code = ''
  if (options.q) {
    const decodedUrl = decodeURIComponent(options.q);
    const reg = /code=([^&]+)/;
    const match = decodedUrl.match(reg);
    code = match && match[1];
  }

  if(options.code) {
    code = options.code
  }

  // 邀请码
  if(code) {
      // 检查是否已经验证过该邀请码
      const validatedCode = uni.getStorageSync('validatedInviteCode')

      if (validatedCode === code) {
        // 已经验证过，直接提示成功
        uni.showToast({
          title: '邀请码已验证',
          icon: 'success'
        })
        return
      }

      // 提示接受邀请成功，在第一次批改后，双方将获取100次批改次数
      uni.showModal({
        title: '提示',
        content: '接受邀请成功，在您第一次批改后，您和邀请方将各获取100次批改次数',
        showCancel: false,
        success: ({ confirm }) => {
            if(confirm) {
              // 调用接口验证邀请码
              validateInviteCode({
                inviteCode: code
              }).then(res => {
                // 验证成功，将邀请码保存到本地存储
                uni.setStorageSync('validatedInviteCode', code)

                uni.showToast({
                  title: '邀请码验证成功',
                  icon: 'success'
                })
              }).catch(err => {
                console.error('邀请码验证失败:', err)
              })
            }
        }
      })
    }
}


// 轮播图数据
const bannerList = ref([
  {
    url: "https://quick-marker-mini.oss-cn-beijing.aliyuncs.com/static/banner3.webp",
    type: "guidelines",
    path: "/pages/correction-guidelines/index"
  },
])

const avatar = ref('https://quick-marker-oss.research.top/static/default-avatar.png')

// 服务列表数据
const serviceList = ref([
  {
    type: 'ai-correction',
    title: '英语默写批改',
    description: '快速批改英语默写作业',
    iconSrc: '/static/icons/ai-correction.svg',
    path: '/pages/ai-correction/step-1/step-1'
  },
  {
    type: 'essay-correction',
    title: '英语作文批改',
    description: '智能批改，准确打分',
    iconSrc: '/static/icons/essay-correction.svg',
    path: '/pages/essay-correction/step-1/step-1'
  },
  {
    type: 'quick-experience',
    title: '快速体验',
    description: '体验AI智能批改效果',
    iconSrc: '/static/icons/quick-experience.svg',
    path: '/pages/quick-experience/index',
    needLogin: false
  },
  {
    type: 'class-manage',
    title: '班级管理',
    description: '管理学生与班级信息',
    iconSrc: '/static/icons/class-manage.svg',
    path: '/pages/class-manage/index'
  },
])

// 最近批改记录数据
const recentScoreRecords = ref([])
const isLoading = ref(false)

const getUserAvatar = () => {
  const userInfo  = uni.getStorageSync('userInfo')
  if(userInfo && userInfo.avatar) {
    avatar.value = userInfo.avatar
  }
}

// 获取最近批改记录
const fetchRecentCorrectionTasks = async () => {
  isLoading.value = true
  try {
    const params = {
      pageSize: 4,
      pageNo: 1,
    }

    const res = await correctionTaskList(params)
    recentScoreRecords.value = res.data.list
  } catch (error) {
    console.error('获取最近批改记录出错:', error)
  } finally {
    getUserAvatar()
    isLoading.value = false
    uni.stopPullDownRefresh()
  }
}

onPullDownRefresh(() => {
  fetchRecentCorrectionTasks()
})

// 计算顶部安全区域高度
const { statusBarHeight, titleBarHeight } = getNavigationBarInfo()
const headerHeight = computed(() => statusBarHeight + titleBarHeight)

// 处理服务卡片点击
const handleServiceClick = (serviceType) => {
  console.log('服务点击:', serviceType)
  uni.navigateTo({
    url: serviceList.value.find(item => item.type === serviceType).path
  })
}

// 处理记录点击
const handleRecordClick = (record) => {
  console.log('记录点击:', record)
}

// 处理查看更多
const handleViewMore = () => {
  console.log('查看更多批改记录')
  // 到成绩tab
  uni.switchTab({
    url: '/pages/grade-query/index'
  })
}

// 处理轮播图点击
const handleBannerClick = (banner) => {
  if (banner.type === 'guidelines' && banner.path) {
    uni.navigateTo({
      url: banner.path
    })
  }
}

onShow(() => {
  getUserAvatar()
  fetchRecentCorrectionTasks()
})

// 处理删除
const handleDelete = async (id) => {
  try {
    uni.showModal({
      title: '提示',
      content: '确定要删除这条记录吗？',
      success: async (res) => {
        if (res.confirm) {
          await deleteTask(id)
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          // 重新获取列表
          fetchRecentCorrectionTasks()
        }
      }
    })
  } catch (error) {
    uni.showToast({
      title: '删除失败',
      icon: 'error'
    })
  }
}

// 处理重命名
const handleRename = async (data) => {
  try {
    uni.showModal({
      title: '重命名',
      editable: true,
      placeholderText: '请输入新名称',
      content: data.title,
      success: async (res) => {
        if (res.confirm && res.content.trim()) {
          await updateTask({
            id: data.id,
            classId: data.classId,
            teacherId: data.teacherId,
            title: res.content.trim()
          })
          uni.showToast({
            title: '重命名成功',
            icon: 'success'
          })
          // 重新获取列表
          fetchRecentCorrectionTasks()
        }
      }
    })
  } catch (error) {
    uni.showToast({
      title: '重命名失败',
      icon: 'error'
    })
  }
}

// 分享配置
onShareAppMessage(() => {
  return {
    title: '英语批改助手 - 快速批改英文默写作业',
    path: '/pages/index/index',
    imageUrl: 'https://quick-marker-mini.oss-cn-beijing.aliyuncs.com/static/banner1.webp'
  }
})
onShareTimeline(() => {
  return {
    title: '英语批改助手 - 快速批改英文默写作业',
    query: '',
    imageUrl: 'https://quick-marker-mini.oss-cn-beijing.aliyuncs.com/static/banner1.webp'
  }
})
</script>

<style>
page{
  background-color: #f5f7fa;
  padding-bottom: 20rpx;
}
</style>

<style lang="scss" scoped>
.container {
  padding-top: v-bind('headerHeight + "px"');
  box-sizing: border-box;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 20rpx;
}

.refresh-tip {
  font-size: 24rpx;
  color: #b1b1b1;
  text-align: center;
  margin-top: 12rpx;
}
</style>
