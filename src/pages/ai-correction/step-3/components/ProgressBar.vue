<template>
  <!-- 批改进度卡片 -->
  <view class="progress-card">
    <view class="card-header">
      <text class="card-title">批改进度</text>
      <view class="action-button" @tap="handleCheckUnsubmitted" v-if="totalStudents > submittedCount">
        <text class="button-text">未提交或未识别学生（{{ studentsWithoutTaskItemsCount }}人）</text>
      </view>
    </view>
    
    <view class="card-content">
      <!-- 数据统计区域 -->
      <view class="stats-container">
        <view class="stat-item">
          <text class="stat-value">{{ correctedCount }}</text>
          <text class="stat-label">已批改</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ submittedCount }}</text>
          <text class="stat-label">已上传</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ totalStudents }}</text>
          <text class="stat-label">班级人数</text>
        </view>
      </view>
      
      <!-- 进度条区域 -->
      <view class="progress-track-wrapper">
        <view class="progress-track">
          <view class="progress-indicator" :style="{ width: progressPercentage + '%' }"></view>
        </view>
        <text class="progress-percentage">{{ progressPercentage.toFixed(0) }}%</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  studentsWithoutTaskItemsCount: {
    type: Number,
    default: 0
  },
  correctedCount: {
    type: Number,
    default: 0
  },
  submittedCount: {
    type: Number,
    default: 0
  },
  totalStudents: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['checkUnsubmitted']);

const handleCheckUnsubmitted = () => {
  emit('checkUnsubmitted');
};

// 计算进度百分比
const progressPercentage = computed(() => {
  if (props.submittedCount === 0) return 0;
  return (props.correctedCount / props.submittedCount) * 100;
});
</script>

<style lang="scss" scoped>
.progress-card {
  margin: 20rpx 0;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  
  .card-header {
    padding: 20rpx 40rpx;
    border-bottom: 2rpx solid #f5f5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
    }
    
    .action-button {
      padding: 8rpx 20rpx;
      background: rgba(66, 133, 244, 0.1);
      border-radius: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 48rpx;
      
      .button-text {
        font-size: 24rpx;
        color: #4285F4;
        line-height: 1;
      }
    }
  }
  
  .card-content {
    padding: 20rpx 40rpx;
  }
  
  .stats-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    
    .stat-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .stat-value {
        font-size: 36rpx;
        font-weight: 600;
        color: #333333;
        margin-bottom: 4rpx;
      }
      
      .stat-label {
        font-size: 22rpx;
        color: #888888;
      }
    }
    
    .stat-divider {
      width: 2rpx;
      height: 50rpx;
      background-color: #eeeeee;
    }
  }
  
  .progress-track-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 6rpx;
    
    .progress-track {
      flex: 1;
      height: 14rpx;
      background-color: #E0E0E0;
      border-radius: 7rpx;
      overflow: hidden;
      margin-right: 16rpx;
      
      .progress-indicator {
        height: 100%;
        background-color: #4285F4;
        border-radius: 7rpx;
        transition: width 0.3s ease;
      }
    }
    
    .progress-percentage {
      font-size: 26rpx;
      color: #4285F4;
      text-align: right;
    }
  }
}
</style>
