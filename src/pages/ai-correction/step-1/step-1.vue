<template>
  <view class="ai-correction-container">
    <loading-mask :visible="isLoading" text="请稍候..."></loading-mask>

    <!-- 步骤指示器  -->
    <Stepper :active-step="1"/>

    <!-- 主体内容区 -->
    <view class="content">
      <view v-if="classList.length > 0">
        <ClassSelector
          :class-list="classList"
          :class-index="classIndex"
          @class-change="handleClassChange"
        />
      </view>
      <view v-else class="no-class-tip">
        <view class="tip-text">您还没有创建班级，请先创建班级</view>
        <button class="create-class-btn" @click="goToCreateClass">创建班级</button>
      </view>

      <!-- 上传区域组件 -->
      <UploadSection @upload="handleUpload"/>

      <!-- 提取结果显示区 -->
      <ExtractionResult
        v-if="isParsingAnswer || result.length > 0"
        :display-mode="displayMode"
        :result="result"
        :is-loading="isParsingAnswer"
        :is-result-matched="isResultMatched"
        :detected-task-type="detectedTaskType"
        :selected-task-type="selectedTaskType"
        @update:result="handleResultUpdate"
      />

      <!-- 图片预览区域 (优化版) -->
      <view class="preview-section" v-if="createTaskData.correctionImage">
        <view class="preview-header">
          <text class="preview-title">参考答案图片</text>
          <view class="preview-actions">
            <button class="action-btn view-btn" @click="previewImage">查看大图</button>
          </view>
        </view>
        <view class="image-container" @click="previewImage">
          <image
            class="preview-image"
            :src="createTaskData.correctionImage"
            mode="aspectFit"
          />
        </view>
      </view>
    </view>

    <!-- 底部按钮组件 -->
    <FooterActions :disabled="classList.length === 0 || isParsingAnswer || result.length === 0" @next="goToNextStep"/>

    <!-- 创建任务确认 modal -->
    <CreateTaskModal
      v-model:visible="showCreateModal"
      :class-name="currentClassName"
      :default-title="createTaskData.title"
      @confirm="handleCreateConfirm"
      @cancel="showCreateModal = false"
    />
  </view>
</template>

<script setup>
import {ref, computed, reactive} from 'vue';
import Stepper from '@/components/Stepper.vue';
import ExtractionResult from './components/ExtractionResult.vue';
import UploadSection from './components/UploadSection.vue';
import ClassSelector from './components/ClassSelector.vue';
import FooterActions from './components/FooterActions.vue';
import {getClassList} from '@/api/class';
import {createTask, parseAnswer} from '@/api/task';
import {getStudentCountByClass} from '@/api/student';
import {choseAndUploadImage} from "@/libs/utils";
import LoadingMask from '@/components/LoadingMask.vue';
import {getCurrentTeacher} from "@/api/teacher";
import CreateTaskModal from './components/CreateTaskModal.vue';
import {onShow} from "@dcloudio/uni-app";

// 状态管理
const displayMode = ref('');
const result = ref([]);
const showCreateModal = ref(false);
const isParsingAnswer = ref(false);
const isResultMatched = ref(true); // 结果是否与题型匹配
const detectedTaskType = ref(0); // 检测到的题型
const selectedTaskType = ref(0); // 用户选择的题型

// 班级列表
const isLoading = ref(true);
const classList = ref([]);
const classIndex = ref(0);
const classData = ref([]); // 存储完整的班级数据

// 计算当前选中的班级名称
const currentClassName = computed(() => {
  return classData.value?.[classIndex.value]?.className || '';
});

// 任务创建数据
const createTaskData = reactive({
  title: '',
  teacherId: '',
  classId: 0,
  correctionImage: '',
  correctionType: '',
  taskType: 1
});


// 检查班级是否有学生并处理
const checkClassStudents = async (classId, className) => {
  try {
    const res = await getStudentCountByClass(classId);
    const studentCount = res.data || 0;

    // 更新文件数量限制
    if (studentCount > 0) {
      return {hasStudents: true, count: studentCount};
    }

    // 提示添加学生
    uni.showModal({
      title: '提示',
      content: `班级 "${className}" 暂无学生，请先添加学生后再创建任务`,
      confirmText: '去添加',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: `/pages/student-list/index?id=${classId}&name=${className}`
          });
        }
      }
    });

    return {hasStudents: false, count: 0};
  } catch (error) {
    console.error('获取班级学生数量失败:', error);
    uni.showToast({
      title: '获取班级学生数量失败',
      icon: 'none'
    });
    return {hasStudents: false, count: 0, error};
  }
};

// 获取班级列表
const fetchClassList = async () => {
  try {
    const res = await getClassList({pageSize: 100, pageNo: 1});
    if (res.code === 0 && res.data && res.data.list) {
      // 保存完整的班级数据
      classData.value = res.data.list;
      // 提取班级名称作为显示列表
      classList.value = res.data.list.map(item => item.className);

      if (createTaskData.classId) {
        return
      }

      // 默认选中第一个班级
      if (classList.value.length > 0) {
        classIndex.value = 0;
        const defaultClass = classData.value[0];
        createTaskData.classId = defaultClass.id;

        // 检查默认班级学生情况
        await checkClassStudents(defaultClass.id, defaultClass.className);
      } else {
        // 如果没有班级，显示确认框
        showCreateClassConfirm();
      }
    }
  } catch (error) {
    console.error('获取班级列表失败', error);
    uni.showToast({
      title: '获取班级列表失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false;
  }
};

// 显示创建班级确认框
const showCreateClassConfirm = () => {
  uni.showModal({
    title: '提示',
    content: '您还没有创建班级，是否立即创建？',
    confirmText: '立即创建',
    cancelText: '稍后再说',
    success: (res) => {
      if (res.confirm) {
        goToCreateClass();
      }
    }
  });
};

// 处理上传
const handleUpload = async (type) => {
  try {
    const res = await choseAndUploadImage();
    const url = res.data.ossUrl;

    // 将图片URL保存到createTaskData中
    if (url) {
      createTaskData.correctionImage = url;

      // 根据类型设置任务类型
      if (type) {
        // 仅答案类型为1，问答对照类型为2
        createTaskData.taskType = type;
        displayMode.value = type === 1 ? 'answer_only' : 'answer_with_questions';
        createTaskData.correctionType = displayMode.value;
      }

      // 调用parseAnswer接口解析图片内容
      try {
        // 设置解析中状态
        isParsingAnswer.value = true;

        const parseRes = await parseAnswer({
          taskImage: url,
          taskType: type
        });

        // 解析结果
        const parsedResult = JSON.parse(parseRes.data.questions);

        // 检测结果的实际类型
        const actualType = detectResultType(parsedResult);

        // 检查解析结果是否与用户选择的taskType一致
        const isValidResult = actualType === type;

        // 更新提取结果
        result.value = parsedResult;

        // 设置结果匹配状态
        isResultMatched.value = isValidResult;
        // 设置检测到的类型和用户选择的类型
        detectedTaskType.value = actualType;
        selectedTaskType.value = type;

        // 如果不匹配，显示提示
        if (!isValidResult) {
          uni.showToast({
            title: '解析结果与所选题型不匹配',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (parseError) {
        console.error('解析图片内容失败', parseError);
        uni.showModal({
          title: '提示',
          content: '解析图片内容失败，请检查上传的图片是否符合要求',
          showCancel: false,
          confirmText: '确定'
        });
        // 清空结果
        result.value = [];
      } finally {
        // 解析完成，关闭加载状态
        isParsingAnswer.value = false;
      }
    }
  } catch (error) {
    // 检查是否为用户取消操作
    if (error.message && (error.message.includes('cancel') || error.message.includes('canceled'))) {
      // 用户取消操作，不显示错误提示
      console.log('用户取消了上传操作');
    } else {
      // 其他错误，显示错误提示
      console.error('上传图片失败', error);
      uni.showModal({
        title: '上传失败',
        content: error.message,
        showCancel: false
      });
    }
  } finally {
    isLoading.value = false;
  }
};

// 班级选择
const handleClassChange = async (value) => {
  classIndex.value = value;
  // 更新createTaskData中的classId
  if (classData.value[value]) {
    const selectedClass = classData.value[value];
    createTaskData.classId = selectedClass.id;

    // 检查班级学生情况
    await checkClassStudents(selectedClass.id, selectedClass.className);
  }
};

// 前往创建班级页面
const goToCreateClass = () => {
  uni.navigateTo({
    url: '/pages/class-manage/index'
  });
};

// 生成任务标题
const generateTaskTitle = (className) => {
  const now = new Date();
  const dateStr = `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日`;
  const timeStr = `${now.getHours()}:${String(now.getMinutes()).padStart(2, '0')}`;
  return `${className} ${dateStr} ${timeStr}`;
};

// 前往下一步
const goToNextStep = async () => {
  // 确保选择了班级
  if (classList.value.length === 0) {
    showCreateClassConfirm();
    return;
  }

  // 确保已上传图片
  if (!createTaskData.correctionImage) {
    uni.showToast({
      title: '请先上传参考答案',
      icon: 'none'
    });
    return;
  }

  // 确保解析完成且有结果
  if (isParsingAnswer.value) {
    uni.showToast({
      title: '正在解析中，请稍候',
      icon: 'none'
    });
    return;
  }

  if (result.value.length === 0) {
    uni.showToast({
      title: '未获取到有效结果，请重新上传',
      icon: 'none'
    });
    return;
  }

  // 获取选中的班级信息
  const selectedClass = classData.value[classIndex.value];

  try {
    // 检查班级是否有学生
    const {hasStudents, count} = await checkClassStudents(selectedClass.id, selectedClass.className);
    if (!hasStudents) {
      return;
    }

    // 生成默认任务标题
    createTaskData.title = generateTaskTitle(selectedClass.className);

    // 显示创建确认 modal
    showCreateModal.value = true;
  } catch (error) {
    console.error('检查班级学生失败', error);
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    });
  }
};

// 处理任务创建确认
const handleCreateConfirm = async (taskTitle) => {
  try {
    uni.showLoading({title: '正在创建任务...'});

    // 获取教师信息
    if (!uni.getStorageSync('userInfo')) {
      await getCurrentTeacher()
    }
    createTaskData.teacherId = uni.getStorageSync('userInfo').id;
    createTaskData.title = taskTitle;
    createTaskData.correctionText = JSON.stringify(result.value);

    // 调用创建任务接口
    const res = await createTask(createTaskData);
    uni.hideLoading();

    // 显示创建成功提示
    uni.showToast({
      title: '创建成功',
      icon: 'success',
      mask: true,
      duration: 1500
    });

    // 延迟1.5秒后自动跳转
    setTimeout(() => {
      uni.redirectTo({
        url: `/pages/ai-correction/step-3/step-3?taskId=${res.data}`
      });
    }, 1000);
  } catch (error) {
    uni.hideLoading();
    console.error('创建任务失败', error);
    uni.showToast({
      title: '创建任务失败',
      icon: 'none'
    });
  }
};

// 预览图片
const previewImage = () => {
  uni.previewImage({
    urls: [createTaskData.correctionImage],
    current: 0
  });
};

// 页面加载时获取班级列表
onShow(() => {
  fetchClassList();
});

// 检测结果的实际类型
const detectResultType = (result) => {
  // 如果没有结果，返回0
  if (!result || !Array.isArray(result) || result.length === 0) {
    return 0;
  }

  // 检查是否所有项都有答案但没有问题（或问题为空）
  const isAnswerOnly = result.every(item => {
    return item.hasOwnProperty('answer') &&
      (!item.hasOwnProperty('question') || item.question === '');
  });

  if (isAnswerOnly) {
    return 1; // 仅答案模式
  }

  // 检查是否所有项都同时有问题和答案
  const isQuestionAnswer = result.every(item => {
    return item.hasOwnProperty('question') &&
      item.hasOwnProperty('answer') &&
      item.question !== '';
  });

  if (isQuestionAnswer) {
    return 2; // 问答对照模式
  }

  return 0; // 未知类型
};

// 验证解析结果是否与所选题型匹配
const validateResultWithTaskType = (result, taskType) => {
  const detectedType = detectResultType(result);
  return detectedType === taskType;
};

// 处理结果更新
const handleResultUpdate = (updatedResult) => {
  console.log(updatedResult);
  // 更新结果数组
  result.value = updatedResult;
};
</script>

<style lang="scss" scoped>
.ai-correction-container {
  padding-top: 88rpx; // 为固定的步骤指示器留出空间
}

// 主体内容区
.content {
  flex: 1;
  padding: 32rpx;
  margin-top: 60rpx; // 为步骤指示器留出空间
  padding-bottom: 200rpx; // 为固定的footer留出空间
}

// 没有班级时的提示样式
.no-class-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  margin: 20rpx 0;

  .tip-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
  }

  .create-class-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #007aff;
    color: #fff;
    font-size: 28rpx;
    border-radius: 40rpx;
  }
}

// 预览区域样式 - 优化版，提升图片显示效果
.preview-section {
  margin-top: 20rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .preview-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }

    .preview-actions {
      display: flex;
      gap: 12rpx;

      .action-btn {
        font-size: 24rpx;
        color: #666;
        background-color: #f0f0f0;
        border: none;
        padding: 6rpx 16rpx;
        border-radius: 20rpx;
        line-height: 1.5;

        &.view-btn {
          background-color: #e6f7ff;
          color: #1890ff;
        }

        &::after {
          display: none;
        }
      }
    }
  }

  .image-container {
    height: 320rpx;
    overflow: hidden;
    border-radius: 12rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f9f9f9;
    border: 1px dashed #e0e0e0;
    position: relative;
    cursor: pointer;
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 12rpx;
  }
}
</style>
