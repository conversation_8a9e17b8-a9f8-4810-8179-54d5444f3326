import { extractQuestion } from "@/llm/qwen";
import { choseAndUploadImage } from "@/libs/utils";
import { checkResult } from "../extract-paper";

/**
 * 上传并处理图片
 * @returns {Promise<{result: Array, displayMode: string, imageUrl: string}>}
 */
export async function uploadAndProcessImage() {
  try {
    // 选择并上传图片
    const res = await choseAndUploadImage();
    console.log('res222', res);
    const url = res.data.ossUrl;

    // 显示加载提示
    uni.showLoading({
      title: '试卷正在提取...'
    });

    // 提取问题
    const result = await extractQuestion(url);

    // 检查结果
    if (!checkResult(result)) {
      throw new Error('提取结果验证失败');
    }

    // 确定显示模式
    let displayMode = '';
    let processedResult = result;

    const firstItem = result[0];
    if (firstItem.question && firstItem.answer) {
      // 在页面两列显示问题和答案
      displayMode = 'two-column';
    } else if (firstItem.question || firstItem.answer) {
      // 在页面一列显示，认为value就是答案
      displayMode = 'single-column';
      processedResult = result.map((item) => {
        return {
          question: '',
          answer: item.question || item.answer
        };
      });
    }

    // 隐藏加载提示
    uni.hideLoading();

    // 显示提示
    uni.showToast({
      title: '请检查提取结果无误后再进行下一步',
      icon: 'none'
    });

    return {
      result: processedResult,
      displayMode,
      imageUrl: url // 返回图片URL
    };
  } catch (err) {
    // 隐藏加载提示
    uni.hideLoading();
    console.error('Error in uploadAndProcessImage:', err);

    return {
      result: [],
      displayMode: '',
      imageUrl: '' // 返回空的图片URL
    };
  }
}
