<template>
  <view class="example-page">
    <view class="content">
      <view class="image-container">
        <text class="image-title">默写参考答案示例</text>
        <image
          class="example-image"
          :src="exampleImage"
          mode="widthFix"
          @click="previewImage"
          data-index="1"
        ></image>
      </view>

      <view class="section">
        <text class="tips-title">上传提示</text>
        <view class="tips-list">
          <view class="tip-item">
            <text class="dot">•</text>
            <text>请确保图片清晰，文字可辨认</text>
          </view>
          <view class="tip-item">
            <text class="dot">•</text>
            <text>保持光线充足，避免阴影干扰</text>
          </view>
          <view class="tip-item">
            <text class="dot">•</text>
            <text>尽量保持纸张平整，避免褶皱</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const exampleImage = ref('https://quick-marker-oss.research.top/static/%E7%AD%94%E6%A1%88.png');

// 预览图片
const previewImage = (event) => {
  const urls = [exampleImage.value];

  uni.previewImage({
    urls: urls,
  });
};
</script>

<style lang="scss" scoped>
.example-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.content {
  flex: 1;
  padding: 32rpx;
}

.section {
  margin-bottom: 32rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }

  .description {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 24rpx;
    display: block;
  }
}

.image-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

  .image-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }

  .example-image {
    width: 100%;
    border-radius: 8rpx;
  }
}

.tips-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.tips-list {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

  .tip-item {
    display: flex;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .dot {
      color: $uni-primary-color;
      margin-right: 12rpx;
      font-size: 32rpx;
    }

    text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
    }
  }
}
</style>
