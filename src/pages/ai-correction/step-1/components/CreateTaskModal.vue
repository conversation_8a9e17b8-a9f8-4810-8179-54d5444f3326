<template>
  <view class="modal" v-if="visible">
    <view class="mask" @click="handleCancel"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="title">创建批改任务</text>
      </view>
      <view class="modal-body">
        <view class="form-item" style="display: flex;">
          <text class="label">班级</text>
          <text class="value" style="margin-left: 20rpx;">{{ className }}</text>
        </view>
        <view class="form-item">
          <text class="label">任务名称</text>
          <view class="input-wrapper">
            <input
              class="input"
              v-model="taskTitle"
              placeholder="请输入任务名称"
              type="text"
            />
            <text v-if="taskTitle" class="clear-icon" @click.stop="taskTitle = ''">×</text>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn cancel" @click="handleCancel">取消</button>
        <button class="btn confirm" @click="handleConfirm">确定</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  className: {
    type: String,
    default: ''
  },
  defaultTitle: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

const taskTitle = ref('');

// 监听 defaultTitle 的变化
watch(() => props.defaultTitle, (newVal) => {
  taskTitle.value = newVal;
}, { immediate: true });

const handleConfirm = () => {
  if (!taskTitle.value.trim()) {
    uni.showToast({
      title: '请输入任务名称',
      icon: 'none'
    });
    return;
  }
  emit('confirm', taskTitle.value);
  emit('update:visible', false);
};

const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};
</script>

<style lang="scss" scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
  }

  .modal-content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 600rpx;
    box-sizing: border-box;
    background: #fff;
    border-radius: 20rpx;
    overflow: hidden;
  }

  .modal-header {
    padding: 30rpx;
    text-align: center;
    border-bottom: 1rpx solid #eee;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .modal-body {
    padding: 30rpx;

    .form-item {
      margin-bottom: 20rpx;

      .label {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 10rpx;
        display: block;
      }

      .value {
        font-size: 30rpx;
        color: #333;
      }

      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;

        .input {
          flex: 1;
          height: 80rpx;
          border: 1rpx solid #ddd;
          border-radius: 8rpx;
          padding: 0 60rpx 0 20rpx;
          font-size: 28rpx;
          margin-top: 10rpx;
        }

        .clear-icon {
          position: absolute;
          right: 20rpx;
          top: 50%;
          transform: translateY(-40%);
          width: 32rpx;
          height: 32rpx;
          line-height: 32rpx;
          text-align: center;
          color: #fff;
          font-size: 24rpx;
          z-index: 1;
          background-color: #ccc;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          &:active {
            background-color: #999;
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    border-top: 1rpx solid #eee;

    .btn {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      font-size: 30rpx;
      background: none;
      border-radius: 0;

      &::after {
        display: none;
      }

      &.cancel {
        color: #666;
        border-right: 1rpx solid #eee;
      }

      &.confirm {
        color: #007AFF;
        font-weight: bold;
      }
    }
  }
}
</style>
