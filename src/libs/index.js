/**
 * 获取微信授权码
 * @returns {Promise<string>} 返回一个Promise，包含从uni.login中获取的授权码（code）。
 * @throws {any} 如果获取授权码失败，则会抛出错误。
 */
export function getAuthCode() {
  return new Promise((resolve, reject) => {
    uni.getProvider({
      service: 'oauth',
      success: ({ provider }) => {
        uni.login({
          provider,
          success(res) {
            resolve(res.code)
          },
          fail(err) {
            reject(err)
          },
        })
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

/**
 * 从URL中获取查询参数，并返回一个包含查询参数的对象。
 * @param {string} url - URL字符串。
 * @returns {Object} 包含查询参数的对象。
 */
export function getQueryObject(url) {
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}
