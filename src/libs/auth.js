export function getToken() {
  return uni.getStorageSync('token')
}

export function setLoginCache(data) {
  const { accessToken, refreshToken, expiresTime, openid } = data
  uni.setStorageSync('token', accessToken)
  uni.setStorageSync('openid', openid)
  uni.setStorageSync('refreshToken', refreshToken)
  uni.setStorageSync('expiresTime', expiresTime)
}

export function hasToken() {
    return !!getToken()
}

export function clearAllCache() {
  uni.clearStorageSync()
}

export function setUserInfoCache(data) {
  uni.setStorageSync('userInfo', data)
}

export function getUserInfoCache() {
  return uni.getStorageSync('userInfo')
}

export function isTokenExpired() {
  const expireTime = uni.getStorageSync('expiresTime')
  if (!expireTime) return true
  return Date.now() >= expireTime
}
