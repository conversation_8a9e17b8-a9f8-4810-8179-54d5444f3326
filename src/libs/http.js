import HttpRequest from 'luch-request'

import config from '@/config'
import storage from './storage'
import { hasToken, isTokenExpired, setLoginCache } from "@/libs/auth";
import { wxMiniAppLoginSimple } from "@/api/auth";
import { getCurrentTeacher } from "@/api/teacher";


const checkNeedLogin = function () {
  const storageEnv = uni.getStorageSync('env')
    // 检查token是否存在
    if (!hasToken()) {
      console.log('token不存在，需要登录')
      storage.clear()
      return true
    }

  if (!uni.getStorageSync('openid')) {
    console.log('openid不存在，需要登录')
    storage.clear()
    return true
  }

    // 检查环境是否发生变化
    if (storageEnv && config.env !== storageEnv) {
      console.log('环境发生变化，需要重新登录')
      storage.clear()
      return true
    }
    // 更新环境信息
    uni.setStorageSync('env', config.env)

    // 检查token是否过期
    return isTokenExpired()
}

let lock = null

const http = new HttpRequest({
  timeout: 4 * 60 * 1000, // 请求超时时间，4分钟
})

http.interceptors.request.use(async (request) => {
  const isNeedLogin = checkNeedLogin()
  if (isNeedLogin && request.custom.ignore !== true) {
    // 生成锁
    if (!lock) {
      lock = wxMiniAppLoginSimple()
    }
    try {
      const { data, code, msg } = await lock
      if (code === 0) {
        setLoginCache(data)
        // 每次并发完成清除Promise
        lock = null
      } else {
        storage.clear()
        return Promise.reject(new Error(msg || '登录出错'))
      }
    } catch (e) {
      return Promise.reject(e)
    }
  }
  const token = storage.get('token')

  // 如果有token但没有用户信息，获取用户信息
  if(token && !storage.get('userInfo') && !request.custom.isUserInfo) {
    await getCurrentTeacher(token)
  }

  request.header.Authorization = token
  request.header['tenant-id'] = '162'
  // 获取小程序环境，添加header envVersion
  request.header['envVersion'] = uni.getAccountInfoSync().miniProgram.envVersion

  request.baseURL = config.baseURL
  return request
})

http.interceptors.response.use((response) => {
  const apiRes = response.data
  // 如果响应数据错误有额外的逻辑，而不只是仅仅弹出错误，请将 needProcessError 设置为 ture
  if (response.config.custom.needProcessError) {
    return apiRes
  }

  if (response.config.custom.isBlob) {
    return apiRes
  }

  if (apiRes.code === 0 || apiRes.code === 200) {
    return apiRes
  }

  if (apiRes.code === 401) {
    storage.clear()
    // 显示登录过期提示
    uni.showModal({
      title: '提示',
      content: '您的登录已过期，点击确认重新登录',
      showCancel: false,
      success: () => {
        // 重启小程序
        uni.reLaunch({
          url: '/pages/index/index'
        })
      }
    })
    return apiRes
  }

  const errMessage = apiRes.message || apiRes.msg || '系统繁忙，请稍后重试'

  uni.showModal({
    content: errMessage,
    showCancel: false,
    title: '提示',
  })

  const err = new Error(errMessage)
  console.log('err', err)
  return Promise.reject(err)
}, (err) => {
  console.error('err', err)
  uni.showModal({
    content: err.message || err.msg || '系统繁忙，请稍后重试',
    showCancel: false,
    title: '提示',
  })
  return Promise.reject(err)
})

const request = http.request.bind(http)
export default request
