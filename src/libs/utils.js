import { generateOssSignature } from "@/api/upload";

// OSS 凭证缓存
let cachedCredentials = null;

/**
 * 解析 OSS 时间字符串
 * @param {string} dateString - 时间字符串，如 "20250617T044241Z"
 * @returns {Date|null} - 解析后的 Date 对象，失败时返回 null
 */
function parseISODate(dateString) {
  try {
    // 尝试直接解析标准 ISO 格式
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date;
    }

    // 解析紧凑格式: 20250617T044241Z
    const compactRegex = /^(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z?$/;
    const compactMatch = dateString.match(compactRegex);

    if (compactMatch) {
      const [, year, month, day, hour, minute, second] = compactMatch;
      // 注意：月份需要减1，因为 Date 构造函数中月份是从0开始的
      return new Date(Date.UTC(
        parseInt(year, 10),
        parseInt(month, 10) - 1,
        parseInt(day, 10),
        parseInt(hour, 10),
        parseInt(minute, 10),
        parseInt(second, 10)
      ));
    }

    // 解析标准 ISO 格式: 2025-06-17T04:28:06Z
    const standardRegex = /^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})Z?$/;
    const standardMatch = dateString.match(standardRegex);

    if (standardMatch) {
      const [, year, month, day, hour, minute, second] = standardMatch;
      return new Date(Date.UTC(
        parseInt(year, 10),
        parseInt(month, 10) - 1,
        parseInt(day, 10),
        parseInt(hour, 10),
        parseInt(minute, 10),
        parseInt(second, 10)
      ));
    }

    return null;
  } catch (error) {
    console.error('解析日期字符串失败:', error);
    return null;
  }
}

/**
 * 判断 OSS 凭证是否过期
 * @returns {boolean} - 是否过期
 */
function isCredentialsExpired() {
  if (!cachedCredentials || !cachedCredentials.x_oss_date) {
    return true;
  }

  // 解析 x_oss_date 为 Date 对象
  // x_oss_date 格式: 20250617T044241Z
  const expireTime = parseISODate(cachedCredentials.x_oss_date);

  if (!expireTime) {
    console.error('x_oss_date 解析失败:', cachedCredentials.x_oss_date);
    return true;
  }

  const now = new Date();

  // 如果有效期不足一分钟，视为过期（添加缓冲时间）
  return expireTime.getTime() - now.getTime() <= 60000;
}

/**
 * 获取缓存的或新的 OSS 签名
 * @returns {Promise<Object>} - OSS 签名对象
 */
async function getCachedOrFreshSignature() {
  // 检查缓存的凭证是否有效
  if (cachedCredentials && !isCredentialsExpired()) {
    console.log('使用缓存的 OSS 凭证，有效期至:', cachedCredentials.x_oss_date);
    return cachedCredentials;
  }

  console.log('获取新的 OSS 凭证');
  try {
    const signatureRes = await generateOssSignature();
    cachedCredentials = signatureRes;
    console.log('新凭证有效期至:', signatureRes.x_oss_date);
    return signatureRes;
  } catch (error) {
    throw new Error('获取签名失败');
  }
}

/**
 * 生成 uuid
 */
export function generateUUID() { // Public Domain/MIT
  let d = new Date().getTime()// Timestamp
  let d2 = ((typeof performance !== 'undefined') && performance.now && (performance.now() * 1000)) || 0// Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    let r = Math.random() * 16// random number between 0 and 16
    if (d > 0) { // Use timestamp until depleted
      r = (d + r) % 16 | 0
      d = Math.floor(d / 16)
    } else { // Use microseconds since page-load if supported
      r = (d2 + r) % 16 | 0
      d2 = Math.floor(d2 / 16)
    }
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16)
  })
}

/**
 * 统一的图片上传逻辑
 */
export const uploadImage = async (filePath, directory = 'images', needCompress=true) => {
  const ext = filePath.split('.').pop()
  const path = `${directory}/${generateUUID()}.${ext}`

  const tempFilePath = needCompress? await compressImage(filePath): filePath
  let signatureRes = null
  try {
    signatureRes = await getCachedOrFreshSignature()
  } catch (e) {
    throw new Error('获取签名失败')
  }

  console.log(path, 'path')

  const formData = {
    key: path,  //上传文件名称
    policy: signatureRes.policy,   //表单域
    'x-oss-signature-version': signatureRes.x_oss_signature_version,    //指定签名的版本和算法
    'x-oss-credential': signatureRes.x_oss_credential,   //指明派生密钥的参数集
    'x-oss-date': signatureRes.x_oss_date,   //请求的时间
    'x-oss-signature': signatureRes.signature,   //签名认证描述信息
    'x-oss-security-token': signatureRes.security_token,  //安全令牌
    success_action_status: '201'  //上传成功后响应状态码
  };

  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `https://quick-marker-oss.research.top`,
      filePath: tempFilePath,
      name: 'file',
      formData,
      success: (uploadRes) => {
        try {
          // 使用正则表达式匹配 Location 标签内的内容
          const locationMatch = uploadRes.data.match(/<Location>(.*?)<\/Location>/)
          const location = locationMatch?.[1]

          if (location) {
            resolve({
              data: {
                ossUrl: location,
                filePath: tempFilePath
              }
            })
          } else {
            reject(new Error('上传失败：无法获取文件地址'))
          }
        } catch (e) {
          reject(new Error('解析响应数据失败'))
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '上传失败'))
      },
    })
  })
}

/**
 * 统一的图片上传方法
 */
export const choseAndUploadImage = (options = {}, directory='images') => {
  const {
    count = 1,
    sizeType = ['original'],
    sourceType = ['album', 'camera'],
  } = options

  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count,
      sizeType,
      sourceType,
      success: async (res) => {
        try {
          uni.showLoading({
            title: '上传中...',
            mask: true,
          })

          const result = await uploadImage(res.tempFilePaths[0], directory)
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          uni.hideLoading()
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '选择图片失败'))
      },
    })
  })
}

/**
 * 选择图片并转换为base64
 * @returns {Promise<{tempFilePath: string, base64: string}>}
 */
export const chooseImageToBase64 = () => {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: 1,
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];

        try {
          const fileContent = uni.getFileSystemManager().readFileSync(tempFilePath, 'base64');
          resolve({
            tempFilePath,
            base64: `data:image/jpeg;base64,${fileContent}`
          });
        } catch (error) {
          reject(error);
        }
      },
      fail: reject
    });
  });
};

// byte to mb
export const byteToMb = (byte) => {
  return byte / 1024 / 1024
}

export const compressImage = (filePath) => {
  return new Promise(async (resolve, reject) => {
    // 判断文件大小是否大于 1 m
    const { size } = await getFileInfo(filePath)

    const fileMb = byteToMb(size)
    const limitMb = 2

    console.log('压缩前文件大小：', fileMb)
    if (fileMb < limitMb) {
      console.log('不压缩')
      return resolve(filePath)
    }

    console.log('压缩')

    uni.compressImage({
      src: filePath,
      quality: 40,
      success:async (res) => {
        const compressRes = await getFileInfo(res.tempFilePath)
        console.log('压缩后文件大小：', byteToMb(compressRes.size))
        resolve(res.tempFilePath)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}


export const getFileInfo = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().getFileInfo({
      filePath,
      success: (res) => {
        resolve(res)
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

/**
 * 上传文件到 Coze API
 * @param {string} filePath - 文件路径
 * @returns {Promise<{file_id: string}>} - 返回包含文件ID的对象
 */
export const uploadToCoze = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.showLoading({
      title: '上传中...',
      mask: true
    })

    uni.uploadFile({
      url: 'https://api.coze.cn/v1/files/upload',
      filePath: filePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer pat_ngNFgCvUkoFhiWMu4YCkMfOcBGuyurBsdX0fmxW8rrvOW9vSTtWfVj2Y19PnR5Xh',
        'Content-Type': 'multipart/form-data'
      },
      success: (uploadRes) => {
        try {
          if (uploadRes.statusCode === 200) {
            const result = JSON.parse(uploadRes.data)
            resolve(result)
          } else {
            reject(new Error(`上传失败：状态码 ${uploadRes.statusCode}`))
          }
        } catch (e) {
          reject(new Error('解析响应数据失败: ' + e.message))
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '上传失败'))
      },
      complete: () => {
        uni.hideLoading()
      }
    })
  })
}

/**
 * 选择图片并上传到 Coze API
 * @param {Object} options - 选择图片的选项
 * @returns {Promise<{file_id: string}>} - 返回包含文件ID的对象
 */
export const chooseAndUploadToCoze = (options = {}) => {
  const {
    count = 1,
    sizeType = ['original'],
    sourceType = ['album', 'camera'],
  } = options

  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count,
      sizeType,
      sourceType,
      success: async (res) => {
        try {
          uni.showLoading({
            title: '上传中...',
            mask: true,
          })

          // 可以选择是否需要压缩图片
          const tempFilePath = res.tempFilePaths[0]
          const result = await uploadToCoze(tempFilePath)
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          uni.hideLoading()
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '选择图片失败'))
      },
    })
  })
}

/**
 * 选择文件并上传到 Coze API
 * @param {Object} options - 选择文件的选项
 * @param {string[]} options.extension - 文件扩展名数组，如 ['pdf', 'doc', 'docx']
 * @returns {Promise<{file_id: string}>} - 返回包含文件ID的对象
 */
export const chooseFileAndUploadToCoze = (options = {}) => {
  const {
    count = 1,
    extension = [],
    type = 'file'
  } = options

  return new Promise((resolve, reject) => {
    uni.chooseMessageFile({
      count,
      type,
      extension,
      success: async (res) => {
        try {
          uni.showLoading({
            title: '上传中...',
            mask: true,
          })

          const tempFilePath = res.tempFiles[0].path
          const result = await uploadToCoze(tempFilePath)
          resolve(result)
        } catch (error) {
          reject(error)
        } finally {
          uni.hideLoading()
        }
      },
      fail: (err) => {
        reject(new Error(err.errMsg || '选择文件失败'))
      },
    })
  })
}

/**
 * 检查小程序版本并强制更新
 */
export const checkForUpdates = () => {
  const updateManager = uni.getUpdateManager()

  updateManager.onCheckForUpdate(function (res) {
    // 请求完新版本信息的回调
    console.log(res.hasUpdate)
  })

  updateManager.onUpdateReady(function () {
    uni.showModal({
      title: '更新提示',
      content: '新版本已经准备好，点击确定重启小程序',
      showCancel: false,
      success: function (res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate()
        }
      }
    })
  })

  updateManager.onUpdateFailed(function () {
    // 新版本下载失败
    uni.showModal({
      title: '更新提示',
      content: '新版本下载失败，请检查网络后重试',
      showCancel: false
    });
  })
};

