import request from '@/libs/http'

/**
 * 解析作文内容
 */
export function parseEssay(data) {
  return request({
    url: '/quick-marker/essay/parse-image',
    method: 'post',
    data
  })
}

/**
 * 批改作文
 */
export function correctEssay(data) {
  return request({
    url: '/quick-marker/essay/correct',
    method: 'post',
    data
  })
}

/**
 * 获取作文批改结果
 */
export function getEssayResult(taskId) {
  return request({
    url: `/quick-marker/essay/result/${taskId}`,
    method: 'get'
  })
}
