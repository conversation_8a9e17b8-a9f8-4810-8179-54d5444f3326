import request from '@/libs/http'

/**
 * 获取学生列表
 */
export function getStudentList(params) {
  return request({
    url: '/quick-marker/students/page',
    method: 'GET',
    params
  })
}

/**
 * 获取学生详情
 */
export function getStudentInfo(id) {
  return request({
    url: '/quick-marker/students/get',
    method: 'GET',
    params: { id }
  })
}

/**
 * 创建学生
 */
export function createStudent(data) {
  return request({
    url: '/quick-marker/students/create',
    method: 'POST',
    data
  })
}

/**
 * 更新学生
 */
export function updateStudent(data) {
  return request({
    url: '/quick-marker/students/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除学生
 */
export function deleteStudent(id) {
  return request({
    url: '/quick-marker/students/delete',
    method: 'DELETE',
    params: { id }
  })
}

/**
 * 获取班级学生数量 /quick-marker/students/count-by-class
 */
export function getStudentCountByClass(classId) {
  return request({
    url: '/quick-marker/students/count-by-class',
    method: 'GET',
    params: { classId }
  })
}

/**
 * @typedef {Object} Student
 * @property {number} id - 学生ID
 * @property {string} name - 学生姓名
 * @property {string} studentNumber - 学号
 * @property {number} classId - 班级ID
 * @property {string} createTime - 创建时间
 */

/**
 * @typedef {Object} ApiResponse
 * @property {number} code - 响应状态码
 * @property {Student[]} data - 学生数据列表
 * @property {string} msg - 响应消息
 */

/**
 * 获取未完成任务的学生列表
 * @param {Object} params - 请求参数
 * @param {number} params.classId - 班级ID
 * @param {number} params.taskId - 任务ID 
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} [params.searchKeyword] - 搜索关键词
 * @returns {Promise<ApiResponse>} 返回未完成任务的学生列表
 */
export function getStudentsWithoutTaskItems(params) {
  return request({
    url: '/quick-marker/students/list-without-task-items',
    method: 'GET',
    params,
  })
}

/**
 * 从图片中识别学生
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<ApiResponse>} 返回识别结果
 */
export function recognizeStudentsFromImage(imageUrl) {
  return request({
    url: '/quick-marker/students/recognize-from-image',
    method: 'POST',
    data: { imageUrl }
  })
}

/**
 * 批量创建学生
 * @param {Object} data - 请求数据
 * @param {Array<Object>} data.students - 学生信息数组
 * @param {number} data.classId - 班级ID
 * @returns {Promise<ApiResponse>} 返回创建结果
 */
export function batchCreateStudents(data) {
  return request({
    url: '/quick-marker/students/batch-create',
    method: 'POST',
    data
  })
}

/**
 * 从Excel文件解析学生信息
 * @param {string} excelUrl - Excel文件的URL地址
 * @returns {Promise<ApiResponse>} 返回解析结果
 */
export function parseStudentsFromExcel(excelUrl) {
  return request({
    url: '/quick-marker/students/parse-from-excel',
    method: 'POST',
    data: { excelUrl }
  })
}
