import request from '@/libs/http'

/**
 * 获取作文评分标准表分页
 */
export function getRubricList(params) {
  return request({
    url: '/quick-marker/rubric/page',
    method: 'GET',
    params
  })
}

/**
 * 获得作文评分标准表详情
 */
export function getRubricDetail(id) {
  return request({
    url: '/quick-marker/rubric/get',
    method: 'GET',
    params: { id }
  })
}

/**
 * 创建作文评分标准表
 */
export function createRubric(data) {
  return request({
    url: '/quick-marker/rubric/create',
    method: 'POST',
    data
  })
}

/**
 * 更新作文评分标准表
 */
export function updateRubric(data) {
  return request({
    url: '/quick-marker/rubric/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除作文评分标准表
 */
export function deleteRubric(id) {
  return request({
    url: '/quick-marker/rubric/delete',
    method: 'DELETE',
    params: { id }
  })
}

/**
 * 获得模版评分标准列表
 */
export function getRubricTemplates() {
  return request({
    url: '/quick-marker/rubric/templates',
    method: 'GET'
  })
}

/**
 * AI润色作文批改标准
 */
export function polishRubricWithAI(data) {
  return request({
    url: '/quick-marker/rubric/polish-with-ai',
    method: 'POST',
    data
  })
}

/**
 * 导出作文评分标准表 Excel
 */
export function exportRubricExcel(params) {
  return request({
    url: '/quick-marker/rubric/export-excel',
    method: 'GET',
    params,
    custom: {
      isBlob: true
    }
  })
}
