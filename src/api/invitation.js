import request from '@/libs/http'

/**
 * 获取邀请记录列表
 */
export function getInvitationRecords(params) {
  return request({
    url: '/quick-marker/invitation/records',
    method: 'GET',
    params
  })
}


/**
 * 获取邀请码
 */
export function getInviteCode() {
  return request({
    url: '/quick-marker/invite-qrcode/get-invite-code',
    method: 'GET'
  })
}


/**
 * 验证邀请码
 */
export function validateInviteCode(data) {
  return request({
    url: '/quick-marker/invite-qrcode/validate-invite-code',
    method: 'POST',
    data
  })
}


/**
 * 获取邀请统计信息
 */
export function getInviteStatistics() {
  return request({
    url: '/quick-marker/invite-qrcode/get-invite-statistics',
    method: 'GET'
  })
}
