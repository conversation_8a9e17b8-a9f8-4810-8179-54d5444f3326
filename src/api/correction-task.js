import request from '@/libs/http'


/**
 * 获取批改任务列表
 */
export function correctionTaskList(params) {
  return request({
    url: '/quick-marker/correction-tasks/page',
    method: 'GET',
    params
  })
}

/**
 * 获取批改任务详情
 */
export function getCorrectionTaskById(id) {
  return request({
    url: `/quick-marker/correction-tasks/${id}`,
    method: 'GET'
  })
}

/**
 * 获取学生答题详情
 */
export function getStudentAnswers(correctionTaskId, studentId) {
  return request({
    url: `/quick-marker/correction-tasks/${correctionTaskId}/students/${studentId}/answers`,
    method: 'GET'
  })
}
