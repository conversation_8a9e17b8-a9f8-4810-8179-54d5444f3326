import request from '@/libs/http'

/**
 * 获取批改任务项列表
 */
export function getTaskItemList(params) {
  return request({
    url: '/quick-marker/correction-task-items/page',
    method: 'GET',
    params
  })
}

/**
 * 创建批改任务项
 */
export function createTaskItem(data) {
  return request({
    url: '/quick-marker/correction-task-items/create',
    method: 'POST',
    data,
    custom: {
      needProcessError: true
    },
  })
}

/**
 * 创建批改任务项, 使用 coze 工作流
 */
export function createTaskItemV2(data) {
  return request({
    url: '/quick-marker/correction-task-items/v2/create',
    method: 'POST',
    data,
    custom: {
      needProcessError: true
    },
  })
}

/**
 * 更新批改任务项
 */
export function updateTaskItem(data) {
  return request({
    url: '/quick-marker/correction-task-items/update',
    method: 'PUT',
    data
  })
}

/**
 * 创建批改任务项详情
 */
export function createTaskItemDetail(data) {
  return request({
    url: '/quick-marker/correction-task-item-details/create',
    method: 'POST',
    data
  })
}

/**
 * 更新批改任务项详情
 * @param {Object} data - 更新数据
 * @param {number} data.itemId - 任务项ID
 * @param {number} data.isCorrect - 是否正确(0-错误,1-正确)
 * @param {string} data.errorAnalysis - 错误分析
 * @returns {Promise}
 */
export function updateTaskItemDetail(data) {
  return request({
    url: '/quick-marker/correction-task-item-details/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除批改任务项详情
 */
export function deleteTaskItemDetail(id) {
  return request({
    url: '/quick-marker/correction-task-item-details/delete',
    method: 'DELETE',
    params: { id }
  })
}

/**
 * 删除批改任务项
 */
export function deleteTaskItem(id) {
  return request({
    url: '/quick-marker/correction-task-items/delete',
    method: 'DELETE',
    params: { id }
  })
}


/**
 * 获取批改任务项详情
 */
export function getTaskItemDetail(id) {
  return request({
    url: '/quick-marker/correction-task-items/get',
    method: 'GET',
    params: { id }
  })
}

/**
 * 导出批改任务项Excel
 */
export function exportTaskListExcel(params) {
  return request({
    url: '/quick-marker/correction-task-items/export-excel',
    method: 'GET',
    params,
    custom: {
      isBlob: true
    },
    responseType: 'arraybuffer',
  })
}


/**
 * 导出批改作业详情Excel
 */
export function exportTaskDetailExcel(params) {
  return request({
    url: '/quick-marker/correction-task-item-details/export-excel',
    method: 'GET',
    params,
    custom: {
      isBlob: true
    },
    responseType: 'arraybuffer',
  })
}

/**
 * 重试批改任务项
 * @param {number} id - 任务项ID
 * @returns {Promise}
 */
export function retryTaskItem(id) {
  return request({
    url: '/quick-marker/correction-task-items/retry',
    method: 'POST',
    params: { id },
    custom: {
      needProcessError: true
    },
  })
}

/**
 * 重试批改任务项,  使用 coze 工作流
 * @param {number} id - 任务项ID
 * @returns {Promise}
 */
export function retryTaskItemV2(id) {
  return request({
    url: '/quick-marker/correction-task-items/v2/retry',
    method: 'POST',
    params: { id },
    custom: {
      needProcessError: true
    },
  })
}

/**
 * 获取批改任务项统计数据
 * @param {number} taskId - 任务ID
 * @returns {Promise}
 */
export function getTaskItemStatistics(taskId) {
  return request({
    url: '/quick-marker/correction-task-item-details/statistics',
    method: 'GET',
    params: { taskId, isCorrect: 0 }
  })
}

/**
 * 批量导出批改任务Excel为ZIP文件
 * @param {Object} params - 请求参数
 * @param {number} params.taskId - 任务ID
 * @param {number} params.teacherId - 教师ID
 * @returns {Promise}
 */
export function batchExportTaskExcel(params) {
  return request({
    url: '/quick-marker/correction-task-item-details/batch-export-excel',
    method: 'GET',
    params,
    custom: {
      isBlob: true
    },
    responseType: 'arraybuffer',
  })
}

/**
 * AI文字识别和补全
 * @param {Object} data - 请求数据
 * @param {string} data.prompt - 提示词
 * @param {string} data.content - 现有内容
 * @param {string} data.imageUrl - 图片URL
 * @returns {Promise}
 */
export function aiCompletions(data) {
  return request({
    url: '/quick-marker/correction-task-items/ai-completions',
    method: 'POST',
    data,
    header: {
      'API-Version': 'v2'
    }
  })
}



export function getTaskItemIdList(params) {
  return request({
    url: '/quick-marker/correction-task-items/get-all-ids',
    method: 'GET',
    params,
  })
}
