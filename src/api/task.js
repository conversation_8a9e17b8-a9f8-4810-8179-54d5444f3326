import request from '@/libs/http'

/**
 * 获取批改任务列表
 */
export function getTaskList(params) {
  return request({
    url: '/quick-marker/correction-tasks/page',
    method: 'GET',
    params
  })
}

/**
 * 获取批改任务详情
 */
export function getTaskInfo(id) {
  return request({
    url: '/quick-marker/correction-tasks/get',
    method: 'GET',
    params: { id }
  })
}

/**
 * 创建批改任务
 */
export function createTask(data) {
  return request({
    url: '/quick-marker/correction-tasks/create',
    method: 'POST',
    data
  })
}

/**
 * 更新批改任务
 */
export function updateTask(data) {
  return request({
    url: '/quick-marker/correction-tasks/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除批改任务
 */
export function deleteTask(id) {
  return request({
    url: '/quick-marker/correction-tasks/delete',
    method: 'DELETE',
    params: { id }
  })
}

/**
 * 获取批改任务项列表
 */
export function getTaskItemList(params) {
  return request({
    url: '/quick-marker/correction-task-items/page',
    method: 'GET',
    params
  })
}

/**
 * 获取批改任务项详情列表
 */
export function getTaskItemDetailList(params) {
  return request({
    url: '/quick-marker/correction-task-item-details/page',
    method: 'GET',
    params
  })
}

/**
 * 获取学生答题详情
 */
export function getStudentAnswers(correctionTaskId, studentId) {
  return request({
    url: `/quick-marker/correction-tasks/${correctionTaskId}/students/${studentId}/answers`,
    method: 'GET'
  })
}

/**
 * 解析题目
 */
export function parseAnswer(data) {
  return request({
    url: '/quick-marker/correction-tasks/parse-image',
    method: 'post',
    data
  })
}
