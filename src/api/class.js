import request from '@/libs/http'

/**
 * 获取班级列表
 */
export function getClassList(params) {
  return request({
    url: '/quick-marker/classes/page',
    method: 'GET',
    params
  })
}

/**
 * 获取班级详情
 */
export function getClassInfo(id) {
  return request({
    url: '/quick-marker/classes/get',
    method: 'GET',
    params: { id }
  })
}

/**
 * 创建班级
 */
export function createClass(data) {
  return request({
    url: '/quick-marker/classes/create',
    method: 'POST',
    data
  })
}

/**
 * 更新班级
 */
export function updateClass(data) {
  return request({
    url: '/quick-marker/classes/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除班级
 */
export function deleteClass(id) {
  return request({
    url: '/quick-marker/classes/delete',
    method: 'DELETE',
    params: { id }
  })
} 