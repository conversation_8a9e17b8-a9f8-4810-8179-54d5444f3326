import request from '@/libs/http'

/**
 * 获取订单列表
 */
export function getOrderList(params) {
  return request({
    url: '/quick-marker/orders/page',
    method: 'GET',
    params
  })
}

/**
 * 提交支付订单
 */
export function submitPayOrder(data) {
  return request({
    url: '/pay/order/submit',
    method: 'POST',
    data
  })
}

/**
 * 获取订单详情
 */
export function getOrderInfo(id) {
  return request({
    url: '/quick-marker/orders/get', 
    method: 'GET',
    params: { id }
  })
}

/**
 * 创建订单
 */
export function createOrder(data) {
  return request({
    url: '/quick-marker/orders/create',
    method: 'POST',
    data
  })
}

/**
 * 更新订单
 */
export function updateOrder(data) {
  return request({
    url: '/quick-marker/orders/update',
    method: 'PUT', 
    data
  })
}

/**
 * 删除订单
 */
export function deleteOrder(id) {
  return request({
    url: '/quick-marker/orders/delete',
    method: 'DELETE',
    params: { id }
  })
} 