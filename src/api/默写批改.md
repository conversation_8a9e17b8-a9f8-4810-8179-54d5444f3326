---
title: 默写批改
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.28"

---

# 默写批改

Base URLs:

# Authentication

# 用户 APP - 认证

## POST 使用手机 + 密码登录

POST /member/auth/login

使用手机 + 密码登录

> Body 请求参数

```json
{
  "mobile": "15601691300",
  "password": "buzhidao",
  "socialType": 10,
  "socialCode": "1024",
  "socialState": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppAuthLoginReqVO](#schemaappauthloginreqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "userId": 0,
    "accessToken": "",
    "refreshToken": "",
    "expiresTime": "",
    "openid": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppAuthLoginRespVO](#schemacommonresultappauthloginrespvo)|

## POST 登出系统

POST /member/auth/logout

登出系统

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## POST 刷新令牌

POST /member/auth/refresh-token

刷新令牌

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|refreshToken|query|string| 是 |刷新令牌|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "userId": 0,
    "accessToken": "",
    "refreshToken": "",
    "expiresTime": "",
    "openid": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppAuthLoginRespVO](#schemacommonresultappauthloginrespvo)|

## POST 使用手机 + 验证码登录

POST /member/auth/sms-login

使用手机 + 验证码登录

> Body 请求参数

```json
{
  "mobile": "15601691300",
  "code": "1024",
  "socialType": 10,
  "socialCode": "1024",
  "socialState": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppAuthSmsLoginReqVO](#schemaappauthsmsloginreqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "userId": 0,
    "accessToken": "",
    "refreshToken": "",
    "expiresTime": "",
    "openid": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppAuthLoginRespVO](#schemacommonresultappauthloginrespvo)|

## POST 发送手机验证码

POST /member/auth/send-sms-code

发送手机验证码

> Body 请求参数

```json
{
  "mobile": "15601691234",
  "scene": 1
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppAuthSmsSendReqVO](#schemaappauthsmssendreqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## POST 校验手机验证码

POST /member/auth/validate-sms-code

校验手机验证码

> Body 请求参数

```json
{
  "mobile": "15601691234",
  "scene": 1,
  "code": "1024"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppAuthSmsValidateReqVO](#schemaappauthsmsvalidatereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 社交授权的跳转

GET /member/auth/social-auth-redirect

社交授权的跳转

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|type|query|integer| 是 |社交类型|
|redirectUri|query|string| 否 |回调路径|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": "",
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultString](#schemacommonresultstring)|

## POST 社交快捷登录，使用 code 授权码

POST /member/auth/social-login

社交快捷登录，使用 code 授权码
适合未登录的用户，但是社交账号已绑定用户

> Body 请求参数

```json
{
  "type": 10,
  "code": "1024",
  "state": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppAuthSocialLoginReqVO](#schemaappauthsocialloginreqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "userId": 0,
    "accessToken": "",
    "refreshToken": "",
    "expiresTime": "",
    "openid": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppAuthLoginRespVO](#schemacommonresultappauthloginrespvo)|

## POST 微信小程序的一键登录

POST /member/auth/weixin-mini-app-login

微信小程序的一键登录

> Body 请求参数

```json
{
  "phoneCode": "hello",
  "loginCode": "word",
  "state": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppAuthWeixinMiniAppLoginReqVO](#schemaappauthweixinminiapploginreqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "userId": 0,
    "accessToken": "",
    "refreshToken": "",
    "expiresTime": "",
    "openid": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppAuthLoginRespVO](#schemacommonresultappauthloginrespvo)|

## POST 创建微信 JS SDK 初始化所需的签名

POST /member/auth/create-weixin-jsapi-signature

创建微信 JS SDK 初始化所需的签名
参考 https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html 文档

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|url|query|string| 是 |none|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "appId": "",
    "nonceStr": "",
    "timestamp": 0,
    "url": "",
    "signature": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultSocialWxJsapiSignatureRespDTO](#schemacommonresultsocialwxjsapisignaturerespdto)|

# 用户 App - 文件存储

## POST 上传文件

POST /infra/file/upload

上传文件

> Body 请求参数

```yaml
file: string
path: yudaoyuanma.png

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |文件附件|
|» path|body|string| 否 |文件附件|

> 返回示例

```json
{
  "code": 0,
  "data": "",
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultString](#schemacommonresultstring)|

## GET 获取文件预签名地址

GET /infra/file/presigned-url

获取文件预签名地址
模式二：前端上传文件：用于前端直接上传七牛、阿里云 OSS 等文件存储器

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|path|query|string| 是 |none|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "configId": 0,
    "uploadUrl": "",
    "url": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultFilePresignedUrlRespVO](#schemacommonresultfilepresignedurlrespvo)|

## POST 创建文件

POST /infra/file/create

创建文件
模式二：前端上传文件：配合 presigned-url 接口，记录上传了上传的文件

> Body 请求参数

```json
{
  "configId": 11,
  "path": "yudao.jpg",
  "name": "yudao.jpg",
  "url": "https://www.iocoder.cn/yudao.jpg",
  "type": "application/octet-stream",
  "size": 2048
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[FileCreateReqVO](#schemafilecreatereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

# 用户 APP - 批改任务项详情

## POST 创建批改任务项详情

POST /quick-marker/correction-task-item-details/create

创建批改任务项详情

> Body 请求参数

```json
{
  "id": 5961,
  "itemId": 6269,
  "imageId": 26812,
  "recognizedText": "string",
  "correctText": "string",
  "score": 0,
  "isCorrect": 0,
  "errorAnalysis": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppCorrectionTaskItemDetailsSaveReqVO](#schemaappcorrectiontaskitemdetailssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新批改任务项详情

PUT /quick-marker/correction-task-item-details/update

更新批改任务项详情

> Body 请求参数

```json
{
  "id": 5961,
  "itemId": 6269,
  "imageId": 26812,
  "recognizedText": "string",
  "correctText": "string",
  "score": 0,
  "isCorrect": 0,
  "errorAnalysis": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppCorrectionTaskItemDetailsSaveReqVO](#schemaappcorrectiontaskitemdetailssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## DELETE 删除批改任务项详情

DELETE /quick-marker/correction-task-item-details/delete

删除批改任务项详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得批改任务项详情

GET /quick-marker/correction-task-item-details/get

获得批改任务项详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "itemId": 0,
    "imageId": 0,
    "recognizedText": "",
    "correctText": "",
    "score": 0,
    "isCorrect": 0,
    "errorAnalysis": "",
    "createTime": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppCorrectionTaskItemDetailsRespVO](#schemacommonresultappcorrectiontaskitemdetailsrespvo)|

## GET 获得批改任务项详情分页

GET /quick-marker/correction-task-item-details/page

获得批改任务项详情分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|itemId|query|integer(int64)| 否 |关联到哪份作业，quick_marker_correction_task_items.id|
|imageId|query|integer(int64)| 否 |若可溯源到具体哪张图片，可逻辑存放；可不再使用|
|recognizedText|query|string| 否 |AI 识别到的学生答案|
|correctText|query|string| 否 |标准答案（或比对后的正确答案）|
|score|query|string| 否 |该题得分|
|isCorrect|query|integer| 否 |是否正确(0=错误,1=正确,2=部分正确等)|
|errorAnalysis|query|string| 否 |错误原因或拼写错误提示|
|createTime|query|array[string]| 否 |创建时间|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "itemId": 0,
        "imageId": 0,
        "recognizedText": "",
        "correctText": "",
        "score": 0,
        "isCorrect": 0,
        "errorAnalysis": "",
        "createTime": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultAppCorrectionTaskItemDetailsRespVO](#schemacommonresultpageresultappcorrectiontaskitemdetailsrespvo)|

## GET 导出批改任务项详情 Excel

GET /quick-marker/correction-task-item-details/export-excel

导出批改任务项详情 Excel

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|itemId|query|integer(int64)| 否 |关联到哪份作业，quick_marker_correction_task_items.id|
|imageId|query|integer(int64)| 否 |若可溯源到具体哪张图片，可逻辑存放；可不再使用|
|recognizedText|query|string| 否 |AI 识别到的学生答案|
|correctText|query|string| 否 |标准答案（或比对后的正确答案）|
|score|query|string| 否 |该题得分|
|isCorrect|query|integer| 否 |是否正确(0=错误,1=正确,2=部分正确等)|
|errorAnalysis|query|string| 否 |错误原因或拼写错误提示|
|createTime|query|array[string]| 否 |创建时间|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

# 用户 APP - 老师

## PUT 更新老师

PUT /quick-marker/teachers/update

更新老师

> Body 请求参数

```json
{
  "id": 13791,
  "name": "芋艿",
  "memberUserId": 14645,
  "email": "string",
  "avatar": "string",
  "phone": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppTeachersSaveReqVO](#schemaappteacherssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获取当前老师信息

GET /quick-marker/teachers/current

获取当前老师信息

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "name": "",
    "memberUserId": 0,
    "email": "",
    "avatar": "",
    "phone": "",
    "totalCorrectionCount": 0,
    "remainCorrectionCount": 0,
    "createTime": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppTeachersRespVO](#schemacommonresultappteachersrespvo)|

# 用户 APP - 学生表

## POST 创建学生表

POST /quick-marker/students/create

创建学生表

> Body 请求参数

```json
{
  "id": 14598,
  "name": "张三",
  "studentNumber": "string",
  "classId": 23831
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppStudentsSaveReqVO](#schemaappstudentssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新学生表

PUT /quick-marker/students/update

更新学生表

> Body 请求参数

```json
{
  "id": 14598,
  "name": "张三",
  "studentNumber": "string",
  "classId": 23831
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppStudentsSaveReqVO](#schemaappstudentssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得学生表

GET /quick-marker/students/get

获得学生表

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "name": "",
    "studentNumber": "",
    "classId": 0,
    "createTime": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppStudentsRespVO](#schemacommonresultappstudentsrespvo)|

## GET 获得学生表分页

GET /quick-marker/students/page

获得学生表分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|name|query|string| 否 |学生姓名|
|studentNumber|query|string| 否 |学生学号|
|classId|query|integer(int64)| 否 |所属班级 ID，逻辑关联 quick_marker_classes.id|
|createTime|query|array[string]| 否 |创建时间|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "name": "",
        "studentNumber": "",
        "classId": 0,
        "createTime": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultAppStudentsRespVO](#schemacommonresultpageresultappstudentsrespvo)|

# 用户 APP - 批改套餐

## GET 获得批改套餐分页

GET /quick-marker/packages/page

获得批改套餐分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|name|query|string| 否 |套餐名称|
|description|query|string| 否 |套餐描述|
|category|query|string| 否 |套餐类别: experience(体验), standard(标准), advanced(高级), flagship(旗舰)|
|price|query|string| 否 |套餐价格|
|originalPrice|query|string| 否 |原价|
|correctionCount|query|integer| 否 |可批改次数|
|validityDays|query|integer| 否 |有效期(天)，从购买日期算起|
|imageUrl|query|string| 否 |套餐封面图片 URL|
|sortOrder|query|integer| 否 |排序权重(后台管理可调整)|
|status|query|integer| 否 |状态: 1=上架, 0=下架|
|createTime|query|array[string]| 否 |创建时间|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "name": "",
        "description": "",
        "category": "",
        "price": 0,
        "originalPrice": 0,
        "correctionCount": 0,
        "validityDays": 0,
        "imageUrl": "",
        "sortOrder": 0,
        "status": 0,
        "createTime": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultAppPackagesRespVO](#schemacommonresultpageresultapppackagesrespvo)|

# 用户 APP - 批改任务

## POST 创建批改任务

POST /quick-marker/correction-tasks/create

创建批改任务

> Body 请求参数

```json
{
  "id": 3247,
  "title": "string",
  "teacherId": 7599,
  "classId": 16560,
  "correctionImage": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppCorrectionTasksSaveReqVO](#schemaappcorrectiontaskssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## GET 获得批改任务

GET /quick-marker/correction-tasks/get

获得批改任务

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer| 是 |编号|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "id": 0,
    "title": "",
    "teacherId": 0,
    "classId": 0,
    "description": "",
    "status": "",
    "correctionLimit": 0,
    "usedCorrectionCount": 0,
    "averageAccuracy": 0,
    "maxAccuracy": 0,
    "minAccuracy": 0,
    "createTime": "",
    "correctionImage": ""
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultAppCorrectionTasksRespVO](#schemacommonresultappcorrectiontasksrespvo)|

## GET 获得批改任务分页

GET /quick-marker/correction-tasks/page

获得批改任务分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|title|query|string| 否 |任务标题|
|teacherId|query|integer(int64)| 否 |创建该任务的教师 ID，逻辑关联 quick_marker_teachers.id|
|classId|query|integer(int64)| 否 |所属班级 ID（若有班级表，可逻辑关联）|
|description|query|string| 否 |任务描述|
|status|query|string| 否 |任务状态：pending/processing/done等|
|correctionLimit|query|integer| 否 |本次批改可用次数或需要的次数|
|usedCorrectionCount|query|integer| 否 |已消耗的批改次数|
|averageAccuracy|query|string| 否 |平均正确率(0~100)|
|maxAccuracy|query|string| 否 |最高正确率(0~100)|
|minAccuracy|query|string| 否 |最低正确率(0~100)|
|createTime|query|array[string]| 否 |创建时间|
|correctionImage|query|string| 否 |批改任务的图片路径或 URL|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "title": "",
        "teacherId": 0,
        "classId": 0,
        "description": "",
        "status": "",
        "correctionLimit": 0,
        "usedCorrectionCount": 0,
        "averageAccuracy": 0,
        "maxAccuracy": 0,
        "minAccuracy": 0,
        "createTime": "",
        "correctionImage": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultAppCorrectionTasksRespVO](#schemacommonresultpageresultappcorrectiontasksrespvo)|

# 用户 APP - 批改任务项

## POST 创建批改任务项

POST /quick-marker/correction-task-items/create

创建批改任务项

> Body 请求参数

```json
{
  "id": 31241,
  "taskId": 18815,
  "teacherId": 32399,
  "classId": 27316,
  "images": "string",
  "errorAnalysis": "string",
  "improvementSuggestions": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppCorrectionTaskItemsSaveReqVO](#schemaappcorrectiontaskitemssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": 0,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultLong](#schemacommonresultlong)|

## PUT 更新批改任务项

PUT /quick-marker/correction-task-items/update

更新批改任务项

> Body 请求参数

```json
{
  "id": 31241,
  "taskId": 18815,
  "teacherId": 32399,
  "classId": 27316,
  "images": "string",
  "errorAnalysis": "string",
  "improvementSuggestions": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|
|body|body|[AppCorrectionTaskItemsSaveReqVO](#schemaappcorrectiontaskitemssavereqvo)| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": false,
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultBoolean](#schemacommonresultboolean)|

## GET 获得批改任务项分页

GET /quick-marker/correction-task-items/page

获得批改任务项分页

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 是 |页码，从 1 开始|
|pageSize|query|integer| 是 |每页条数，最大值为 100|
|taskId|query|integer(int64)| 否 |所属批改任务 ID，逻辑关联 quick_marker_correction_tasks.id|
|teacherId|query|integer(int64)| 否 |教师 ID（冗余存储）|
|classId|query|integer(int64)| 否 |班级 ID（冗余存储）|
|studentId|query|integer(int64)| 否 |学生 ID（逻辑关联到学生表或其他）|
|status|query|string| 否 |状态：pending/processing/corrected|
|finalScore|query|string| 否 |AI 批改后该作业的总得分|
|images|query|string| 否 |作业图片URL集合（JSON数组或字符串）|
|correctRate|query|string| 否 |正确率(0~100)|
|totalCount|query|integer| 否 |总题数量|
|correctCount|query|integer| 否 |正确题数|
|wrongCount|query|integer| 否 |错误题数|
|errorAnalysis|query|string| 否 |错题分析|
|improvementSuggestions|query|string| 否 |改进意见|
|createTime|query|array[string]| 否 |创建时间|
|tenant-id|header|string| 否 |none|
|authorization|header|string| 否 |none|

> 返回示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 0,
        "taskId": 0,
        "teacherId": 0,
        "classId": 0,
        "studentId": 0,
        "status": "",
        "finalScore": 0,
        "images": "",
        "correctRate": 0,
        "totalCount": 0,
        "correctCount": 0,
        "wrongCount": 0,
        "errorAnalysis": "",
        "improvementSuggestions": "",
        "createTime": ""
      }
    ],
    "total": 0
  },
  "msg": ""
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|[CommonResultPageResultAppCorrectionTaskItemsRespVO](#schemacommonresultpageresultappcorrectiontaskitemsrespvo)|

# 数据模型

<h2 id="tocS_CommonResultString">CommonResultString</h2>

<a id="schemacommonresultstring"></a>
<a id="schema_CommonResultString"></a>
<a id="tocScommonresultstring"></a>
<a id="tocscommonresultstring"></a>

```json
{
  "code": 0,
  "data": "string",
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|string|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AppAuthLoginRespVO">AppAuthLoginRespVO</h2>

<a id="schemaappauthloginrespvo"></a>
<a id="schema_AppAuthLoginRespVO"></a>
<a id="tocSappauthloginrespvo"></a>
<a id="tocsappauthloginrespvo"></a>

```json
{
  "userId": 1024,
  "accessToken": "happy",
  "refreshToken": "nice",
  "expiresTime": "string",
  "openid": "qq768"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|userId|integer(int64)|false|none||用户编号|
|accessToken|string|false|none||访问令牌|
|refreshToken|string|false|none||刷新令牌|
|expiresTime|string|false|none||过期时间|
|openid|string|false|none||仅社交登录、社交绑定时会返回<br /><br />为什么需要返回？微信公众号、微信小程序支付需要传递 openid 给支付接口<br />社交用户 openid|

<h2 id="tocS_CommonResultLong">CommonResultLong</h2>

<a id="schemacommonresultlong"></a>
<a id="schema_CommonResultLong"></a>
<a id="tocScommonresultlong"></a>
<a id="tocscommonresultlong"></a>

```json
{
  "code": 0,
  "data": 0,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|integer(int64)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultAppAuthLoginRespVO">CommonResultAppAuthLoginRespVO</h2>

<a id="schemacommonresultappauthloginrespvo"></a>
<a id="schema_CommonResultAppAuthLoginRespVO"></a>
<a id="tocScommonresultappauthloginrespvo"></a>
<a id="tocscommonresultappauthloginrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "userId": 1024,
    "accessToken": "happy",
    "refreshToken": "nice",
    "expiresTime": "string",
    "openid": "qq768"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppAuthLoginRespVO](#schemaappauthloginrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AppClassesSaveReqVO">AppClassesSaveReqVO</h2>

<a id="schemaappclassessavereqvo"></a>
<a id="schema_AppClassesSaveReqVO"></a>
<a id="tocSappclassessavereqvo"></a>
<a id="tocsappclassessavereqvo"></a>

```json
{
  "id": 1942,
  "className": "张三",
  "teacherId": 13601
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|className|string|true|none||班级名称|
|teacherId|integer(int64)|true|none||所属教师 ID（逻辑关联 teacher 表）|

<h2 id="tocS_AppStudentsSaveReqVO">AppStudentsSaveReqVO</h2>

<a id="schemaappstudentssavereqvo"></a>
<a id="schema_AppStudentsSaveReqVO"></a>
<a id="tocSappstudentssavereqvo"></a>
<a id="tocsappstudentssavereqvo"></a>

```json
{
  "id": 14598,
  "name": "张三",
  "studentNumber": "string",
  "classId": 23831
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|name|string|true|none||学生姓名|
|studentNumber|string|true|none||学生学号|
|classId|integer(int64)|true|none||所属班级 ID，逻辑关联 quick_marker_classes.id|

<h2 id="tocS_FilePresignedUrlRespVO">FilePresignedUrlRespVO</h2>

<a id="schemafilepresignedurlrespvo"></a>
<a id="schema_FilePresignedUrlRespVO"></a>
<a id="tocSfilepresignedurlrespvo"></a>
<a id="tocsfilepresignedurlrespvo"></a>

```json
{
  "configId": 11,
  "uploadUrl": "https://s3.cn-south-1.qiniucs.com/ruoyi-vue-pro/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=3TvrJ70gl2Gt6IBe7_IZT1F6i_k0iMuRtyEv4EyS%2F20240217%2Fcn-south-1%2Fs3%2Faws4_request&X-Amz-Date=20240217T123222Z&X-Amz-Expires=600&X-Amz-SignedHeaders=host&X-Amz-Signature=a29f33770ab79bf523ccd4034d0752ac545f3c2a3b17baa1eb4e280cfdccfda5",
  "url": "https://test.yudao.iocoder.cn/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|configId|integer(int64)|false|none||配置编号|
|uploadUrl|string|false|none||文件上传 URL|
|url|string|false|none||为什么要返回 url 字段？<br /><br />前端上传完文件后，需要使用该 URL 进行访问<br />文件访问 URL|

<h2 id="tocS_AppTeachersSaveReqVO">AppTeachersSaveReqVO</h2>

<a id="schemaappteacherssavereqvo"></a>
<a id="schema_AppTeachersSaveReqVO"></a>
<a id="tocSappteacherssavereqvo"></a>
<a id="tocsappteacherssavereqvo"></a>

```json
{
  "id": 13791,
  "name": "芋艿",
  "memberUserId": 14645,
  "email": "string",
  "avatar": "string",
  "phone": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|name|string|true|none||教师姓名|
|memberUserId|integer(int64)|true|none||会员 ID|
|email|string|false|none||教师邮箱|
|avatar|string|false|none||教师头像|
|phone|string|false|none||手机号|

<h2 id="tocS_AppCorrectionTasksSaveReqVO">AppCorrectionTasksSaveReqVO</h2>

<a id="schemaappcorrectiontaskssavereqvo"></a>
<a id="schema_AppCorrectionTasksSaveReqVO"></a>
<a id="tocSappcorrectiontaskssavereqvo"></a>
<a id="tocsappcorrectiontaskssavereqvo"></a>

```json
{
  "id": 3247,
  "title": "string",
  "teacherId": 7599,
  "classId": 16560,
  "correctionImage": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|title|string|true|none||任务标题|
|teacherId|integer(int64)|true|none||创建该任务的教师 ID，逻辑关联 quick_marker_teachers.id|
|classId|integer(int64)|true|none||所属班级 ID（若有班级表，可逻辑关联）|
|correctionImage|string|false|none||批改任务的图片路径或 URL|

<h2 id="tocS_AppCorrectionTaskItemsSaveReqVO">AppCorrectionTaskItemsSaveReqVO</h2>

<a id="schemaappcorrectiontaskitemssavereqvo"></a>
<a id="schema_AppCorrectionTaskItemsSaveReqVO"></a>
<a id="tocSappcorrectiontaskitemssavereqvo"></a>
<a id="tocsappcorrectiontaskitemssavereqvo"></a>

```json
{
  "id": 31241,
  "taskId": 18815,
  "teacherId": 32399,
  "classId": 27316,
  "images": "string",
  "errorAnalysis": "string",
  "improvementSuggestions": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|taskId|integer(int64)|true|none||所属批改任务 ID，逻辑关联 quick_marker_correction_tasks.id|
|teacherId|integer(int64)|true|none||教师 ID（冗余存储）|
|classId|integer(int64)|true|none||班级 ID（冗余存储）|
|images|string|false|none||作业图片URL集合（JSON数组或字符串）|
|errorAnalysis|string|false|none||错题分析|
|improvementSuggestions|string|false|none||改进意见|

<h2 id="tocS_AppCorrectionTaskItemDetailsSaveReqVO">AppCorrectionTaskItemDetailsSaveReqVO</h2>

<a id="schemaappcorrectiontaskitemdetailssavereqvo"></a>
<a id="schema_AppCorrectionTaskItemDetailsSaveReqVO"></a>
<a id="tocSappcorrectiontaskitemdetailssavereqvo"></a>
<a id="tocsappcorrectiontaskitemdetailssavereqvo"></a>

```json
{
  "id": 5961,
  "itemId": 6269,
  "imageId": 26812,
  "recognizedText": "string",
  "correctText": "string",
  "score": 0,
  "isCorrect": 0,
  "errorAnalysis": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|itemId|integer(int64)|true|none||关联到哪份作业，quick_marker_correction_task_items.id|
|imageId|integer(int64)|false|none||若可溯源到具体哪张图片，可逻辑存放；可不再使用|
|recognizedText|string|false|none||AI 识别到的学生答案|
|correctText|string|false|none||标准答案（或比对后的正确答案）|
|score|number|false|none||该题得分|
|isCorrect|integer|true|none||是否正确(0=错误,1=正确,2=部分正确等)|
|errorAnalysis|string|false|none||错误原因或拼写错误提示|

<h2 id="tocS_AppOrdersSaveReqVO">AppOrdersSaveReqVO</h2>

<a id="schemaapporderssavereqvo"></a>
<a id="schema_AppOrdersSaveReqVO"></a>
<a id="tocSapporderssavereqvo"></a>
<a id="tocsapporderssavereqvo"></a>

```json
{
  "id": 17819,
  "orderNo": "string",
  "memberUserId": 10265,
  "packageId": 20776,
  "price": 15190,
  "originalPrice": 12008,
  "status": 1,
  "paymentStatus": 2,
  "paymentMethod": "string",
  "transactionId": "29022",
  "paymentTime": "string",
  "expireTime": "string",
  "remark": "你说的对"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||订单 ID|
|orderNo|string|true|none||订单编号|
|memberUserId|integer(int64)|true|none||用户 ID，关联 member_user|
|packageId|integer(int64)|true|none||套餐 ID，关联 quick_marker_packages|
|price|number|true|none||支付金额|
|originalPrice|number|true|none||原价|
|status|integer|false|none||订单状态: 0=待支付, 1=已支付, 2=已取消|
|paymentStatus|integer|false|none||支付状态: 0=未支付, 1=支付成功, 2=支付失败, 3=退款中, 4=已退款|
|paymentMethod|string|false|none||支付方式（wechat: 微信, alipay: 支付宝, credit: 余额）|
|transactionId|string|false|none||第三方支付交易号（微信/支付宝等）|
|paymentTime|string|false|none||支付时间|
|expireTime|string|false|none||订单过期时间|
|remark|string|false|none||订单备注|

<h2 id="tocS_AppPackagesSaveReqVO">AppPackagesSaveReqVO</h2>

<a id="schemaapppackagessavereqvo"></a>
<a id="schema_AppPackagesSaveReqVO"></a>
<a id="tocSapppackagessavereqvo"></a>
<a id="tocsapppackagessavereqvo"></a>

```json
{
  "id": 20776,
  "name": "芋艿",
  "description": "随便",
  "category": "string",
  "price": 8081,
  "originalPrice": 5951,
  "correctionCount": 7252,
  "validityDays": 0,
  "imageUrl": "https://www.iocoder.cn",
  "sortOrder": 0,
  "status": 2
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||套餐 ID|
|name|string|true|none||套餐名称|
|description|string|false|none||套餐描述|
|category|string|true|none||套餐类别: experience(体验), standard(标准), advanced(高级), flagship(旗舰)|
|price|number|true|none||套餐价格|
|originalPrice|number|true|none||原价|
|correctionCount|integer|true|none||可批改次数|
|validityDays|integer|true|none||有效期(天)，从购买日期算起|
|imageUrl|string|false|none||套餐封面图片 URL|
|sortOrder|integer|false|none||排序权重(后台管理可调整)|
|status|integer|false|none||状态: 1=上架, 0=下架|

<h2 id="tocS_AppAuthLoginReqVO">AppAuthLoginReqVO</h2>

<a id="schemaappauthloginreqvo"></a>
<a id="schema_AppAuthLoginReqVO"></a>
<a id="tocSappauthloginreqvo"></a>
<a id="tocsappauthloginreqvo"></a>

```json
{
  "mobile": "15601691300",
  "password": "buzhidao",
  "socialType": 10,
  "socialCode": "1024",
  "socialState": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mobile|string|true|none||手机号|
|password|string|true|none||密码|
|socialType|integer|false|none||社交平台的类型，参见 SocialTypeEnum 枚举值|
|socialCode|string|false|none||授权码|
|socialState|string|false|none||state|

<h2 id="tocS_CommonResultFilePresignedUrlRespVO">CommonResultFilePresignedUrlRespVO</h2>

<a id="schemacommonresultfilepresignedurlrespvo"></a>
<a id="schema_CommonResultFilePresignedUrlRespVO"></a>
<a id="tocScommonresultfilepresignedurlrespvo"></a>
<a id="tocscommonresultfilepresignedurlrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "configId": 11,
    "uploadUrl": "https://s3.cn-south-1.qiniucs.com/ruoyi-vue-pro/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=3TvrJ70gl2Gt6IBe7_IZT1F6i_k0iMuRtyEv4EyS%2F20240217%2Fcn-south-1%2Fs3%2Faws4_request&X-Amz-Date=20240217T123222Z&X-Amz-Expires=600&X-Amz-SignedHeaders=host&X-Amz-Signature=a29f33770ab79bf523ccd4034d0752ac545f3c2a3b17baa1eb4e280cfdccfda5",
    "url": "https://test.yudao.iocoder.cn/758d3a5387507358c7236de4c8f96de1c7f5097ff6a7722b34772fb7b76b140f.png"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[FilePresignedUrlRespVO](#schemafilepresignedurlrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultBoolean">CommonResultBoolean</h2>

<a id="schemacommonresultboolean"></a>
<a id="schema_CommonResultBoolean"></a>
<a id="tocScommonresultboolean"></a>
<a id="tocscommonresultboolean"></a>

```json
{
  "code": 0,
  "data": true,
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|boolean|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AppClassesRespVO">AppClassesRespVO</h2>

<a id="schemaappclassesrespvo"></a>
<a id="schema_AppClassesRespVO"></a>
<a id="tocSappclassesrespvo"></a>
<a id="tocsappclassesrespvo"></a>

```json
{
  "id": 1942,
  "className": "张三",
  "teacherId": 13601,
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|className|string|false|none||班级名称|
|teacherId|integer(int64)|false|none||所属教师 ID（逻辑关联 teacher 表）|
|createTime|string|false|none||创建时间|

<h2 id="tocS_AppStudentsRespVO">AppStudentsRespVO</h2>

<a id="schemaappstudentsrespvo"></a>
<a id="schema_AppStudentsRespVO"></a>
<a id="tocSappstudentsrespvo"></a>
<a id="tocsappstudentsrespvo"></a>

```json
{
  "id": 14598,
  "name": "张三",
  "studentNumber": "string",
  "classId": 23831,
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|name|string|false|none||学生姓名|
|studentNumber|string|false|none||学生学号|
|classId|integer(int64)|false|none||所属班级 ID，逻辑关联 quick_marker_classes.id|
|createTime|string|false|none||创建时间|

<h2 id="tocS_AppTeachersRespVO">AppTeachersRespVO</h2>

<a id="schemaappteachersrespvo"></a>
<a id="schema_AppTeachersRespVO"></a>
<a id="tocSappteachersrespvo"></a>
<a id="tocsappteachersrespvo"></a>

```json
{
  "id": 13791,
  "name": "芋艿",
  "memberUserId": 14645,
  "email": "string",
  "avatar": "string",
  "phone": "string",
  "totalCorrectionCount": 14301,
  "remainCorrectionCount": 1477,
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|name|string|false|none||教师姓名|
|memberUserId|integer(int64)|false|none||会员 ID|
|email|string|false|none||教师邮箱|
|avatar|string|false|none||教师头像|
|phone|string|false|none||手机号|
|totalCorrectionCount|integer|false|none||总批改次数|
|remainCorrectionCount|integer|false|none||剩余批改次数|
|createTime|string|false|none||创建时间|

<h2 id="tocS_AppCorrectionTasksRespVO">AppCorrectionTasksRespVO</h2>

<a id="schemaappcorrectiontasksrespvo"></a>
<a id="schema_AppCorrectionTasksRespVO"></a>
<a id="tocSappcorrectiontasksrespvo"></a>
<a id="tocsappcorrectiontasksrespvo"></a>

```json
{
  "id": 3247,
  "title": "string",
  "teacherId": 7599,
  "classId": 16560,
  "description": "你说的对",
  "status": "1",
  "correctionLimit": 0,
  "usedCorrectionCount": 3697,
  "averageAccuracy": 0,
  "maxAccuracy": 0,
  "minAccuracy": 0,
  "createTime": "string",
  "correctionImage": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|title|string|false|none||任务标题|
|teacherId|integer(int64)|false|none||创建该任务的教师 ID，逻辑关联 quick_marker_teachers.id|
|classId|integer(int64)|false|none||所属班级 ID（若有班级表，可逻辑关联）|
|description|string|false|none||任务描述|
|status|string|false|none||任务状态：pending/processing/done等|
|correctionLimit|integer|false|none||本次批改可用次数或需要的次数|
|usedCorrectionCount|integer|false|none||已消耗的批改次数|
|averageAccuracy|number|false|none||平均正确率(0~100)|
|maxAccuracy|number|false|none||最高正确率(0~100)|
|minAccuracy|number|false|none||最低正确率(0~100)|
|createTime|string|false|none||创建时间|
|correctionImage|string|false|none||批改任务的图片路径或 URL|

<h2 id="tocS_AppCorrectionTaskItemsRespVO">AppCorrectionTaskItemsRespVO</h2>

<a id="schemaappcorrectiontaskitemsrespvo"></a>
<a id="schema_AppCorrectionTaskItemsRespVO"></a>
<a id="tocSappcorrectiontaskitemsrespvo"></a>
<a id="tocsappcorrectiontaskitemsrespvo"></a>

```json
{
  "id": 31241,
  "taskId": 18815,
  "teacherId": 32399,
  "classId": 27316,
  "studentId": 14445,
  "status": "1",
  "finalScore": 0,
  "images": "string",
  "correctRate": 0,
  "totalCount": 1854,
  "correctCount": 6858,
  "wrongCount": 11020,
  "errorAnalysis": "string",
  "improvementSuggestions": "string",
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|taskId|integer(int64)|false|none||所属批改任务 ID，逻辑关联 quick_marker_correction_tasks.id|
|teacherId|integer(int64)|false|none||教师 ID（冗余存储）|
|classId|integer(int64)|false|none||班级 ID（冗余存储）|
|studentId|integer(int64)|false|none||学生 ID（逻辑关联到学生表或其他）|
|status|string|false|none||状态：pending/processing/corrected|
|finalScore|number|false|none||AI 批改后该作业的总得分|
|images|string|false|none||作业图片URL集合（JSON数组或字符串）|
|correctRate|number|false|none||正确率(0~100)|
|totalCount|integer|false|none||总题数量|
|correctCount|integer|false|none||正确题数|
|wrongCount|integer|false|none||错误题数|
|errorAnalysis|string|false|none||错题分析|
|improvementSuggestions|string|false|none||改进意见|
|createTime|string|false|none||创建时间|

<h2 id="tocS_AppCorrectionTaskItemDetailsRespVO">AppCorrectionTaskItemDetailsRespVO</h2>

<a id="schemaappcorrectiontaskitemdetailsrespvo"></a>
<a id="schema_AppCorrectionTaskItemDetailsRespVO"></a>
<a id="tocSappcorrectiontaskitemdetailsrespvo"></a>
<a id="tocsappcorrectiontaskitemdetailsrespvo"></a>

```json
{
  "id": 5961,
  "itemId": 6269,
  "imageId": 26812,
  "recognizedText": "string",
  "correctText": "string",
  "score": 0,
  "isCorrect": 0,
  "errorAnalysis": "string",
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||主键 ID|
|itemId|integer(int64)|false|none||关联到哪份作业，quick_marker_correction_task_items.id|
|imageId|integer(int64)|false|none||若可溯源到具体哪张图片，可逻辑存放；可不再使用|
|recognizedText|string|false|none||AI 识别到的学生答案|
|correctText|string|false|none||标准答案（或比对后的正确答案）|
|score|number|false|none||该题得分|
|isCorrect|integer|false|none||是否正确(0=错误,1=正确,2=部分正确等)|
|errorAnalysis|string|false|none||错误原因或拼写错误提示|
|createTime|string|false|none||创建时间|

<h2 id="tocS_AppOrdersRespVO">AppOrdersRespVO</h2>

<a id="schemaappordersrespvo"></a>
<a id="schema_AppOrdersRespVO"></a>
<a id="tocSappordersrespvo"></a>
<a id="tocsappordersrespvo"></a>

```json
{
  "id": 17819,
  "orderNo": "string",
  "memberUserId": 10265,
  "packageId": 20776,
  "price": 15190,
  "originalPrice": 12008,
  "status": 1,
  "paymentStatus": 2,
  "paymentMethod": "string",
  "transactionId": "29022",
  "paymentTime": "string",
  "expireTime": "string",
  "remark": "你说的对",
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||订单 ID|
|orderNo|string|false|none||订单编号|
|memberUserId|integer(int64)|false|none||用户 ID，关联 member_user|
|packageId|integer(int64)|false|none||套餐 ID，关联 quick_marker_packages|
|price|number|false|none||支付金额|
|originalPrice|number|false|none||原价|
|status|integer|false|none||订单状态: 0=待支付, 1=已支付, 2=已取消|
|paymentStatus|integer|false|none||支付状态: 0=未支付, 1=支付成功, 2=支付失败, 3=退款中, 4=已退款|
|paymentMethod|string|false|none||支付方式（wechat: 微信, alipay: 支付宝, credit: 余额）|
|transactionId|string|false|none||第三方支付交易号（微信/支付宝等）|
|paymentTime|string|false|none||支付时间|
|expireTime|string|false|none||订单过期时间|
|remark|string|false|none||订单备注|
|createTime|string|false|none||创建时间|

<h2 id="tocS_AppPackagesRespVO">AppPackagesRespVO</h2>

<a id="schemaapppackagesrespvo"></a>
<a id="schema_AppPackagesRespVO"></a>
<a id="tocSapppackagesrespvo"></a>
<a id="tocsapppackagesrespvo"></a>

```json
{
  "id": 20776,
  "name": "芋艿",
  "description": "随便",
  "category": "string",
  "price": 8081,
  "originalPrice": 5951,
  "correctionCount": 7252,
  "validityDays": 0,
  "imageUrl": "https://www.iocoder.cn",
  "sortOrder": 0,
  "status": 2,
  "createTime": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||套餐 ID|
|name|string|false|none||套餐名称|
|description|string|false|none||套餐描述|
|category|string|false|none||套餐类别: experience(体验), standard(标准), advanced(高级), flagship(旗舰)|
|price|number|false|none||套餐价格|
|originalPrice|number|false|none||原价|
|correctionCount|integer|false|none||可批改次数|
|validityDays|integer|false|none||有效期(天)，从购买日期算起|
|imageUrl|string|false|none||套餐封面图片 URL|
|sortOrder|integer|false|none||排序权重(后台管理可调整)|
|status|integer|false|none||状态: 1=上架, 0=下架|
|createTime|string|false|none||创建时间|

<h2 id="tocS_AppAuthSmsLoginReqVO">AppAuthSmsLoginReqVO</h2>

<a id="schemaappauthsmsloginreqvo"></a>
<a id="schema_AppAuthSmsLoginReqVO"></a>
<a id="tocSappauthsmsloginreqvo"></a>
<a id="tocsappauthsmsloginreqvo"></a>

```json
{
  "mobile": "15601691300",
  "code": "1024",
  "socialType": 10,
  "socialCode": "1024",
  "socialState": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mobile|string|true|none||手机号|
|code|string|true|none||手机验证码|
|socialType|integer|false|none||社交平台的类型，参见 SocialTypeEnum 枚举值|
|socialCode|string|false|none||授权码|
|socialState|string|false|none||state|

<h2 id="tocS_CommonResultAppClassesRespVO">CommonResultAppClassesRespVO</h2>

<a id="schemacommonresultappclassesrespvo"></a>
<a id="schema_CommonResultAppClassesRespVO"></a>
<a id="tocScommonresultappclassesrespvo"></a>
<a id="tocscommonresultappclassesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 1942,
    "className": "张三",
    "teacherId": 13601,
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppClassesRespVO](#schemaappclassesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultAppStudentsRespVO">CommonResultAppStudentsRespVO</h2>

<a id="schemacommonresultappstudentsrespvo"></a>
<a id="schema_CommonResultAppStudentsRespVO"></a>
<a id="tocScommonresultappstudentsrespvo"></a>
<a id="tocscommonresultappstudentsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 14598,
    "name": "张三",
    "studentNumber": "string",
    "classId": 23831,
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppStudentsRespVO](#schemaappstudentsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_FileCreateReqVO">FileCreateReqVO</h2>

<a id="schemafilecreatereqvo"></a>
<a id="schema_FileCreateReqVO"></a>
<a id="tocSfilecreatereqvo"></a>
<a id="tocsfilecreatereqvo"></a>

```json
{
  "configId": 11,
  "path": "yudao.jpg",
  "name": "yudao.jpg",
  "url": "https://www.iocoder.cn/yudao.jpg",
  "type": "application/octet-stream",
  "size": 2048
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|configId|integer(int64)|true|none||文件配置编号|
|path|string|true|none||文件路径|
|name|string|true|none||原文件名|
|url|string|true|none||文件 URL|
|type|string|false|none||文件 MIME 类型|
|size|integer|false|none||文件大小|

<h2 id="tocS_CommonResultAppTeachersRespVO">CommonResultAppTeachersRespVO</h2>

<a id="schemacommonresultappteachersrespvo"></a>
<a id="schema_CommonResultAppTeachersRespVO"></a>
<a id="tocScommonresultappteachersrespvo"></a>
<a id="tocscommonresultappteachersrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 13791,
    "name": "芋艿",
    "memberUserId": 14645,
    "email": "string",
    "avatar": "string",
    "phone": "string",
    "totalCorrectionCount": 14301,
    "remainCorrectionCount": 1477,
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppTeachersRespVO](#schemaappteachersrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultAppCorrectionTasksRespVO">CommonResultAppCorrectionTasksRespVO</h2>

<a id="schemacommonresultappcorrectiontasksrespvo"></a>
<a id="schema_CommonResultAppCorrectionTasksRespVO"></a>
<a id="tocScommonresultappcorrectiontasksrespvo"></a>
<a id="tocscommonresultappcorrectiontasksrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 3247,
    "title": "string",
    "teacherId": 7599,
    "classId": 16560,
    "description": "你说的对",
    "status": "1",
    "correctionLimit": 0,
    "usedCorrectionCount": 3697,
    "averageAccuracy": 0,
    "maxAccuracy": 0,
    "minAccuracy": 0,
    "createTime": "string",
    "correctionImage": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppCorrectionTasksRespVO](#schemaappcorrectiontasksrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultAppCorrectionTaskItemsRespVO">CommonResultAppCorrectionTaskItemsRespVO</h2>

<a id="schemacommonresultappcorrectiontaskitemsrespvo"></a>
<a id="schema_CommonResultAppCorrectionTaskItemsRespVO"></a>
<a id="tocScommonresultappcorrectiontaskitemsrespvo"></a>
<a id="tocscommonresultappcorrectiontaskitemsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 31241,
    "taskId": 18815,
    "teacherId": 32399,
    "classId": 27316,
    "studentId": 14445,
    "status": "1",
    "finalScore": 0,
    "images": "string",
    "correctRate": 0,
    "totalCount": 1854,
    "correctCount": 6858,
    "wrongCount": 11020,
    "errorAnalysis": "string",
    "improvementSuggestions": "string",
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppCorrectionTaskItemsRespVO](#schemaappcorrectiontaskitemsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultAppCorrectionTaskItemDetailsRespVO">CommonResultAppCorrectionTaskItemDetailsRespVO</h2>

<a id="schemacommonresultappcorrectiontaskitemdetailsrespvo"></a>
<a id="schema_CommonResultAppCorrectionTaskItemDetailsRespVO"></a>
<a id="tocScommonresultappcorrectiontaskitemdetailsrespvo"></a>
<a id="tocscommonresultappcorrectiontaskitemdetailsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 5961,
    "itemId": 6269,
    "imageId": 26812,
    "recognizedText": "string",
    "correctText": "string",
    "score": 0,
    "isCorrect": 0,
    "errorAnalysis": "string",
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppCorrectionTaskItemDetailsRespVO](#schemaappcorrectiontaskitemdetailsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultAppOrdersRespVO">CommonResultAppOrdersRespVO</h2>

<a id="schemacommonresultappordersrespvo"></a>
<a id="schema_CommonResultAppOrdersRespVO"></a>
<a id="tocScommonresultappordersrespvo"></a>
<a id="tocscommonresultappordersrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 17819,
    "orderNo": "string",
    "memberUserId": 10265,
    "packageId": 20776,
    "price": 15190,
    "originalPrice": 12008,
    "status": 1,
    "paymentStatus": 2,
    "paymentMethod": "string",
    "transactionId": "29022",
    "paymentTime": "string",
    "expireTime": "string",
    "remark": "你说的对",
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppOrdersRespVO](#schemaappordersrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultAppPackagesRespVO">CommonResultAppPackagesRespVO</h2>

<a id="schemacommonresultapppackagesrespvo"></a>
<a id="schema_CommonResultAppPackagesRespVO"></a>
<a id="tocScommonresultapppackagesrespvo"></a>
<a id="tocscommonresultapppackagesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "id": 20776,
    "name": "芋艿",
    "description": "随便",
    "category": "string",
    "price": 8081,
    "originalPrice": 5951,
    "correctionCount": 7252,
    "validityDays": 0,
    "imageUrl": "https://www.iocoder.cn",
    "sortOrder": 0,
    "status": 2,
    "createTime": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[AppPackagesRespVO](#schemaapppackagesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AppAuthSmsSendReqVO">AppAuthSmsSendReqVO</h2>

<a id="schemaappauthsmssendreqvo"></a>
<a id="schema_AppAuthSmsSendReqVO"></a>
<a id="tocSappauthsmssendreqvo"></a>
<a id="tocsappauthsmssendreqvo"></a>

```json
{
  "mobile": "15601691234",
  "scene": 1
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mobile|string|false|none||手机号|
|scene|integer|true|none||发送场景,对应 SmsSceneEnum 枚举|

<h2 id="tocS_PageResultAppClassesRespVO">PageResultAppClassesRespVO</h2>

<a id="schemapageresultappclassesrespvo"></a>
<a id="schema_PageResultAppClassesRespVO"></a>
<a id="tocSpageresultappclassesrespvo"></a>
<a id="tocspageresultappclassesrespvo"></a>

```json
{
  "list": [
    {
      "id": 1942,
      "className": "张三",
      "teacherId": 13601,
      "createTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppClassesRespVO](#schemaappclassesrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_PageResultAppStudentsRespVO">PageResultAppStudentsRespVO</h2>

<a id="schemapageresultappstudentsrespvo"></a>
<a id="schema_PageResultAppStudentsRespVO"></a>
<a id="tocSpageresultappstudentsrespvo"></a>
<a id="tocspageresultappstudentsrespvo"></a>

```json
{
  "list": [
    {
      "id": 14598,
      "name": "张三",
      "studentNumber": "string",
      "classId": 23831,
      "createTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppStudentsRespVO](#schemaappstudentsrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_PageResultAppTeachersRespVO">PageResultAppTeachersRespVO</h2>

<a id="schemapageresultappteachersrespvo"></a>
<a id="schema_PageResultAppTeachersRespVO"></a>
<a id="tocSpageresultappteachersrespvo"></a>
<a id="tocspageresultappteachersrespvo"></a>

```json
{
  "list": [
    {
      "id": 13791,
      "name": "芋艿",
      "memberUserId": 14645,
      "email": "string",
      "avatar": "string",
      "phone": "string",
      "totalCorrectionCount": 14301,
      "remainCorrectionCount": 1477,
      "createTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppTeachersRespVO](#schemaappteachersrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_PageResultAppCorrectionTasksRespVO">PageResultAppCorrectionTasksRespVO</h2>

<a id="schemapageresultappcorrectiontasksrespvo"></a>
<a id="schema_PageResultAppCorrectionTasksRespVO"></a>
<a id="tocSpageresultappcorrectiontasksrespvo"></a>
<a id="tocspageresultappcorrectiontasksrespvo"></a>

```json
{
  "list": [
    {
      "id": 3247,
      "title": "string",
      "teacherId": 7599,
      "classId": 16560,
      "description": "你说的对",
      "status": "1",
      "correctionLimit": 0,
      "usedCorrectionCount": 3697,
      "averageAccuracy": 0,
      "maxAccuracy": 0,
      "minAccuracy": 0,
      "createTime": "string",
      "correctionImage": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppCorrectionTasksRespVO](#schemaappcorrectiontasksrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_PageResultAppCorrectionTaskItemsRespVO">PageResultAppCorrectionTaskItemsRespVO</h2>

<a id="schemapageresultappcorrectiontaskitemsrespvo"></a>
<a id="schema_PageResultAppCorrectionTaskItemsRespVO"></a>
<a id="tocSpageresultappcorrectiontaskitemsrespvo"></a>
<a id="tocspageresultappcorrectiontaskitemsrespvo"></a>

```json
{
  "list": [
    {
      "id": 31241,
      "taskId": 18815,
      "teacherId": 32399,
      "classId": 27316,
      "studentId": 14445,
      "status": "1",
      "finalScore": 0,
      "images": "string",
      "correctRate": 0,
      "totalCount": 1854,
      "correctCount": 6858,
      "wrongCount": 11020,
      "errorAnalysis": "string",
      "improvementSuggestions": "string",
      "createTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppCorrectionTaskItemsRespVO](#schemaappcorrectiontaskitemsrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_PageResultAppCorrectionTaskItemDetailsRespVO">PageResultAppCorrectionTaskItemDetailsRespVO</h2>

<a id="schemapageresultappcorrectiontaskitemdetailsrespvo"></a>
<a id="schema_PageResultAppCorrectionTaskItemDetailsRespVO"></a>
<a id="tocSpageresultappcorrectiontaskitemdetailsrespvo"></a>
<a id="tocspageresultappcorrectiontaskitemdetailsrespvo"></a>

```json
{
  "list": [
    {
      "id": 5961,
      "itemId": 6269,
      "imageId": 26812,
      "recognizedText": "string",
      "correctText": "string",
      "score": 0,
      "isCorrect": 0,
      "errorAnalysis": "string",
      "createTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppCorrectionTaskItemDetailsRespVO](#schemaappcorrectiontaskitemdetailsrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_PageResultAppOrdersRespVO">PageResultAppOrdersRespVO</h2>

<a id="schemapageresultappordersrespvo"></a>
<a id="schema_PageResultAppOrdersRespVO"></a>
<a id="tocSpageresultappordersrespvo"></a>
<a id="tocspageresultappordersrespvo"></a>

```json
{
  "list": [
    {
      "id": 17819,
      "orderNo": "string",
      "memberUserId": 10265,
      "packageId": 20776,
      "price": 15190,
      "originalPrice": 12008,
      "status": 1,
      "paymentStatus": 2,
      "paymentMethod": "string",
      "transactionId": "29022",
      "paymentTime": "string",
      "expireTime": "string",
      "remark": "你说的对",
      "createTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppOrdersRespVO](#schemaappordersrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_PageResultAppPackagesRespVO">PageResultAppPackagesRespVO</h2>

<a id="schemapageresultapppackagesrespvo"></a>
<a id="schema_PageResultAppPackagesRespVO"></a>
<a id="tocSpageresultapppackagesrespvo"></a>
<a id="tocspageresultapppackagesrespvo"></a>

```json
{
  "list": [
    {
      "id": 20776,
      "name": "芋艿",
      "description": "随便",
      "category": "string",
      "price": 8081,
      "originalPrice": 5951,
      "correctionCount": 7252,
      "validityDays": 0,
      "imageUrl": "https://www.iocoder.cn",
      "sortOrder": 0,
      "status": 2,
      "createTime": "string"
    }
  ],
  "total": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|list|[[AppPackagesRespVO](#schemaapppackagesrespvo)]|false|none||数据|
|total|integer(int64)|false|none||总量|

<h2 id="tocS_AppAuthSmsValidateReqVO">AppAuthSmsValidateReqVO</h2>

<a id="schemaappauthsmsvalidatereqvo"></a>
<a id="schema_AppAuthSmsValidateReqVO"></a>
<a id="tocSappauthsmsvalidatereqvo"></a>
<a id="tocsappauthsmsvalidatereqvo"></a>

```json
{
  "mobile": "15601691234",
  "scene": 1,
  "code": "1024"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|mobile|string|false|none||手机号|
|scene|integer|true|none||发送场景,对应 SmsSceneEnum 枚举|
|code|string|true|none||手机验证码|

<h2 id="tocS_CommonResultPageResultAppClassesRespVO">CommonResultPageResultAppClassesRespVO</h2>

<a id="schemacommonresultpageresultappclassesrespvo"></a>
<a id="schema_CommonResultPageResultAppClassesRespVO"></a>
<a id="tocScommonresultpageresultappclassesrespvo"></a>
<a id="tocscommonresultpageresultappclassesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1942,
        "className": "张三",
        "teacherId": 13601,
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppClassesRespVO](#schemapageresultappclassesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultAppStudentsRespVO">CommonResultPageResultAppStudentsRespVO</h2>

<a id="schemacommonresultpageresultappstudentsrespvo"></a>
<a id="schema_CommonResultPageResultAppStudentsRespVO"></a>
<a id="tocScommonresultpageresultappstudentsrespvo"></a>
<a id="tocscommonresultpageresultappstudentsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 14598,
        "name": "张三",
        "studentNumber": "string",
        "classId": 23831,
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppStudentsRespVO](#schemapageresultappstudentsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultAppTeachersRespVO">CommonResultPageResultAppTeachersRespVO</h2>

<a id="schemacommonresultpageresultappteachersrespvo"></a>
<a id="schema_CommonResultPageResultAppTeachersRespVO"></a>
<a id="tocScommonresultpageresultappteachersrespvo"></a>
<a id="tocscommonresultpageresultappteachersrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 13791,
        "name": "芋艿",
        "memberUserId": 14645,
        "email": "string",
        "avatar": "string",
        "phone": "string",
        "totalCorrectionCount": 14301,
        "remainCorrectionCount": 1477,
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppTeachersRespVO](#schemapageresultappteachersrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultAppCorrectionTasksRespVO">CommonResultPageResultAppCorrectionTasksRespVO</h2>

<a id="schemacommonresultpageresultappcorrectiontasksrespvo"></a>
<a id="schema_CommonResultPageResultAppCorrectionTasksRespVO"></a>
<a id="tocScommonresultpageresultappcorrectiontasksrespvo"></a>
<a id="tocscommonresultpageresultappcorrectiontasksrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 3247,
        "title": "string",
        "teacherId": 7599,
        "classId": 16560,
        "description": "你说的对",
        "status": "1",
        "correctionLimit": 0,
        "usedCorrectionCount": 3697,
        "averageAccuracy": 0,
        "maxAccuracy": 0,
        "minAccuracy": 0,
        "createTime": "string",
        "correctionImage": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppCorrectionTasksRespVO](#schemapageresultappcorrectiontasksrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultAppCorrectionTaskItemsRespVO">CommonResultPageResultAppCorrectionTaskItemsRespVO</h2>

<a id="schemacommonresultpageresultappcorrectiontaskitemsrespvo"></a>
<a id="schema_CommonResultPageResultAppCorrectionTaskItemsRespVO"></a>
<a id="tocScommonresultpageresultappcorrectiontaskitemsrespvo"></a>
<a id="tocscommonresultpageresultappcorrectiontaskitemsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 31241,
        "taskId": 18815,
        "teacherId": 32399,
        "classId": 27316,
        "studentId": 14445,
        "status": "1",
        "finalScore": 0,
        "images": "string",
        "correctRate": 0,
        "totalCount": 1854,
        "correctCount": 6858,
        "wrongCount": 11020,
        "errorAnalysis": "string",
        "improvementSuggestions": "string",
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppCorrectionTaskItemsRespVO](#schemapageresultappcorrectiontaskitemsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultAppCorrectionTaskItemDetailsRespVO">CommonResultPageResultAppCorrectionTaskItemDetailsRespVO</h2>

<a id="schemacommonresultpageresultappcorrectiontaskitemdetailsrespvo"></a>
<a id="schema_CommonResultPageResultAppCorrectionTaskItemDetailsRespVO"></a>
<a id="tocScommonresultpageresultappcorrectiontaskitemdetailsrespvo"></a>
<a id="tocscommonresultpageresultappcorrectiontaskitemdetailsrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 5961,
        "itemId": 6269,
        "imageId": 26812,
        "recognizedText": "string",
        "correctText": "string",
        "score": 0,
        "isCorrect": 0,
        "errorAnalysis": "string",
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppCorrectionTaskItemDetailsRespVO](#schemapageresultappcorrectiontaskitemdetailsrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultAppOrdersRespVO">CommonResultPageResultAppOrdersRespVO</h2>

<a id="schemacommonresultpageresultappordersrespvo"></a>
<a id="schema_CommonResultPageResultAppOrdersRespVO"></a>
<a id="tocScommonresultpageresultappordersrespvo"></a>
<a id="tocscommonresultpageresultappordersrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 17819,
        "orderNo": "string",
        "memberUserId": 10265,
        "packageId": 20776,
        "price": 15190,
        "originalPrice": 12008,
        "status": 1,
        "paymentStatus": 2,
        "paymentMethod": "string",
        "transactionId": "29022",
        "paymentTime": "string",
        "expireTime": "string",
        "remark": "你说的对",
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppOrdersRespVO](#schemapageresultappordersrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_CommonResultPageResultAppPackagesRespVO">CommonResultPageResultAppPackagesRespVO</h2>

<a id="schemacommonresultpageresultapppackagesrespvo"></a>
<a id="schema_CommonResultPageResultAppPackagesRespVO"></a>
<a id="tocScommonresultpageresultapppackagesrespvo"></a>
<a id="tocscommonresultpageresultapppackagesrespvo"></a>

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 20776,
        "name": "芋艿",
        "description": "随便",
        "category": "string",
        "price": 8081,
        "originalPrice": 5951,
        "correctionCount": 7252,
        "validityDays": 0,
        "imageUrl": "https://www.iocoder.cn",
        "sortOrder": 0,
        "status": 2,
        "createTime": "string"
      }
    ],
    "total": 0
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[PageResultAppPackagesRespVO](#schemapageresultapppackagesrespvo)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

<h2 id="tocS_AppAuthSocialLoginReqVO">AppAuthSocialLoginReqVO</h2>

<a id="schemaappauthsocialloginreqvo"></a>
<a id="schema_AppAuthSocialLoginReqVO"></a>
<a id="tocSappauthsocialloginreqvo"></a>
<a id="tocsappauthsocialloginreqvo"></a>

```json
{
  "type": 10,
  "code": "1024",
  "state": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|type|integer|true|none||社交平台的类型，参见 SocialTypeEnum 枚举值|
|code|string|true|none||授权码|
|state|string|true|none||state|

<h2 id="tocS_AppAuthWeixinMiniAppLoginReqVO">AppAuthWeixinMiniAppLoginReqVO</h2>

<a id="schemaappauthweixinminiapploginreqvo"></a>
<a id="schema_AppAuthWeixinMiniAppLoginReqVO"></a>
<a id="tocSappauthweixinminiapploginreqvo"></a>
<a id="tocsappauthweixinminiapploginreqvo"></a>

```json
{
  "phoneCode": "hello",
  "loginCode": "word",
  "state": "9b2ffbc1-7425-4155-9894-9d5c08541d62"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|phoneCode|string|true|none||手机 code，小程序通过 wx.getPhoneNumber 方法获得|
|loginCode|string|true|none||登录 code，小程序通过 wx.login 方法获得|
|state|string|true|none||state|

<h2 id="tocS_SocialWxJsapiSignatureRespDTO">SocialWxJsapiSignatureRespDTO</h2>

<a id="schemasocialwxjsapisignaturerespdto"></a>
<a id="schema_SocialWxJsapiSignatureRespDTO"></a>
<a id="tocSsocialwxjsapisignaturerespdto"></a>
<a id="tocssocialwxjsapisignaturerespdto"></a>

```json
{
  "appId": "string",
  "nonceStr": "string",
  "timestamp": 0,
  "url": "string",
  "signature": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|appId|string|false|none||微信公众号的 appId|
|nonceStr|string|false|none||匿名串|
|timestamp|integer(int64)|false|none||时间戳|
|url|string|false|none||URL|
|signature|string|false|none||签名|

<h2 id="tocS_CommonResultSocialWxJsapiSignatureRespDTO">CommonResultSocialWxJsapiSignatureRespDTO</h2>

<a id="schemacommonresultsocialwxjsapisignaturerespdto"></a>
<a id="schema_CommonResultSocialWxJsapiSignatureRespDTO"></a>
<a id="tocScommonresultsocialwxjsapisignaturerespdto"></a>
<a id="tocscommonresultsocialwxjsapisignaturerespdto"></a>

```json
{
  "code": 0,
  "data": {
    "appId": "string",
    "nonceStr": "string",
    "timestamp": 0,
    "url": "string",
    "signature": "string"
  },
  "msg": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||错误码|
|data|[SocialWxJsapiSignatureRespDTO](#schemasocialwxjsapisignaturerespdto)|false|none||返回数据|
|msg|string|false|none||错误提示，用户可阅读|

