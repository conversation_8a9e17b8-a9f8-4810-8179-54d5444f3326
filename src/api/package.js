import request from '@/libs/http'

/**
 * 获取套餐列表
 */
export function getPackageList(params) {
  return request({
    url: '/quick-marker/packages/page',
    method: 'GET',
    params
  })
}

/**
 * 获取套餐详情
 */
export function getPackageInfo(id) {
  return request({
    url: '/quick-marker/packages/get',
    method: 'GET',
    params: { id }
  })
}

/**
 * 创建套餐
 */
export function createPackage(data) {
  return request({
    url: '/quick-marker/packages/create',
    method: 'POST',
    data
  })
}

/**
 * 更新套餐
 */
export function updatePackage(data) {
  return request({
    url: '/quick-marker/packages/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除套餐
 */
export function deletePackage(id) {
  return request({
    url: '/quick-marker/packages/delete',
    method: 'DELETE',
    params: { id }
  })
} 