import request from '@/libs/http'

/**
 * 获取微信登录code
 */
function getWxLoginCode() {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success(res) {
        resolve(res.code)
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

/**
 * 微信小程序一键登录
 * @param {string} phoneCode 手机号获取凭证
 */
export async function wxMiniAppLogin(phoneCode) {
  const loginCode = await getWxLoginCode()

  return request({
    url: '/member/auth/weixin-mini-app-login',
    method: 'POST',
    custom: {
      ignore: true
    },
    data: {
      phoneCode,
      loginCode,
      state: '100' // 根据实际需求传入
    }
  })
}

/**
 * 退出登录
 */
export function logout() {
  return request({
    url: '/member/auth/logout',
    method: 'POST'
  })
}

/**
 * 刷新访问令牌
 * @param {string} refreshToken 刷新令牌
 */
export function refreshAccessToken(refreshToken) {
  return request({
    url: '/member/auth/refresh-token/quick-marker',
    method: 'POST',
    custom: {
        ignore: true
    },
    data: {
      refreshToken
    }
  })
}

/**
 * 微信小程序简单登录
 * @param {string} state 登录状态标识
 */
export async function wxMiniAppLoginSimple(state='miniapp') {
  const loginCode = await getWxLoginCode()

  return request({
    url: '/member/auth/weixin-mini-app-login-simple',
    method: 'POST',
    custom: {
      ignore: true
    },
    data: {
      loginCode,
      state
    }
  })
}

/**
 * 绑定微信小程序手机号
 * @param {string} phoneCode 手机号获取凭证
 */
export function bindWxMiniAppPhone(phoneCode) {
  return request({
    url: '/member/auth/weixin-mini-app-phone',
    method: 'POST',
    data: {
      phoneCode
    }
  })
}
