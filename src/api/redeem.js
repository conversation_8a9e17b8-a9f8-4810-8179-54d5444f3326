import request from '@/libs/http'

/**
 * 兑换码兑换
 */
export function redeemCode(data) {
  return request({
    url: '/quick-marker/redeem/code',
    method: 'POST',
    data
  })
}

/**
 * 获取兑换记录列表
 */
export function getRedeemRecords(params) {
  return request({
    url: '/quick-marker/redeem/records',
    method: 'GET',
    params
  })
}

/**
 * 获取兑换记录详情
 */
export function getRedeemRecordDetail(id) {
  return request({
    url: '/quick-marker/redeem/record/detail',
    method: 'GET',
    params: { id }
  })
}

/**
 * 使用兑换码
 * @param {Object} data - 包含兑换码的对象
 * @param {string} data.code - 兑换码
 */
export function useRedeemCode(data) {
  return request({
    url: '/quick-marker/redeem-code/use',
    method: 'POST',
    data
  })
}

/**
 * 获取我的兑换码记录
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 */
export function getMyRedeemCodeRecords(params) {
  return request({
    url: '/quick-marker/redeem-code/my-records',
    method: 'GET',
    params
  })
}
