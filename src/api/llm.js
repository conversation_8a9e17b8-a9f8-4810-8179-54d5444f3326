import request from '@/libs/http'

/**
 * 调用大模型进行批改分析
 * @param {string} answerImageUrl 参考答案图片URL
 * @param {string} paperImageUrl 学生试卷图片URL
 */
export function correctByLLM(answerImageUrl, paperImageUrl) {
  return request({
    url: '/quick-marker/llm/correct',
    method: 'POST',
    data: {
      answerImageUrl,
      paperImageUrl
    }
  })
}

/**
 * 直接调用豆包大模型API进行批改分析
 * @param {string} answerImageUrl 参考答案图片URL
 * @param {string} paperImageUrl 学生试卷图片URL
 */
export async function correctByDoubao(answerImageUrl, paperImageUrl) {
  // 构建prompt
  const prompt = `请你作为一名英语老师，对比分析参考答案和学生试卷，进行详细的批改。

## 步骤一：提取学生姓名
从学生答题卡图片上提取学生姓名，如果识别不出来，设置为"体验学生"即可。

## 步骤二：提取学生答题卡内容
从学生答题卡图片中提取题目和学生答案，整理成结构化数据：

提取要求：
1. 剔除说明部分，只提取题目和答案
2. 不要将序号识别为答案的一部分
3. 如果图上有序号，使用图上的序号；如果没有，按顺序设置序号
4. 原封不动地提取，不要翻译或创作
5. 学生没写答案，为空即可
6. 忽略学生答案中划掉或涂抹的内容

## 步骤三：批改学生作业
将提取的学生答题卡数据和第一张参考答案图片进行对比批改：

批改规则：
1. 非常重要：如果参考答案和学生答案语种不一致，直接判断为错误
2. 如果参考答案是中文，学生答案也是中文，核心意思正确即可，用词不必完全一致
3. 忽略大小写差异（如果不影响正确性）
4. 忽略标点符号的差异
5. 学生未填写答案或答案为空，判定为错误

## 输出格式
请严格按照以下JSON格式返回结果，确保可以被JSON.parse解析：

{
  "student": "学生姓名或体验学生",
  "correctRate": 85,
  "correctCount": 17,
  "wrongCount": 3,
  "errorAnalysis": "整体性错误分析，分析主要错误类型和原因",
  "improvementSuggestions": "针对性的学习建议和改进方法", 
  "answerList": [
    {
      "no": "序号",
      "question": "题目内容",
      "standardAnswer": "参考答案",
      "studentAnswer": "学生答案",
      "analysis": "解析正确或错误的原因",
      "isCorrect": true
    }
  ]
}

请仔细分析两张图片：
- 第一张图片：参考答案
- 第二张图片：学生答题卡

确保返回的JSON格式完全正确，可以直接解析使用。`

  try {
    // 调用豆包大模型API
    const response = await uni.request({
      url: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getArKApiKey()}`
      },
      data: {
        model: 'doubao-1.5-vision-pro-250328',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: {
                  url: answerImageUrl
                }
              },
              {
                type: 'image_url', 
                image_url: {
                  url: paperImageUrl
                }
              },
              {
                type: 'text',
                text: prompt
              }
            ]
          }
        ],
        temperature: 0.1,
        max_tokens: 2000
      }
    })

    if (response.statusCode !== 200) {
      throw new Error(`API调用失败: ${response.statusCode}`)
    }

    const result = response.data
    if (!result.choices || !result.choices[0]) {
      throw new Error('API返回数据格式错误')
    }

    // 解析大模型返回的JSON结果
    const content = result.choices[0].message.content
    
    // 提取JSON部分（可能包含其他文本）
    let jsonStr = content
    const jsonMatch = content.match(/\{[\s\S]*\}/)
    if (jsonMatch) {
      jsonStr = jsonMatch[0]
    }

    const analysisResult = JSON.parse(jsonStr)
    
    // 验证返回数据结构
    if (!analysisResult.correctRate || !analysisResult.answerList) {
      throw new Error('大模型返回的数据结构不完整')
    }

    return {
      code: 200,
      data: analysisResult,
      message: '批改成功'
    }
  } catch (error) {
    console.error('大模型调用失败:', error)
    throw error
  }
}

/**
 * 获取ARK API Key
 */
function getArKApiKey() {
  return '51961100-a631-43a6-9085-38ba70729beb'
}

/**
 * 设置ARK API Key
 * @param {string} apiKey 
 */
export function setArKApiKey(apiKey) {
  uni.setStorageSync('ARK_API_KEY', apiKey)
}
