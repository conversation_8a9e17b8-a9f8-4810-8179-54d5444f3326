import request from '@/libs/http'
import storage from '@/libs/storage'

/**
 * 获取当前教师信息
 */
export async function getCurrentTeacher(token) {
  const res = await request({
    url: '/quick-marker/teachers/current',
    method: 'GET',
    header: {
        Authorization: token
    },
    custom: {
        isUserInfo: true
    },
  })

  // 将用户信息存储到 storage 中
  storage.set('userInfo', res.data)
  return res
}

/**
 * 获取教师详情
 */
export function getTeacherInfo(id) {
  return request({
    url: '/quick-marker/teachers/get',
    method: 'GET',
    params: { id }
  })
}

/**
 * 获取教师列表
 */
export function getTeacherList(params) {
  return request({
    url: '/quick-marker/teachers/page',
    method: 'GET',
    params
  })
}

/**
 * 创建教师
 */
export function createTeacher(data) {
  return request({
    url: '/quick-marker/teachers/create',
    method: 'POST',
    data
  })
}

/**
 * 更新教师信息
 */
export function updateTeacher(data) {
  return request({
    url: '/quick-marker/teachers/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除教师
 */
export function deleteTeacher(id) {
  return request({
    url: '/quick-marker/teachers/delete',
    method: 'DELETE',
    params: { id }
  })
}
