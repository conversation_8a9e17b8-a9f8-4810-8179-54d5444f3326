// API 配置
import OpenAIClient from './openai-http-client';

const CONFIG = {
    MODEL: {
        'qwen2.5-vl-72b-instruct': 'qwen2.5-vl-72b-instruct',
        'qwen2.5-vl-7b-instruct': 'qwen2.5-vl-7b-instruct',
        'qwen2.5-vl-3b-instruct': 'qwen2.5-vl-3b-instruct',
        'qwen-vl-max': 'qwen-vl-max',
    },
    PROMPTS: {
        EXTRACT_QUESTION: '图片是默写作业的图片，有可能只有答案，有可能是问题答案对照，请提取内容，返回 json ,示例结构{"result":[{"question":"", "answer": ""}]}，注意不要把序号识别为问题或答案',
        ANALYSIS: '如图是学生的默写作业的图片，有可能只有答案，有可能是问题答案对照，作业上可能有学生的姓名，没有的话，学生姓名为空，请帮忙批改，输出 json 格式，示例结构{"student":"","result":"[{"question":"", "answer": "","analysis": "正确","isCorrect": true}]"}，注意不要把序号识别为问题或答案，注意任何情况下最终输出的内容只能是json，不要补充额外的东西',
        EXTRACT_STUDENT_LIST: '如图是学生名称的图片，有可能只有学生姓名，有可能姓名和学号都有，帮忙提取，输出 json 格式，示例结构{"result":[{"name":"", "no": ""}]}，注意不要把序号识别为问题或答案，注意任何情况下最终输出的内容只能是json，不要补充额外的东西',
        EXTRACT_ESSAY: '图片是英语作文的图片，请识别并提取作文内容，返回 json 格式，示例结构{"content":"作文内容","title":"作文标题（如果有）","wordCount":字数}，注意任何情况下最终输出的内容只能是json，不要补充额外的东西',
        CORRECT_ESSAY: '请对以下英语作文进行批改，从内容、语法、词汇、结构四个维度进行评分和点评，返回 json 格式，示例结构{"totalScore":85,"contentScore":20,"grammarScore":22,"vocabularyScore":20,"structureScore":23,"comments":"整体评价","suggestions":"改进建议","errors":[{"type":"语法错误","original":"原文","corrected":"修正","explanation":"解释"}]}，注意任何情况下最终输出的内容只能是json，不要补充额外的东西'
    }
};

// 创建 OpenAI 客户端实例
const client = new OpenAIClient({
    apiKey: 'sk-213c8dc026f749e892f8655e266cb09f',
    baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    timeout: 60 * 1000,
    maxRetries: 2
});

// 原试题提取
async function extractQuestion(imageUrl) {
    return client.recognizeImage(imageUrl, CONFIG.PROMPTS.EXTRACT_QUESTION, CONFIG.MODEL['qwen2.5-vl-72b-instruct']);
}

// 批改默写
async function analysis(imageUrl) {
    return client.recognizeImage(imageUrl, CONFIG.PROMPTS.ANALYSIS, CONFIG.MODEL['qwen2.5-vl-7b-instruct']);
}

// 提取学生列表
async function extractStudentList(imageUrl) {
    return client.recognizeImage(imageUrl, CONFIG.PROMPTS.EXTRACT_STUDENT_LIST, CONFIG.MODEL['qwen-vl-max']);
}

// 提取作文内容
async function extractEssay(imageUrl) {
    return client.recognizeImage(imageUrl, CONFIG.PROMPTS.EXTRACT_ESSAY, CONFIG.MODEL['qwen2.5-vl-72b-instruct']);
}

// 批改作文
async function correctEssay(essayContent) {
    return client.chat(CONFIG.PROMPTS.CORRECT_ESSAY + '\n\n作文内容：\n' + essayContent, CONFIG.MODEL['qwen2.5-vl-72b-instruct']);
}

export { analysis, extractQuestion, extractStudentList, extractEssay, correctEssay };
