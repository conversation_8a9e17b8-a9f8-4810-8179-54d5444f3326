// API 配置
import OpenAIClient from './openai-http-client';

const CONFIG = {
    MODEL: {
        VISION: 'Doubao-1.5-lite-32k'
    },
    PROMPTS: {
        EXTRACT_QUESTION: '图片是默写作业的图片，有可能只有答案，有可能是问题答案对照，请提取内容，返回 json ,示例结构{"result":[{"question":"", "answer": ""}]}，注意不要把序号识别为问题或答案',
        ANALYSIS: '如图是学生的默写作业的图片，有可能只有答案，有可能是问题答案对照，作业上可能有学生的姓名，没有的话，学生姓名为空，请帮忙批改，输出 json 格式，示例结构{"student":"","result":"[{"question":"", "answer": "","analysis": "正确","isCorrect": true}]"}，注意不要把序号识别为问题或答案，注意任何情况下最终输出的内容只能是json，不要补充额外的东西',
        EXTRACT_STUDENT_LIST: '如图是学生名称的图片，有可能只有学生姓名，有可能姓名和学号都有，帮忙提取，输出 json 格式，示例结构{"result":[{"name":"", "no": ""}]}，注意不要把序号识别为问题或答案，注意任何情况下最终输出的内容只能是json，不要补充额外的东西'
    }
};

// 创建 OpenAI 客户端实例
const client = new OpenAIClient({
    apiKey: 'f3974aa2-199f-4eb1-9805-a233cee5f209',
    baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
    timeout: 60 * 1000,
    maxRetries: 2
});

// 原试题提取
async function extractQuestion(imageUrl) {
    return client.recognizeImage(imageUrl, CONFIG.PROMPTS.EXTRACT_QUESTION, CONFIG.MODEL.VISION);
}

// 批改默写
async function analysis(imageUrl) {
    return client.recognizeImage(imageUrl, CONFIG.PROMPTS.ANALYSIS, CONFIG.MODEL.VISION);
}

// 提取学生列表
async function extractStudentList(imageUrl) {
    return client.recognizeImage(imageUrl, CONFIG.PROMPTS.EXTRACT_STUDENT_LIST, CONFIG.MODEL.VISION);
}

export { analysis, extractQuestion, extractStudentList };
