import { ref } from 'vue'

const ARK_API_URL = 'https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions'
const ARK_MODEL = 'bot-20250318215815-bf75k'

export const useArkChat = () => {
  const loading = ref(false)
  const error = ref(null)

  const chat = async (messages) => {
    try {
      loading.value = true
      error.value = null

      const response = await uni.request({
        url: ARK_API_URL,
        method: 'POST',
        header: {
          'Authorization': `Bearer f3974aa2-199f-4eb1-9805-a233cee5f209`,
          'Content-Type': 'application/json'
        },
        data: {
            stream: false,
          model: ARK_MODEL,
          messages
        }
      })

      if (response.statusCode !== 200) {
        throw new Error(`API 请求失败: ${response.statusCode}`)
      }

      console.log(response.data, 'testExcel')

      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    chat,
    loading,
    error
  }
}
