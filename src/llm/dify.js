const config = {
    apiKey: 'app-tv0UWVhFZ1BNIK3x5GJSBZnB',
    baseURL: 'http://dify-dev.erlitech.com:38080/v1'
};

class DifyClient {
  constructor() {
    this.apiKey = config.apiKey
    this.baseURL = config.baseURL
    this.timeout = 120 * 1000
    this.maxRetries = 3
  }

  async chat(params) {
    const { inputs, user } = params
    const response = await this._request('/workflows/run', {
      method: 'POST',
      data: {
        inputs,
        response_mode: 'blocking',
        user
      }
    })
    console.log('response', response)
    return response.data.outputs?.text || ''
  }

  async feedbackMessage(messageId, rating, user) {
    return this._request(`/messages/${messageId}/feedbacks`, {
      method: 'POST',
      data: {
        rating,
        user
      }
    })
  }

  async _request(path, options = {}) {
    const url = `${this.baseURL}${path}`;
    let retries = 0;

    const makeRequest = () => {
      return new Promise((resolve, reject) => {
        uni.request({
          url,
          method: options.method || 'GET',
          data: options.data,
          header: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: this.timeout,
          success: (response) => {
            if (response.statusCode >= 200 && response.statusCode < 300) {
              resolve(response.data);
            } else {
              reject(this._handleError(response));
            }
          },
          fail: (error) => {
            reject(this._handleError(error));
          }
        });
      });
    };

    while (retries < this.maxRetries) {
      try {
        return await makeRequest();
      } catch (error) {
        if (this._shouldRetry(error) && retries < this.maxRetries - 1) {
          retries++;
          await this._wait(this._getRetryDelay(retries));
          continue;
        }
        throw error;
      }
    }
  }

  _handleError(response) {
    const error = new Error();
    if (response.data?.error) {
      error.message = response.data.error.message;
      error.code = response.data.error.code;
      error.type = response.data.error.type;
    } else {
      error.message = response.errMsg || '请求失败';
      error.status = response.statusCode;
    }
    return error;
  }

  _shouldRetry(error) {
    const retryableStatusCodes = [429, 500, 502, 503, 504];
    return retryableStatusCodes.includes(error.status) || error.message.includes('timeout');
  }

  _getRetryDelay(retryCount) {
    return Math.min(1000 * Math.pow(2, retryCount), 10000);
  }

  _wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default DifyClient