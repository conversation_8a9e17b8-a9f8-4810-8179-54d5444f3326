/**
 * 使用 uni.request 运行 Coze 工作流
 * @param {Object} parameters - 工作流参数
 * @param {string} workflowId - 工作流ID，默认为空对象
 * @returns {Promise<Object>} - 返回工作流运行结果
 */
export function runCozeWorkflow(parameters = {}, workflowId = '') {
    return new Promise((resolve, reject) => {
        uni.request({
            url: 'https://api.coze.cn/v1/workflow/run',
            method: 'POST',
            header: {
                'Authorization': 'Bearer pat_YaJUz8by4Ef4HMMf01XGVuQq8QJpK8B6HcSySbxQNjnICkGVWV181HcUA7PBHUeu',
                'Content-Type': 'application/json'
            },
            data: {
                workflow_id: workflowId,
                parameters: parameters
            },
            success: (response) => {
                if (response.statusCode === 200) {
                    resolve(response.data);
                } else {
                    reject(new Error(`请求失败：状态码 ${response.statusCode}`));
                }
            },
            fail: (error) => {
                reject(new Error(error.errMsg || '请求失败'));
            }
        });
    });
}



/**
 * 使用 runCozeWorkflow 解析答案
 * @param {string} fileId - 文件ID
 * @param {string} taskType - 任务类型
 * @returns {Promise<Object>} - 返回解析结果
 */
export async function parseAnswer({imageUrl, taskType}) {
    const parameters = {
        imageUrl,
        taskType,
    };

    return await runCozeWorkflow(parameters, '7502450133759901715');
}

/**
 * 使用 Coze 工作流解析Excel中的学生列表
 * @param {Object} params - 参数对象
 * @param {string} params.excelUrl - Excel文件URL
 * @returns {Promise<Array>} - 返回学生列表数组
 */
export async function parseStudentList({excelUrl}) {
    const parameters = {
        "excelUrl": excelUrl
    };

    // 调用工作流
    const response = await runCozeWorkflow(parameters, '7502450921772171303');

    // 检查响应是否成功
    if (response.code !== 0) {
        console.error('工作流运行失败:', response);
        throw new Error(`工作流运行失败: ${response.msg}`);
    }

    // 如果没有数据，返回空数组
    if (!response.data) {
        return [];
    }

    // 先将字符串解析为JSON对象
    const parsedData = JSON.parse(response.data);

    // 再解析output字段中的JSON字符串
    if (parsedData.output) {
        return JSON.parse(parsedData.output);
    }

    return [];
}
