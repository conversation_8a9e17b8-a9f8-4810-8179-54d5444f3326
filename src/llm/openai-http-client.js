// OpenAI HTTP 客户端封装

class OpenAIClient {
    constructor(config = {}) {
        this.apiKey = config.apiKey;
        this.baseURL = config.baseURL || 'https://api.openai.com/v1';
        this.timeout = config.timeout || 60000; // 默认超时时间 60s
        this.maxRetries = config.maxRetries || 3; // 默认最大重试次数
    }

    async createChatCompletion(params) {
        return this._request('/chat/completions', {
            method: 'POST',
            data: params
        });
    }

    async recognizeImage(imageUrl, prompt, model) {
        try {
            const response = await this.createChatCompletion({
                model,
                messages: [{
                    role: "user",
                    content: [
                        {type: "text", text: prompt},
                        {type: "image_url", image_url: {"url": imageUrl}}
                    ]
                }],
                response_format: { "type": "json_object" }
            });
            if (response.choices && response.choices.length > 0) {
                return JSON.parse(response.choices[0].message.content);
            } else {
                console.error('No valid response from API:', response);
                return null;
            }
        } catch (err) {
            console.error('Error in image recognition:', err);
            return null;
        }
    }

    async createCompletion(params) {
        return this._request('/completions', {
            method: 'POST',
            data: params
        });
    }

    async createEmbedding(params) {
        return this._request('/embeddings', {
            method: 'POST',
            data: params
        });
    }

    async _request(path, options = {}) {
        const url = `${this.baseURL}${path}`;
        let retries = 0;

        const makeRequest = () => {
            return new Promise((resolve, reject) => {
                uni.request({
                    url,
                    method: options.method || 'GET',
                    data: options.data,
                    header: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: this.timeout,
                    success: (response) => {
                        if (response.statusCode >= 200 && response.statusCode < 300) {
                            resolve(response.data);
                        } else {
                            reject(this._handleError(response));
                        }
                    },
                    fail: (error) => {
                        reject(this._handleError(error));
                    }
                });
            });
        };

        while (retries < this.maxRetries) {
            try {
                return await makeRequest();
            } catch (error) {
                if (this._shouldRetry(error) && retries < this.maxRetries - 1) {
                    retries++;
                    await this._wait(this._getRetryDelay(retries));
                    continue;
                }
                throw error;
            }
        }
    }

    _handleError(response) {
        const error = new Error();
        if (response.data?.error) {
            error.message = response.data.error.message;
            error.code = response.data.error.code;
            error.type = response.data.error.type;
        } else {
            error.message = response.errMsg || '请求失败';
            error.status = response.statusCode;
        }
        return error;
    }

    _shouldRetry(error) {
        // 判断是否需要重试的错误
        const retryableStatusCodes = [429, 500, 502, 503, 504];
        return retryableStatusCodes.includes(error.status) || error.message.includes('timeout');
    }

    _getRetryDelay(retryCount) {
        // 指数退避策略
        return Math.min(1000 * Math.pow(2, retryCount), 10000);
    }

    _wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

export default OpenAIClient;
